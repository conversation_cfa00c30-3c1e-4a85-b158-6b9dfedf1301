package com.workplat.accept.business.allInOne;

import cn.hutool.http.Header;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.vo.CommonUserVO;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.common.core.exception.AuthenticationException;
import com.workplat.gss.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AioServiceHelper {

    /**
     * 获取一网通办用户信息
     * @param accessToken
     * @return
     */
    public CommonUserVO getCommonUserInfo(String accessToken) {
        try {
            String url = "/allinone-api/api/ucenter/user/get";
            String responseString =
                    GatewayUtils.executeGetRequest(url,
                            ImmutableMap.of(Header.AUTHORIZATION.getValue(), accessToken),
                            null, false, false);
            JSONObject jsonObject = JSONObject.parseObject(responseString);
            if (jsonObject != null && jsonObject.getIntValue("status") == 401) {
                throw new AuthenticationException(jsonObject.getString("message"));
            }
            if (jsonObject != null){
                log.info("getUserInfo responseString:{}", responseString);
                return jsonObject.toJavaObject(CommonUserVO.class);
            }
        } catch (JSONException e) {
            throw new BusinessException(e.getMessage());
        }
        return null;
    }
}
