package com.workplat.utils;

import java.awt.*;
import java.awt.image.BufferedImage;

public class ImagesUtil {

    /**
     * 将图片旋转指定角度
     *
     * @param image 原始图片
     * @param angle 旋转角度
     * @return 旋转后的图片
     */
    public static BufferedImage rotateImageByDegrees(BufferedImage image, double angle) {
        // 将角度转换为弧度
        double rads = Math.toRadians(angle);
        double sin = Math.abs(Math.sin(rads));
        double cos = Math.abs(Math.cos(rads));

        // 计算新图片的尺寸
        int width = image.getWidth();
        int height = image.getHeight();
        int newWidth = (int) Math.floor(width * cos + height * sin);
        int newHeight = (int) Math.floor(height * cos + width * sin);

        // 创建新的BufferedImage对象
        BufferedImage rotatedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = rotatedImage.createGraphics();

        // 设置图片质量
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 进行旋转
        g2d.translate((newWidth - width) / 2, (newHeight - height) / 2);
        g2d.rotate(rads, (double) width / 2, (double) height / 2);
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();

        return rotatedImage;
    }
}
