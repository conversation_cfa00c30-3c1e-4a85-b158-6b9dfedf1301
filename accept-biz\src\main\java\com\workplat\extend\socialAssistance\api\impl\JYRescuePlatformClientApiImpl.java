package com.workplat.extend.socialAssistance.api.impl;

import com.alibaba.fastjson2.JSON;
import com.workplat.extend.socialAssistance.api.JYRescuePlatformClientApi;
import com.workplat.extend.socialAssistance.dto.RescueUserInfoDto;
import com.workplat.extend.socialAssistance.service.JYRescuePlatformClient;
import com.workplat.extend.socialAssistance.vo.AppealInfoVo;
import com.workplat.extend.socialAssistance.vo.RescueUserInfoVO;
import com.workplat.gss.common.core.response.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
public class JYRescuePlatformClientApiImpl implements JYRescuePlatformClientApi {

    @Autowired
    private JYRescuePlatformClient jyRescuePlatformClient;


    @Override
    public ResponseData<String> getAuthToken(String token) {
        String authToken = jyRescuePlatformClient.getAuthToken(token);
        return ResponseData.success(authToken);
    }

    @Override
    public ResponseData submitAppeal(String token) {
        RescueUserInfoDto rescueUserInfoDto = JSON.parseObject(formData, RescueUserInfoDto.class);
        List<RescueUserInfoVO> rescueUserInfoVOList = jyRescuePlatformClient.submitAppeal(rescueUserInfoDto, token);
        return ResponseData.success().data(rescueUserInfoVOList);
    }

    @Override
    public ResponseData<AppealInfoVo> getAppealInfo(String id, String token) {
        AppealInfoVo appealInfo = jyRescuePlatformClient.getAppealInfo(id, token);
        return ResponseData.success().data(appealInfo);
    }

    /**
     * 提交诉求申请请求
     */
    public static final String formData = """
            {
                "xm": "张三三三",
                "sfz": "320281198507282714",
                "lxdh": "18814683944",
                "ssdq": "320281107",
                "xjzd": "320281108212",
                "age": 0,
                "bylxsj": "1",
                "cjdj": "1",
                "cjlb": "2",
                "id": "",
                "jtcyrjsr": 111,
                "jtrks": 1,
                "localHj": "1",
                "sfjy": "1",
                "sfshbxzctrzj": "1",
                "sfwf": "1",
                "sfxyflyz": "1",
                "ysr": 111,
                "jtcyListYd": [
                                    {
                                        "age": 11,
                                        "gx": "10",
                                        "sex": "1",
                                        "sfcj": "1",
                                        "sfhzbhhjb": "1",
                                        "sfshbxzctrzj": "1",
                                        "sfxyflyz": "1",
                                        "sfz": "341224199604242310",
                                        "sfzyznxwh": "1",
                                        "xm": "邹传雪",
                                        "ysr": 111,
                                        "zdnj": "3",
                                        "zy": "2"
                                    }
                                ]
            }
            """;

    /**
     * 提交诉求申请请求响应
     */
    public static final String RescueUserInfoVO = """
            [
                {
                    "bksx": [
                        {
                            "bxsyy": "不符合政策条件",
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "name": "困境儿童"
                        },
                        {
                            "bxsyy": "不开学",
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "name": "临时救助"
                        },
                        {
                            "bxsyy": "不符合低保边缘要求",
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "name": "低保边缘"
                        },
                        {
                            "bxsyy": "不行",
                            "xm": "钱七七",
                            "pname": "教育",
                            "sfz": "320281198507280815",
                            "name": "教育救助"
                        },
                        {
                            "bxsyy": "vnvn",
                            "xm": "钱七七",
                            "pname": "人社",
                            "sfz": "320281198507280815",
                            "name": "就业困难人员"
                        },
                        {
                            "bxsyy": "1",
                            "xm": "钱七七",
                            "pname": "残联",
                            "sfz": "320281198507280815",
                            "name": "低收入家庭无障碍设施改造"
                        }
                    ],
                    "nkxs": [
                        {
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "nkxstsy": "可以额",
                            "name": "两项补贴"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "nkxstsy": "可以的",
                            "name": "特困救助"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "nkxstsy": "ok",
                            "name": "尊老金"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "民政",
                            "sfz": "320281198507280815",
                            "nkxstsy": "家庭平均月收入符合要求",
                            "name": "低保救助"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "人社",
                            "sfz": "320281198507280815",
                            "nkxstsy": "符合的提示语test",
                            "name": "灵活就业"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "妇联",
                            "sfz": "320281198507280815",
                            "nkxstsy": "1",
                            "name": "贫困两癌妇女"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "司法",
                            "sfz": "320281198507280815",
                            "nkxstsy": "1",
                            "name": "法律援助"
                        },
                        {
                            "xm": "钱七七",
                            "pname": "应急管理",
                            "sfz": "320281198507280815",
                            "nkxstsy": null,
                            "name": "自然灾害救助"
                        }
                    ]
                }
            ]
            """;
}
