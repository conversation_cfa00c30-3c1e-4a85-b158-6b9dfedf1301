package com.workplat.accept.business.chat.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.dto.AskDTO;
import com.workplat.accept.business.chat.dto.DifyMessageDTO;
import com.workplat.accept.business.chat.vo.BlockMessageVO;
import com.workplat.accept.business.chat.vo.BlockWorkFlowVO;
import com.workplat.accept.business.chat.vo.StreamMessageVO;
import com.workplat.accept.business.chat.vo.StreamWorkFlowVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;

@Slf4j
@Component
public class DifyServiceHelper {

    @Value("${dify.api:http://192.168.124.232/v1}")
    private String url;

    private final RestTemplate restTemplate;

    private final WebClient webClient;

    public DifyServiceHelper(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        this.webClient = WebClient.create();
    }

    /**
     * 流式调用dify chatMessage
     *
     * @param askDto 用户查询请求体
     * @param apiKey         用于API认证的Bearer令牌
     * @return 包含流式响应和最终问题数据的响应流
     */
    public Flux<StreamMessageVO> streamingMessage(AskDTO askDto, String apiKey) {
        // 构建API请求体
        HashMap<String, Object> inputs = new HashMap<>();
        inputs.put("channel", askDto.getChannel());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        DifyMessageDTO body = new DifyMessageDTO();
        body.setInputs(inputs);       // 初始化输入参数容器
        body.setQuery(askDto.getQuestions());                  // 设置用户查询内容
        body.setResponseMode("streaming");     // 指定流式响应模式
        body.setConversationId(askDto.getChatId());            // 清空会话ID（新会话）
        body.setUser(askDto.getUserId());       // 转换用户ID为字符串格式

        StringBuffer stringBuffer = new StringBuffer();

        // 构建WebClient请求
        Flux<StreamMessageVO> flux = webClient.post()
                .uri(url + "/chat-messages")  // 使用配置的API地址
                // 设置请求头
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);  // JSON格式请求体
                    httpHeaders.setBearerAuth(apiKey);  // Bearer Token认证
                })
                .bodyValue(JSON.toJSONString(body))  // 序列化请求体为JSON
                .retrieve()  // 发送请求并获取响应
                // 处理HTTP错误状态（4xx/5xx）
                .onStatus(
                        HttpStatusCode::isError, resp ->
                                resp.bodyToMono(String.class)  // 读取错误响应体
                                        .flatMap(error -> Mono.error(new RuntimeException("API error: " + error)))
                )
                // 将响应转换为流式响应对象流
                .bodyToFlux(StreamMessageVO.class)
                // 设置30秒超时（防止无限等待）
                //.timeout(Duration.ofSeconds(30))
                // 错误日志记录
                .doOnError(e -> log.error("请求处理异常", e))
                // 设置合理缓冲区大小
                .onBackpressureBuffer(500)
                // 记录元数据日志
                .doOnNext(res -> {
                    if (res.getAnswer() != null) {
                        stringBuffer.append(res.getAnswer());
                    }
//                    log.info("元数据: {}", res);
                })
                .doFinally(results -> {
                    if (!stringBuffer.isEmpty()) {
                        log.info("最终回答: {}", stringBuffer);
                    }
                })
                // 异常恢复处理（保持流连续性）
                .onErrorResume(e -> {
                    log.warn("元数据处理异常: {}", e.getMessage(), e);  // 记录完整异常信息
                    // 封装错误信息并返回
                    StreamMessageVO errorResponse = new StreamMessageVO();
                    errorResponse.setEvent("error");
                    errorResponse.setAnswer("处理请求时发生错误: " + e.getMessage());
                    errorResponse.setQuery(JSON.toJSONString(body)); // 返回整个请求体
                    errorResponse.setRecordId(askDto.getRecordId()); // 设置recordId，用于追踪整个请求链路
                    return Flux.just(errorResponse);
                });
        stopWatch.stop();
        log.info("StreamMessage time:{}ms", stopWatch.getTotalTimeMillis());
        return flux;
    }

    /**
     * 阻塞式调用dify chatMessage
     *
     * @param askDto 用户查询内容
     * @param apiKey         apiKey 通过 apiKey 获取权限并区分不同的 dify 应用
     * @return BlockResponse
     */
    public BlockMessageVO blockingMessage(AskDTO askDto, String apiKey) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //1.设置请求体
        DifyMessageDTO body = new DifyMessageDTO();
        body.setInputs(askDto.getInputs() == null ? new HashMap<>() : askDto.getInputs());       // 初始化输入参数容器
        body.setQuery(askDto.getQuestions());
        body.setResponseMode("blocking");
        body.setConversationId(askDto.getChatId());            // 清空会话ID（新会话）
        body.setUser(askDto.getUserId());
        //2.设置请求头
        HttpHeaders headers = getHttpHeaders(apiKey);
        //3.封装请求体和请求头
        String jsonString = JSON.toJSONString(body);
        HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
        //4.发送post请求，阻塞式
        ResponseEntity<BlockMessageVO> stringResponseEntity =
                restTemplate.postForEntity(url + "/chat-messages", entity, BlockMessageVO.class);
        //5.返回响应体
        BlockMessageVO response = stringResponseEntity.getBody();
        log.info("BlockMessageResponse: {}", response);
        stopWatch.stop();
        log.info("BlockMessage time:{}ms", stopWatch.getTotalTimeMillis());
        return response;
    }

    /**
     * 阻塞式调用dify workflow
     *
     * @param userId 用户id
     * @param apiKey apiKey 通过 apiKey 获取权限并区分不同的 dify 应用
     * @param inputs 请求体
     * @return BlockResponse
     */
    public BlockWorkFlowVO blockingWorkflow(String userId, String apiKey, ImmutableMap<String, Object> inputs) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //1.设置请求体
        DifyMessageDTO body = new DifyMessageDTO();
        body.setInputs(inputs);
        body.setResponseMode("blocking");
        body.setUser(userId);
        //2.设置请求头
        HttpHeaders headers = getHttpHeaders(apiKey);
        //3.封装请求体和请求头
        String jsonString = JSON.toJSONString(body);
        HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
        //4.发送post请求，阻塞式
        ResponseEntity<BlockWorkFlowVO> stringResponseEntity =
                restTemplate.postForEntity(url + "/workflows/run", entity, BlockWorkFlowVO.class);
        //5.返回响应体
        BlockWorkFlowVO response = stringResponseEntity.getBody();
        log.info("BlockWorkFlowResponse: {}", response);
        stopWatch.stop();
        log.info("BlockWorkFlow time:{}ms", stopWatch.getTotalTimeMillis());
        return response;
    }

   /**
     * 流式调用dify workflow
     *
     * @param userId 用户id
     * @param apiKey apiKey 通过 apiKey 获取权限并区分不同的 dify 应用
     * @param inputs 输入参数
     * @return Flux<StreamWorkFlowResponse>
     */
    public Flux<StreamWorkFlowVO> streamingWorkflow(String userId, String apiKey, ImmutableMap<String, Object> inputs) {
        // 构建API请求体
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        DifyMessageDTO body = new DifyMessageDTO();
        body.setInputs(inputs);
        body.setResponseMode("streaming");
        body.setUser(userId);

        StringBuffer stringBuffer = new StringBuffer();

        Flux<StreamWorkFlowVO> flux = webClient
                .post()
                .uri(url + "/workflows/run")
                // 设置请求头
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);  // JSON格式请求体
                    httpHeaders.setBearerAuth(apiKey);  // Bearer Token认证
                })
                .bodyValue(JSON.toJSONString(body))  // 序列化请求体为JSON
                .retrieve()  // 发送请求并获取响应
                // 处理HTTP错误状态（4xx/5xx）
                .onStatus(
                        HttpStatusCode::isError, resp ->
                                resp.bodyToMono(String.class)  // 读取错误响应体
                                        .flatMap(error -> Mono.error(new RuntimeException("API error: " + error)))
                )
                // 将响应转换为流式响应对象流
                .bodyToFlux(StreamWorkFlowVO.class)
                // 错误日志记录
                .doOnError(e -> log.error("请求处理异常", e))
                // 设置合理缓冲区大小
                .onBackpressureBuffer(500)
                // 记录元数据日志
                .doOnNext(res -> {
                    if (res.getData() != null) {
                        stringBuffer.append(JSON.toJSONString(res.getData()));
                    }
//                    log.info("元数据: {}", res);
                })
                .doFinally(results -> {
                    if (!stringBuffer.isEmpty()) {
                        log.info("最终回答: {}", stringBuffer);
                    }
                })
                // 异常恢复处理（保持流连续性）
                .onErrorResume(e -> {
                    log.warn("元数据处理异常: {}", e.getMessage(), e);  // 记录完整异常信息
                    // 将错误信息封装返回
                    StreamWorkFlowVO errorResponse = new StreamWorkFlowVO();
                    errorResponse.setEvent("error");
                    errorResponse.setData(ImmutableMap.of("error", e.getMessage(), "body", JSON.toJSONString(body)));
                    return Flux.just(errorResponse);
                });
        stopWatch.stop();
        log.info("StreamWorkFlow time:{}ms", stopWatch.getTotalTimeMillis());
        return flux;
    }

    @NotNull
    private static HttpHeaders getHttpHeaders(String apiKey) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(ImmutableList.of(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(apiKey);
        return headers;
    }

    /**
     * 获取消息列表
     *
     * @param userId         用户ID
     * @param conversationId 会话ID
     * @param limit          消息数量限制
     * @param apiKey         API密钥
     * @return 消息列表
     */
    public JSONObject getMessages(String userId, String conversationId, int limit, String apiKey) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 构建请求URL
        String requestUrl = String.format("%s/messages?user=%s&conversation_id=%s&limit=%d", url, userId, conversationId, limit);

        // 设置请求头
        JSONObject jsonObject = getJsonObject(apiKey, requestUrl, stopWatch);
        log.info("GetMessages time:{}ms", stopWatch.getTotalTimeMillis());

        return jsonObject;
    }

    // 获取流程执行情况
    public Integer workFlowsRun(String workflowId, String apiKey) {
        //http://192.168.124.232/v1/workflows/run/252913b4-3339-4074-b5fc-28db229f94cf

        //2.设置请求头
        HttpHeaders headers = getHttpHeaders(apiKey);
        //3.封装请求体和请求头
        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        ResponseEntity<String> exchange = restTemplate
                .exchange(url + "/workflows/run/" + workflowId, HttpMethod.GET, entity, String.class);
        String forObject = exchange.getBody();
        if (null != forObject) {
            JSONObject jsonObject = JSON.parseObject(forObject);
            return jsonObject.getInteger("total_steps");
        } else {
            return 0;
        }
    }

    /**
     * 停止 ChatMessage 任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 响应体
     */
    public String stopChatMessageTask(String taskId, String userId, String apiKey) {
        // 构建请求URL
        String requestUrl = String.format("%s/chat-messages/%s/stop", url, taskId);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);

        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("user", userId);

        // 封装请求体和请求头
        HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

        // 发送POST请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(requestUrl, entity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }


    /**
     * 获取会话列表
     *
     * @param userId 用户ID
     * @param lastId 最后一条会话的ID，用于分页
     * @param limit  会话数量限制
     * @param apiKey API密钥
     * @return 会话列表
     */
    public JSONObject getConversations(String userId, String lastId, int limit, String apiKey) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 构建请求URL
        String requestUrl = String.format("%s/conversations?user=%s&last_id=%s&limit=%d", url, userId, lastId, limit);

        // 设置请求头
        JSONObject jsonObject = getJsonObject(apiKey, requestUrl, stopWatch);
        log.info("GetConversations time:{}ms", stopWatch.getTotalTimeMillis());

        return jsonObject;
    }

    @Nullable
    private JSONObject getJsonObject(String apiKey, String requestUrl, StopWatch stopWatch) {
        HttpHeaders headers = getHttpHeaders(apiKey);

        // 封装请求体和请求头
        HttpEntity<String> entity = new HttpEntity<>(null, headers);

        // 发送GET请求
        ResponseEntity<String> responseEntity = restTemplate.exchange(requestUrl, HttpMethod.GET, entity, String.class);

        // 处理响应
        String responseBody = responseEntity.getBody();
        JSONObject jsonObject = JSON.parseObject(responseBody);

        stopWatch.stop();
        return jsonObject;
    }


}
