package com.workplat.accept.business.serviceZone.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.Comment;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单与专区多对多关联表
 */
@Getter
@Setter
@Entity
@Table(name = "conf_service_item_zone_relation", 
       uniqueConstraints = {@UniqueConstraint(columnNames = {"service_item_id", "service_zone_id", "deleted"})},
       indexes = {
           @Index(name = "idx_service_item_id", columnList = "service_item_id"),
           @Index(name = "idx_service_zone_id", columnList = "service_zone_id"),
           @Index(name = "idx_deleted", columnList = "deleted")
       })
public class ConfServiceItemZoneRelation extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_item_id", nullable = false)
    @Comment("服务清单ID")
    private ConfServiceItem serviceItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_zone_id", nullable = false)
    @Comment("服务专区ID")
    private ConfServiceZone serviceZone;

    @Comment("排序")
    @Column(name = "sort")
    private Integer sort;
}