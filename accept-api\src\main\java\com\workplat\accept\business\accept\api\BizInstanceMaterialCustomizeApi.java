
package com.workplat.accept.business.accept.api;

import com.workplat.gss.application.api.dto.BizInstanceInfoGetMaterialDTO;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RestController
@Tag(name = "申报材料模块-自定义")
@RequestMapping(value = "/api/biz/instance/material/customize")
public interface BizInstanceMaterialCustomizeApi {


    @GetMapping("/getSignFileList")
    @Operation(summary = "获取签名文件列表")
    ResponseData<List<BizInstanceMaterialFileVO>> getSignFileList(BizInstanceInfoGetMaterialDTO instanceInfoGetMaterialDTO);


}
