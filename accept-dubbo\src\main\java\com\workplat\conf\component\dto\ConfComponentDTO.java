package com.workplat.conf.component.dto;

import com.workplat.gss.common.core.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "组件保存模型")
public class ConfComponentDTO extends BaseEntity {

    @Schema(description = "组件名称")
    private String name;

    @Schema(description = "组件编码")
    private String code;

    @Schema(description = "组件类型")
    private String type;

    @Schema(description = "组件描述")
    private String description;

    @Schema(description = "组件内容")
    private String content;

    @Schema(description = "引擎编码")
    private String engineCode;

    @Schema(description = "可替换属性", example = "{\"组件属性label\":\"组件存储key\"}")
    private String replaceableProps;
}
