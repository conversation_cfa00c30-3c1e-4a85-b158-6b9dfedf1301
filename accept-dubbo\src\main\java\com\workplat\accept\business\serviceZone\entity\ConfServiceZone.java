package com.workplat.accept.business.serviceZone.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置实体类
 */
@Getter
@Setter
@Entity
@Table(name = "conf_service_zone", indexes = {
    @Index(name = "idx_name", columnList = "name"),
    @Index(name = "idx_deleted", columnList = "deleted")
})
public class ConfServiceZone extends BaseEntity {

    @Comment("专区名称")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Comment("专区描述")
    @Column(name = "description", length = 500)
    private String description;

    @Comment("专区图标地址文件id")
    @Column(name = "icon_file_id", length = 200)
    private String iconFileId;

    @Comment("wx背景图地址")
    @Column(name = "background_file_id", length = 200)
    private String backgroundFileId;

    @Comment("PC背景图")
    @Column(name = "pc_background_file_id", length = 200)
    private String pcBackgroundFileId;

    @Comment("是否启用 0:禁用 1:启用")
    @Column(name = "enabled")
    private Boolean enabled;

    @Comment("服务清单")
    @OneToMany(mappedBy = "serviceZone", cascade = CascadeType.ALL)
    @Where(clause = "deleted = 0")
    @OrderBy("sort asc")
    private List<ConfServiceItemZoneRelation> serviceItemRelations;

}
