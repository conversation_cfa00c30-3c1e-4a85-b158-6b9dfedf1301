package com.workplat.serviceZone.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置DTO
 */
@Data
public class ConfServiceZoneDTO {

    /**
     * 主键ID（更新时必填）
     */
    private String id;

    /**
     * 专区名称
     */
    @NotBlank(message = "专区名称不能为空")
    @Size(max = 100, message = "专区名称长度不能超过100个字符")
    private String name;

    /**
     * 专区描述
     */
    @Size(max = 500, message = "专区描述长度不能超过500个字符")
    private String description;

    /**
     * 专区图标地址
     */
    @Size(max = 200, message = "图标地址长度不能超过200个字符")
    private String iconFileId;

    /**
     * 背景图地址
     */
    @Size(max = 200, message = "背景图地址长度不能超过200个字符")
    private String backgroundFileId;

    /**
     * PC背景图
     */
    @Size(max = 200, message = "PC背景图长度不能超过200个字符")
    private String pcBackgroundFileId;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序号
     */
    private Integer sort;
}
