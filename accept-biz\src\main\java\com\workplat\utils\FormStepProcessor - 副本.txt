package com.workplat.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class FormStepProcessor {

    /**
     * 处理 JSON 表单 获取表单域的数量
     *
     * @param formJson 原始表单 JSON 字符串
     * @return 表单域的数量
     */
    public static int getFormStepCount(String formJson) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            return renderList.size();
        }
        return 0;
    }

    /**
     * 处理 JSON 表单并根据步骤编号返回特定的 renderList 项
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     步数 （0， 1， 2）
     * @return JSONObject，其中包含请求的步骤数据或空对象（如果步骤无效）
     */
    public static JSONObject getStepData(String formJson, int step) {
        // 步长减一，因为数组索引从0开始
        int stepIndex = step - 1;
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            if (stepIndex >= 0 && stepIndex < renderList.size()) {
                // Get the specific form area for the step
                JSONObject formArea = renderList.getJSONObject(stepIndex);

                // Create a new result object with the same structure but only the selected step
                result.put("tableName", formJsonObj.getString("tableName"));
                result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
                result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
                result.put("submitFunction", formJsonObj.getString("submitFunction"));

                // Create a new renderList with just the selected form area
                JSONArray singleStepRenderList = new JSONArray();
                singleStepRenderList.add(formArea);
                result.put("renderList", singleStepRenderList);
            }
        }

        return result;
    }

    /**
     * 返回指定步长之前的所有步长的替代版本
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     要包含的最大步数 （0， 1， 2）
     * @return JSONObject 包含指定步骤之前的所有步骤
     */
    public static JSONObject getStepsUpTo(String formJson, int step) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // Add all steps up to the specified one
            for (int i = 0; i <= step && i < renderList.size(); i++) {
                filteredRenderList.add(renderList.getJSONObject(i));
            }

            // Build the result object
            result.put("tableName", formJsonObj.getString("tableName"));
            result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
            result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
            result.put("submitFunction", formJsonObj.getString("submitFunction"));
            result.put("renderList", filteredRenderList);
        }

        return result;
    }

    /**
     * 处理表单JSON，根据字段映射保留需要展示的表单项
     *
     * @param formJson 原始表单JSON字符串
     * @param fieldMap 字段值映射表
     * @return 处理后的表单JSON对象
     */
    public static JSONObject processFormJson(String formJson, Map<String, Object> fieldMap) {
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 递归处理子组件
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    JSONArray filteredChildren = processChildrenRecursively(children, fieldMap);

                    // 只保留有匹配子组件的表单区域
                    if (!filteredChildren.isEmpty()) {
                        formArea.put("child", filteredChildren);
                        filteredRenderList.add(formArea);
                    }
                }
            }

            // 替换原始渲染列表为过滤后的列表
            formJsonObj.put("renderList", filteredRenderList);
        }

        return formJsonObj;
    }

    /**
     * 递归处理子组件，支持多层嵌套结构
     *
     * @param children 子组件数组
     * @param fieldMap 字段值映射表
     * @return 过滤后的子组件数组
     */
    private static JSONArray processChildrenRecursively(JSONArray children, Map<String, Object> fieldMap) {
        JSONArray filteredChildren = new JSONArray();

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            boolean shouldKeepChild = false;

            // 检查当前组件是否有field属性且在fieldMap中
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");

                if (field != null && fieldMap.containsKey(field)) {
                    // 更新字段值
                    Object value = fieldMap.get(field);
                    props.put("modelValue", value);
                    shouldKeepChild = true;
                }
            }

            // 递归处理子组件的子元素
            if (child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                JSONArray filteredSubChildren = processChildrenRecursively(subChildren, fieldMap);

                if (!filteredSubChildren.isEmpty()) {
                    child.put("child", filteredSubChildren);
                    shouldKeepChild = true;
                }
            }

            // 如果当前组件或其子组件有匹配的字段，则保留
            if (shouldKeepChild) {
                filteredChildren.add(child);
            }
        }

        return filteredChildren;
    }

    /**
     * 处理表单JSON，根据字段映射移除指定的表单项（与processFormJson相反）
     *
     * @param formJson 原始表单JSON字符串
     * @param excludeFieldMap 需要移除的字段映射表
     * @return 处理后的表单JSON对象
     */
    public static JSONObject removeFieldsFromFormJson(String formJson, Map<String, Object> excludeFieldMap) {
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 递归处理子组件
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    JSONArray filteredChildren = removeChildrenRecursively(children, excludeFieldMap);

                    // 如果还有剩余的子组件，则保留该表单区域
                    if (!filteredChildren.isEmpty()) {
                        formArea.put("child", filteredChildren);
                        filteredRenderList.add(formArea);
                    }
                }
            }

            // 替换原始渲染列表为过滤后的列表
            formJsonObj.put("renderList", filteredRenderList);
        }

        return formJsonObj;
    }

    /**
     * 递归移除子组件，支持多层嵌套结构（与processChildrenRecursively相反）
     *
     * @param children 子组件数组
     * @param excludeFieldMap 需要移除的字段映射表
     * @return 过滤后的子组件数组
     */
    private static JSONArray removeChildrenRecursively(JSONArray children, Map<String, Object> excludeFieldMap) {
        JSONArray filteredChildren = new JSONArray();

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            boolean shouldKeepChild = true;

            // 检查当前组件是否有field属性且在excludeFieldMap中
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");

                if (field != null && excludeFieldMap.containsKey(field)) {
                    // 如果字段在排除列表中，则不保留该组件
                    shouldKeepChild = false;
                }
            }

            // 递归处理子组件的子元素
            if (shouldKeepChild && child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                JSONArray filteredSubChildren = removeChildrenRecursively(subChildren, excludeFieldMap);

                // 更新子组件
                child.put("child", filteredSubChildren);

                // 如果子组件被完全移除且当前组件本身没有field，则可能需要移除当前组件
                if (filteredSubChildren.isEmpty()) {
                    // 检查当前组件是否只是容器组件（没有自己的field）
                    if (child.containsKey("props")) {
                        JSONObject props = child.getJSONObject("props");
                        String field = props.getString("field");
                        if (field == null || field.isEmpty()) {
                            // 如果是纯容器组件且子组件都被移除，则不保留
                            shouldKeepChild = false;
                        }
                    }
                }
            }

            // 如果应该保留该组件，则添加到结果中
            if (shouldKeepChild) {
                filteredChildren.add(child);
            }
        }

        return filteredChildren;
    }

    /**
     * 判断指定步骤是否还有可填写的字段
     *
     * @param formJson 原始表单JSON字符串
     * @param step 步骤编号（从1开始）
     * @param filledFieldMap 已填写的字段映射表
     * @return true表示该步骤已完成（没有可填写字段），false表示还有字段需要填写
     */
    public static boolean isStepCompleted(String formJson, int step, Map<String, Object> filledFieldMap) {
        // 获取指定步骤的数据
        JSONObject stepData = getStepData(formJson, step);

        // 如果步骤数据为空，认为已完成
        if (stepData == null || stepData.isEmpty()) {
            return true;
        }

        // 移除已填写的字段
        JSONObject remainingFields = removeFieldsFromFormJson(stepData.toJSONString(), filledFieldMap);

        // 检查是否还有可填写的字段
        return !hasEditableFields(remainingFields);
    }

    /**
     * 获取下一个需要填写的步骤编号
     *
     * @param formJson 原始表单JSON字符串
     * @param filledFieldMap 已填写的字段映射表
     * @return 下一个需要填写的步骤编号，如果所有步骤都已完成则返回-1
     */
    public static int getNextIncompleteStep(String formJson, Map<String, Object> filledFieldMap) {
        int totalSteps = getFormStepCount(formJson);

        for (int step = 1; step <= totalSteps; step++) {
            if (!isStepCompleted(formJson, step, filledFieldMap)) {
                return step;
            }
        }

        // 所有步骤都已完成
        return -1;
    }

    /**
     * 获取当前步骤中未填写的字段
     *
     * @param formJson 原始表单JSON字符串
     * @param step 步骤编号（从1开始）
     * @param filledFieldMap 已填写的字段映射表
     * @return 包含未填写字段的JSON对象
     */
    public static JSONObject getIncompleteFieldsInStep(String formJson, int step, Map<String, Object> filledFieldMap) {
        // 获取指定步骤的数据
        JSONObject stepData = getStepData(formJson, step);

        if (stepData == null || stepData.isEmpty()) {
            return new JSONObject();
        }

        // 移除已填写的字段，返回未填写的字段
        return removeFieldsFromFormJson(stepData.toJSONString(), filledFieldMap);
    }

    /**
     * 检查JSON对象中是否还有可编辑的字段
     *
     * @param jsonObject 要检查的JSON对象
     * @return true表示有可编辑字段，false表示没有
     */
    private static boolean hasEditableFields(JSONObject jsonObject) {
        if (jsonObject == null || !jsonObject.containsKey("renderList")) {
            return false;
        }

        JSONArray renderList = jsonObject.getJSONArray("renderList");
        if (renderList == null || renderList.isEmpty()) {
            return false;
        }

        // 递归检查是否有可编辑字段
        for (int i = 0; i < renderList.size(); i++) {
            JSONObject formArea = renderList.getJSONObject(i);
            if (formArea.containsKey("child")) {
                JSONArray children = formArea.getJSONArray("child");
                if (hasEditableFieldsInChildren(children)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 递归检查子组件中是否有可编辑字段
     *
     * @param children 子组件数组
     * @return true表示有可编辑字段，false表示没有
     */
    private static boolean hasEditableFieldsInChildren(JSONArray children) {
        if (children == null || children.isEmpty()) {
            return false;
        }

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);

            // 检查当前组件是否有field属性（表示是可编辑字段）
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");
                if (field != null && !field.isEmpty()) {
                    return true;
                }
            }

            // 递归检查子组件
            if (child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                if (hasEditableFieldsInChildren(subChildren)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 智能表单处理：根据已填写字段自动定位到需要填写的表单块
     *
     * @param formJson 原始表单JSON字符串
     * @param filledFieldMap 已填写的字段映射表
     * @return 包含需要填写字段的表单JSON对象，如果所有字段都已填写则返回空对象
     */
    public static JSONObject getFormBlockWithIncompleteFields(String formJson, Map<String, Object> filledFieldMap) {
        // 获取下一个需要填写的步骤
        int nextIncompleteStep = getNextIncompleteStep(formJson, filledFieldMap);

        if (nextIncompleteStep == -1) {
            // 所有步骤都已完成，返回空对象
            return new JSONObject();
        }

        // 获取该步骤中未填写的字段
        return getIncompleteFieldsInStep(formJson, nextIncompleteStep, filledFieldMap);
    }

    /**
     * 一键式智能表单处理：直接返回需要用户填写的表单块
     * 这是最简单的使用方式，一行代码搞定所有逻辑
     *
     * @param formJson 原始表单JSON字符串
     * @param filledFieldMap 已填写的字段映射表
     * @return 需要填写的表单JSON字符串，如果所有字段都已填写则返回空字符串
     */
    public static String getNextFormBlockToFill(String formJson, Map<String, Object> filledFieldMap) {
        JSONObject result = getFormBlockWithIncompleteFields(formJson, filledFieldMap);
        return result.isEmpty() ? "" : result.toJSONString();
    }

    /**
     * 检查表单是否已全部完成
     *
     * @param formJson 原始表单JSON字符串
     * @param filledFieldMap 已填写的字段映射表
     * @return true表示所有字段都已填写完成，false表示还有字段需要填写
     */
    public static boolean isFormCompleted(String formJson, Map<String, Object> filledFieldMap) {
        return getNextIncompleteStep(formJson, filledFieldMap) == -1;
    }

    /**
     * 智能表单处理：获取指定步骤中未填写的字段（带步骤信息）
     *
     * @param formJson 原始表单JSON字符串
     * @param step 步骤编号（从1开始）
     * @param filledFieldMap 已填写的字段映射表
     * @return 包含步骤信息和未填写字段的结果对象
     */
    public static FormStepResult getFormStepWithIncompleteFields(String formJson, int step, Map<String, Object> filledFieldMap) {
        FormStepResult result = new FormStepResult();
        result.setStepNumber(step);
        result.setTotalSteps(getFormStepCount(formJson));
        result.setCompleted(isStepCompleted(formJson, step, filledFieldMap));

        if (!result.isCompleted()) {
            JSONObject incompleteFields = getIncompleteFieldsInStep(formJson, step, filledFieldMap);
            result.setFormData(incompleteFields);
            result.setHasIncompleteFields(hasEditableFields(incompleteFields));
        } else {
            result.setFormData(new JSONObject());
            result.setHasIncompleteFields(false);
        }

        return result;
    }

    /**
     * 智能表单处理：获取所有步骤的完成状态和下一个需要处理的步骤
     *
     * @param formJson 原始表单JSON字符串
     * @param filledFieldMap 已填写的字段映射表
     * @return 表单处理结果对象
     */
    public static FormProcessResult processFormWithFilledFields(String formJson, Map<String, Object> filledFieldMap) {
        FormProcessResult result = new FormProcessResult();
        int totalSteps = getFormStepCount(formJson);
        result.setTotalSteps(totalSteps);
        result.setFilledFields(filledFieldMap);

        // 检查每个步骤的完成状态
        for (int step = 1; step <= totalSteps; step++) {
            boolean isCompleted = isStepCompleted(formJson, step, filledFieldMap);
            result.addStepStatus(step, isCompleted);
        }

        // 获取下一个需要填写的步骤
        int nextStep = getNextIncompleteStep(formJson, filledFieldMap);
        result.setNextIncompleteStep(nextStep);

        if (nextStep != -1) {
            // 获取下一步的未完成字段
            JSONObject nextStepIncompleteFields = getIncompleteFieldsInStep(formJson, nextStep, filledFieldMap);
            result.setNextStepFormData(nextStepIncompleteFields);
            result.setAllCompleted(false);
        } else {
            result.setNextStepFormData(new JSONObject());
            result.setAllCompleted(true);
        }

        return result;
    }

    /**
     * 表单步骤结果类
     */
    public static class FormStepResult {
        private int stepNumber;
        private int totalSteps;
        private boolean completed;
        private boolean hasIncompleteFields;
        private JSONObject formData;

        // Getters and Setters
        public int getStepNumber() { return stepNumber; }
        public void setStepNumber(int stepNumber) { this.stepNumber = stepNumber; }

        public int getTotalSteps() { return totalSteps; }
        public void setTotalSteps(int totalSteps) { this.totalSteps = totalSteps; }

        public boolean isCompleted() { return completed; }
        public void setCompleted(boolean completed) { this.completed = completed; }

        public boolean isHasIncompleteFields() { return hasIncompleteFields; }
        public void setHasIncompleteFields(boolean hasIncompleteFields) { this.hasIncompleteFields = hasIncompleteFields; }

        public JSONObject getFormData() { return formData; }
        public void setFormData(JSONObject formData) { this.formData = formData; }

        @Override
        public String toString() {
            return String.format("FormStepResult{stepNumber=%d, totalSteps=%d, completed=%s, hasIncompleteFields=%s}",
                stepNumber, totalSteps, completed, hasIncompleteFields);
        }
    }

    /**
     * 表单处理结果类
     */
    public static class FormProcessResult {
        private int totalSteps;
        private Map<String, Object> filledFields;
        private Map<Integer, Boolean> stepStatusMap = new HashMap<>();
        private int nextIncompleteStep;
        private JSONObject nextStepFormData;
        private boolean allCompleted;

        // Getters and Setters
        public int getTotalSteps() { return totalSteps; }
        public void setTotalSteps(int totalSteps) { this.totalSteps = totalSteps; }

        public Map<String, Object> getFilledFields() { return filledFields; }
        public void setFilledFields(Map<String, Object> filledFields) { this.filledFields = filledFields; }

        public Map<Integer, Boolean> getStepStatusMap() { return stepStatusMap; }
        public void addStepStatus(int step, boolean completed) { this.stepStatusMap.put(step, completed); }

        public int getNextIncompleteStep() { return nextIncompleteStep; }
        public void setNextIncompleteStep(int nextIncompleteStep) { this.nextIncompleteStep = nextIncompleteStep; }

        public JSONObject getNextStepFormData() { return nextStepFormData; }
        public void setNextStepFormData(JSONObject nextStepFormData) { this.nextStepFormData = nextStepFormData; }

        public boolean isAllCompleted() { return allCompleted; }
        public void setAllCompleted(boolean allCompleted) { this.allCompleted = allCompleted; }

        @Override
        public String toString() {
            return String.format("FormProcessResult{totalSteps=%d, nextIncompleteStep=%d, allCompleted=%s}",
                totalSteps, nextIncompleteStep, allCompleted);
        }
    }

    /**
     * 测试方法 - 验证新的表单结构处理
     */
    public static void main(String[] args) {
        // 读取新的表单JSON文件进行测试
        try {
            String formJson = java.nio.file.Files.readString(
                java.nio.file.Paths.get("accept-biz/src/main/java/com/workplat/utils/form.json"));

            // 测试场景1：只保留sslx字段（嵌套在可新增表格中）
            System.out.println("=== 测试场景1：只保留sslx字段 ===");
            Map<String, Object> fieldMap1 = new HashMap<>();
            fieldMap1.put("sslx", "sslx1");
            JSONObject result1 = processFormJson(formJson, fieldMap1);
            System.out.println("结果：" + result1.toJSONString());

            // 测试场景2：只保留cqsyfs字段（在表单域中）
            System.out.println("\n=== 测试场景2：只保留cqsyfs字段 ===");
            Map<String, Object> fieldMap2 = new HashMap<>();
            fieldMap2.put("cqsyfs", "cqsyfs1");
            JSONObject result2 = processFormJson(formJson, fieldMap2);
            System.out.println("结果：" + result2.toJSONString());

            // 测试场景3：保留多个字段
            System.out.println("\n=== 测试场景3：保留多个字段 ===");
            Map<String, Object> fieldMap3 = new HashMap<>();
            fieldMap3.put("sslx", "sslx2");
            fieldMap3.put("ssmj", "100");
            fieldMap3.put("cqsyfs", "cqsyfs2");
            JSONObject result3 = processFormJson(formJson, fieldMap3);
            System.out.println("结果：" + result3.toJSONString());

            // 测试新的反向方法
            System.out.println("\n=== 测试反向方法：移除sslx字段 ===");
            Map<String, Object> excludeFieldMap1 = new HashMap<>();
            excludeFieldMap1.put("sslx", "");
            JSONObject excludeResult1 = removeFieldsFromFormJson(formJson, excludeFieldMap1);
            System.out.println("结果：" + excludeResult1.toJSONString());

            System.out.println("\n=== 测试反向方法：移除多个字段 ===");
            Map<String, Object> excludeFieldMap2 = new HashMap<>();
            excludeFieldMap2.put("sslx", "");
            excludeFieldMap2.put("ssmj", "");
            JSONObject excludeResult2 = removeFieldsFromFormJson(formJson, excludeFieldMap2);
            System.out.println("结果：" + excludeResult2.toJSONString());

            // 测试步骤完成判断功能
            System.out.println("\n=== 测试步骤完成判断功能 ===");

            // 模拟已填写的字段
            Map<String, Object> filledFields = new HashMap<>();
            filledFields.put("sslx", "sslx1");
            filledFields.put("ssmj", "100");

            // 获取总步骤数
            int totalSteps = getFormStepCount(formJson);
            System.out.println("总步骤数：" + totalSteps);

            // 检查每个步骤的完成状态
            for (int step = 1; step <= totalSteps; step++) {
                boolean isCompleted = isStepCompleted(formJson, step, filledFields);
                System.out.println("步骤" + step + "是否完成：" + isCompleted);

                if (!isCompleted) {
                    JSONObject incompleteFields = getIncompleteFieldsInStep(formJson, step, filledFields);
                    System.out.println("步骤" + step + "未完成字段：" + incompleteFields.toJSONString());
                }
            }

            // 获取下一个需要填写的步骤
            int nextStep = getNextIncompleteStep(formJson, filledFields);
            System.out.println("下一个需要填写的步骤：" + (nextStep == -1 ? "所有步骤已完成" : "步骤" + nextStep));

            // 测试您的具体需求场景
            System.out.println("\n=== 测试您的具体需求场景 ===");
            JSONObject stepData = getStepData(formJson, 1);
            System.out.println("步骤1数据：" + stepData.toJSONString());

            JSONObject remainingFieldsInStep1 = removeFieldsFromFormJson(stepData.toJSONString(), filledFields);
            System.out.println("步骤1移除已填写字段后：" + remainingFieldsInStep1.toJSONString());

            boolean step1Completed = isStepCompleted(formJson, 1, filledFields);
            System.out.println("步骤1是否完成：" + step1Completed);

            if (step1Completed) {
                System.out.println("步骤1已完成，可以获取下一个步骤");
                int nextIncompleteStep = getNextIncompleteStep(formJson, filledFields);
                if (nextIncompleteStep != -1) {
                    JSONObject nextStepData = getStepData(formJson, nextIncompleteStep);
                    System.out.println("下一步骤" + nextIncompleteStep + "数据：" + nextStepData.toJSONString());
                }
            } else {
                System.out.println("步骤1还有字段需要填写");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}