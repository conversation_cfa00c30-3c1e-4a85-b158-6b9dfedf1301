package com.workplat.serviceZone.api.impl;

import cn.hutool.core.util.StrUtil;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.gss.common.core.dto.DictModel;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.dict.service.SysDictionarySubService;
import com.workplat.gss.common.dict.utils.SysDictCacheUtils;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.serviceZone.api.ConfServiceItemApi;
import com.workplat.serviceZone.converter.ConfServiceItemConverter;
import com.workplat.serviceZone.dto.ConfServiceItemDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.dto.ServiceItemQueryDTO;
import com.workplat.serviceZone.query.ServiceItemQuery;
import com.workplat.serviceZone.service.ConfServiceItemService;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import com.workplat.serviceZone.vo.ConfServiceItemVO;
import com.workplat.serviceZone.vo.ServiceItemGroupVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 推荐服务清单配置API实现类
 */
@Slf4j
@RestController
public class ConfServiceItemApiImpl implements ConfServiceItemApi {

    @Autowired
    private ConfServiceItemService confServiceItemService;

    @Autowired
    private ConfServiceItemConverter confServiceItemConverter;

    @Autowired
    SysDictionarySubService sysDictionarySubService;

    @ApiLogging(operation = "查询服务清单", module = "推荐服务清单配置", type = OperationType.QUERY)
    @Override
    public ResponseData<Page<ConfServiceItemVO>> queryPage(ServiceItemQueryDTO queryDTO, PageableDTO pageableDTO) {
        ServiceItemQuery query = getQuery(queryDTO);

        Pageable pageable = pageableDTO.convertPageable();
        Page<ConfServiceItem> entityPage = confServiceItemService.queryForPage(query, pageable);
        Page<ConfServiceItemVO> voPage = entityPage.map(confServiceItemConverter::convert);
        
        return ResponseData.success(voPage);
    }



    @ApiLogging(operation = "根据服务专区查询服务清单", module = "推荐服务清单配置", type = OperationType.QUERY)
    @Override
    public ResponseData<List<ConfServiceItemVO>> getByZoneId(String serviceZoneId) {
        List<ConfServiceItem> entities = confServiceItemService.getByServiceZoneId(serviceZoneId);
        List<ConfServiceItemVO> vos = confServiceItemConverter.convert(entities);
        return ResponseData.success(vos);
    }

    @ApiLogging(operation = "根据ID查询服务清单详情", module = "推荐服务清单配置", type = OperationType.QUERY)
    @Override
    public ResponseData<ConfServiceItemVO> getById(String id) {
        ConfServiceItem entity = confServiceItemService.queryById(id);
        if (entity == null) {
            throw new BusinessException("服务清单不存在");
        }
        
        ConfServiceItemVO vo = confServiceItemConverter.convert(entity);
        return ResponseData.success(vo);
    }

    @ApiLogging(operation = "添加服务清单", module = "推荐服务清单配置", type = OperationType.INSERT)
    @Override
    public ResponseData<String> add(ConfServiceItemDTO dto) {
        ConfServiceItem entity = confServiceItemConverter.convert(dto);
        if (entity.getEnabled() == null) {
            entity.setEnabled(true);
        }
        
        confServiceItemService.save(entity);
        
        // 处理关联专区关系
        if (dto.getServiceZoneIds() != null && !dto.getServiceZoneIds().isEmpty()) {
            confServiceItemService.saveServiceItemZoneRelations(entity.getId(), dto.getServiceZoneIds());
        }
        
        return ResponseData.success(entity.getId());
    }

    @ApiLogging(operation = "修改服务清单", module = "推荐服务清单配置", type = OperationType.UPDATE)
    @Override
    public ResponseData<Void> updateById(ConfServiceItemDTO dto) {
        ConfServiceItem entity = confServiceItemService.queryById(dto.getId());
        if (entity == null) {
            throw new BusinessException("服务清单不存在");
        }

        confServiceItemConverter.updateEntity(dto, entity);
        confServiceItemService.update(entity);

        // 处理关联专区关系
        if (dto.getServiceZoneIds() != null) {
            confServiceItemService.saveServiceItemZoneRelations(entity.getId(), dto.getServiceZoneIds());
        }

        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> deleteById(ConfServiceItemDTO dto) {
        confServiceItemService.deleteById(dto.getId());
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> updateEnabled(String id, Boolean enabled) {
        confServiceItemService.updateEnabled(id, enabled);
        return ResponseData.success().build();
    }


    @Override
    public ResponseData<List<ServiceItemGroupVO>> getGroupedByCategory(String serviceZoneId, String channel) {
        // 使用优化后的Service方法获取服务清单
        List<ConfServiceItem> serviceItems = confServiceItemService.getByServiceZoneId(serviceZoneId);
        
        // 过滤指定渠道的服务清单
        List<ConfServiceItem> filteredItems = serviceItems.stream()
                .filter(item -> item.getChannel() != null && item.getChannel().contains(channel))
                .filter(item -> item.getEnabled() == null || item.getEnabled()) // 只显示启用的
                .toList();

        List<DictModel> serviceItemCategory = sysDictionarySubService.getDictCacheListByCode("service_category");
        // 转换为VO
        List<ConfServiceItemVO> serviceItemVOs = confServiceItemConverter.convert(filteredItems);

        // 按分类分组，支持多分类（逗号分隔）
        Map<String, List<ConfServiceItemVO>> groupedItems = new LinkedHashMap<>();
        
        for (ConfServiceItemVO item : serviceItemVOs) {
            String category = item.getCategory();
            if (category != null && category.contains(",")) {
                // 多分类，按逗号拆分
                String[] categories = category.split(",");
                for (String cat : categories) {
                    String trimmedCat = cat.trim();
                    if (trimmedCat.isEmpty()) {
                        trimmedCat = "其他";
                    }
                    groupedItems.computeIfAbsent(trimmedCat, k -> new ArrayList<>()).add(item);
                }
            } else {
                // 单分类或无分类
                String finalCategory = (category != null && !category.trim().isEmpty()) ? category.trim() : "其他";
                groupedItems.computeIfAbsent(finalCategory, k -> new ArrayList<>()).add(item);
            }
        }

        // 预先构建分类到sort值的映射，提高排序效率
        Map<String, Integer> categorySortMap = new HashMap<>();
        if (serviceItemCategory != null) {
            for (DictModel dict : serviceItemCategory) {
                if (dict != null && dict.getValue() != null) {
                    categorySortMap.put(dict.getValue(), dict.getSort());
                }
            }
        }

        // 转换为分组VO并根据serviceItemCategory中的sort排序
        List<ServiceItemGroupVO> groupVOs = groupedItems.entrySet().stream()
                .map(entry -> {
                    ServiceItemGroupVO groupVO = new ServiceItemGroupVO();
                    groupVO.setCategory(entry.getKey());
                    groupVO.setItems(entry.getValue());
                    groupVO.setItemCount(entry.getValue().size());
                    return groupVO;
                })
                .sorted((vo1, vo2) -> {
                    // 使用预构建的映射获取sort值进行排序
                    Integer sort1 = categorySortMap.getOrDefault(vo1.getCategory(), Integer.MAX_VALUE);
                    Integer sort2 = categorySortMap.getOrDefault(vo2.getCategory(), Integer.MAX_VALUE);
                    return sort1.compareTo(sort2);
                })
                .toList();

        return ResponseData.success(groupVOs);
    }

    @NotNull
    private static ServiceItemQuery getQuery(ServiceItemQueryDTO queryDTO) {
        ServiceItemQuery query = new ServiceItemQuery();

        if (queryDTO != null) {
            if (StrUtil.isNotBlank(queryDTO.getName())) {
                query.setName(queryDTO.getName());
            }
            if (StrUtil.isNotBlank(queryDTO.getChannel())) {
                // 支持多个渠道，用逗号分隔
                if (queryDTO.getChannel().contains(",")) {
                    query.setChannels(Arrays.asList(queryDTO.getChannel().split(",")));
                } else {
                    query.setChannels(Arrays.asList(queryDTO.getChannel()));
                }
            }
            if (StrUtil.isNotBlank(queryDTO.getCategory())) {
                // 支持多个分类，用逗号分隔
                if (queryDTO.getCategory().contains(",")) {
                    query.setCategories(Arrays.asList(queryDTO.getCategory().split(",")));
                } else {
                    query.setCategories(Arrays.asList(queryDTO.getCategory()));
                }
            }
            if (queryDTO.getEnabled() != null) {
                query.setEnabled(queryDTO.getEnabled());
            }
            if (queryDTO.getIsHotRecommend() != null) {
                query.setIsHotRecommend(queryDTO.getIsHotRecommend());
            }
        }
        return query;
    }
}
