import cn.hutool.core.map.MapUtil;
import com.workplat.AcceptApp;
import com.workplat.gss.workflow.dubbo.flow.entity.GssModelInfo;
import com.workplat.gss.workflow.dubbo.flow.service.GssModelInfoService;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.jupiter.api.Test;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;


public class Test1 {


    @Test
    void xmlConversion() throws XMLStreamException {
        String xmlStr = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<bpmn:definitions xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:bpmn=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:dc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:di=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:flowable=\"http://flowable.org/bpmn\" id=\"Definitions_test\" targetNamespace=\"http://bpmn.io/schema/bpmn\">\n" +
                "  <bpmn:process id=\"test\" name=\"流程图设计\" isExecutable=\"true\">\n" +
                "    <bpmn:startEvent id=\"Event_0yw4nck\" flowable:initiator=\"initiator\">\n" +
                "      <bpmn:outgoing>Flow_0vmj4wo</bpmn:outgoing>\n" +
                "    </bpmn:startEvent>\n" +
                "    <bpmn:userTask id=\"Activity_17qz38a\" name=\"公共场所卫生许可子事项1\" flowable:assignee=\"${initiator}\" flowable:skipExpression=\"${initiator == &#39;&#39;}\" selectedMatter=\"{&#34;catalogId&#34;:&#34;765306bc20ce4809ba7040de75cf6dae&#34;,&#34;createTime&#34;:&#34;2024-09-09 13:22&#34;,&#34;createdBy&#34;:&#34;超级管理员&#34;,&#34;deleted&#34;:false,&#34;history&#34;:[],&#34;id&#34;:&#34;0588d2608238488d9770860075c27ee6&#34;,&#34;isNormal&#34;:true,&#34;isPublic&#34;:&#34;1&#34;,&#34;matterCode&#34;:&#34;ggcswsxk001&#34;,&#34;matterName&#34;:&#34;公共场所卫生许可子事项1&#34;,&#34;matterType&#34;:&#34;行政许可&#34;,&#34;region&#34;:&#34;SHAXIZHEN&#34;,&#34;regionName&#34;:&#34;&#34;,&#34;sort&#34;:999,&#34;updateTime&#34;:&#34;2024-09-13 10:43&#34;,&#34;updatedBy&#34;:&#34;超级管理员&#34;,&#34;version&#34;:10,&#34;label&#34;:&#34;公共场所卫生许可子事项1&#34;,&#34;value&#34;:&#34;ggcswsxk001&#34;}\">\n" +
                "      <bpmn:extensionElements>\n" +
                "        <flowable:assigneeType>static</flowable:assigneeType>\n" +
                "      </bpmn:extensionElements>\n" +
                "      <bpmn:incoming>Flow_0vmj4wo</bpmn:incoming>\n" +
                "      <bpmn:outgoing>Flow_1gtrt0b</bpmn:outgoing>\n" +
                "    </bpmn:userTask>\n" +
                "    <bpmn:sequenceFlow id=\"Flow_0vmj4wo\" sourceRef=\"Event_0yw4nck\" targetRef=\"Activity_17qz38a\" />\n" +
                "    <bpmn:userTask id=\"Activity_0a4gkh3\" name=\"lxn测试2-1\" selectedMatter=\"{&#34;catalogId&#34;:&#34;9351dee7330643478118fb1b3e0be802&#34;,&#34;createTime&#34;:&#34;2024-11-13 16:46&#34;,&#34;createdBy&#34;:&#34;超级管理员&#34;,&#34;deleted&#34;:false,&#34;history&#34;:[],&#34;id&#34;:&#34;0a4041db6545415da126e4bbaf9bc88a&#34;,&#34;isNormal&#34;:true,&#34;isPublic&#34;:&#34;0&#34;,&#34;matterCode&#34;:&#34;45341313&#34;,&#34;matterName&#34;:&#34;lxn测试2-1&#34;,&#34;matterType&#34;:&#34;ADMINISTRATIVE_PERMIT&#34;,&#34;region&#34;:&#34;LIUHEZHEN&#34;,&#34;regionName&#34;:&#34;&#34;,&#34;sort&#34;:999,&#34;updateTime&#34;:&#34;2024-11-13 16:46&#34;,&#34;updatedBy&#34;:&#34;超级管理员&#34;,&#34;version&#34;:1,&#34;label&#34;:&#34;lxn测试2-1&#34;,&#34;value&#34;:&#34;45341313&#34;}\">\n" +
                "      <bpmn:extensionElements>\n" +
                "        <flowable:assigneeType>static</flowable:assigneeType>\n" +
                "      </bpmn:extensionElements>\n" +
                "      <bpmn:incoming>Flow_1gtrt0b</bpmn:incoming>\n" +
                "      <bpmn:outgoing>Flow_10g4t5l</bpmn:outgoing>\n" +
                "    </bpmn:userTask>\n" +
                "    <bpmn:sequenceFlow id=\"Flow_1gtrt0b\" sourceRef=\"Activity_17qz38a\" targetRef=\"Activity_0a4gkh3\" />\n" +
                "    <bpmn:endEvent id=\"Event_0v36mr5\">\n" +
                "      <bpmn:incoming>Flow_10g4t5l</bpmn:incoming>\n" +
                "    </bpmn:endEvent>\n" +
                "    <bpmn:sequenceFlow id=\"Flow_10g4t5l\" sourceRef=\"Activity_0a4gkh3\" targetRef=\"Event_0v36mr5\" />\n" +
                "  </bpmn:process>\n" +
                "  <bpmndi:BPMNDiagram id=\"BPMNDiagram_1\">\n" +
                "    <bpmndi:BPMNPlane id=\"BPMNPlane_1\" bpmnElement=\"test\">\n" +
                "      <bpmndi:BPMNShape id=\"Event_0yw4nck_di\" bpmnElement=\"Event_0yw4nck\">\n" +
                "        <dc:Bounds x=\"172\" y=\"182\" width=\"36\" height=\"36\" />\n" +
                "      </bpmndi:BPMNShape>\n" +
                "      <bpmndi:BPMNShape id=\"Activity_17qz38a_di\" bpmnElement=\"Activity_17qz38a\">\n" +
                "        <dc:Bounds x=\"260\" y=\"160\" width=\"100\" height=\"80\" />\n" +
                "        <bpmndi:BPMNLabel />\n" +
                "      </bpmndi:BPMNShape>\n" +
                "      <bpmndi:BPMNShape id=\"Activity_0a4gkh3_di\" bpmnElement=\"Activity_0a4gkh3\">\n" +
                "        <dc:Bounds x=\"420\" y=\"160\" width=\"100\" height=\"80\" />\n" +
                "        <bpmndi:BPMNLabel />\n" +
                "      </bpmndi:BPMNShape>\n" +
                "      <bpmndi:BPMNShape id=\"Event_0v36mr5_di\" bpmnElement=\"Event_0v36mr5\">\n" +
                "        <dc:Bounds x=\"622\" y=\"182\" width=\"36\" height=\"36\" />\n" +
                "      </bpmndi:BPMNShape>\n" +
                "      <bpmndi:BPMNEdge id=\"Flow_0vmj4wo_di\" bpmnElement=\"Flow_0vmj4wo\">\n" +
                "        <di:waypoint x=\"208\" y=\"200\" />\n" +
                "        <di:waypoint x=\"260\" y=\"200\" />\n" +
                "      </bpmndi:BPMNEdge>\n" +
                "      <bpmndi:BPMNEdge id=\"Flow_1gtrt0b_di\" bpmnElement=\"Flow_1gtrt0b\">\n" +
                "        <di:waypoint x=\"360\" y=\"200\" />\n" +
                "        <di:waypoint x=\"420\" y=\"200\" />\n" +
                "      </bpmndi:BPMNEdge>\n" +
                "      <bpmndi:BPMNEdge id=\"Flow_10g4t5l_di\" bpmnElement=\"Flow_10g4t5l\">\n" +
                "        <di:waypoint x=\"520\" y=\"200\" />\n" +
                "        <di:waypoint x=\"622\" y=\"200\" />\n" +
                "      </bpmndi:BPMNEdge>\n" +
                "    </bpmndi:BPMNPlane>\n" +
                "  </bpmndi:BPMNDiagram>\n" +
                "</bpmn:definitions>";

//        GssModelInfo modelInfo = modelInfoService.queryForSingle(MapUtil.<String, Object>builder().put("=(modelKey)", "").build());
        XMLInputFactory xif = XMLInputFactory.newInstance();
        InputStreamReader xmlIn = new InputStreamReader(new ByteArrayInputStream(xmlStr.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
        XMLStreamReader xtr = xif.createXMLStreamReader(xmlIn);


    }
}
