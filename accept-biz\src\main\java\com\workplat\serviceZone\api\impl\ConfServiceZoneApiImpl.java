package com.workplat.serviceZone.api.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.serviceZone.api.ConfServiceZoneApi;
import com.workplat.serviceZone.converter.ConfServiceItemConverter;
import com.workplat.serviceZone.converter.ConfServiceZoneConverter;
import com.workplat.serviceZone.dto.ConfServiceZoneDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.dto.ServiceZoneQueryDTO;
import com.workplat.serviceZone.query.ServiceItemQuery;
import com.workplat.serviceZone.query.ServiceZoneQuery;
import com.workplat.serviceZone.service.ConfServiceItemService;
import com.workplat.serviceZone.service.ConfServiceZoneService;
import com.workplat.serviceZone.vo.ConfServiceZoneVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置API实现类
 */
@Slf4j
@RestController
public class ConfServiceZoneApiImpl implements ConfServiceZoneApi {

    @Autowired
    private ConfServiceZoneService confServiceZoneService;

    @Autowired
    private ConfServiceItemService confServiceItemService;

    @Autowired
    private ConfServiceZoneConverter confServiceZoneConverter;

    @Autowired
    private ConfServiceItemConverter confServiceItemConverter;

    @ApiLogging(operation = "分页查询服务专区", module = "服务专区", type = OperationType.QUERY)
    @Override
    public ResponseData<Page<ConfServiceZoneVO>> queryPage(ServiceZoneQueryDTO queryDTO, PageableDTO pageableDTO) {
        ServiceZoneQuery query = getQuery(queryDTO);
        Pageable pageable = pageableDTO.convertPageable();
        Page<ConfServiceZone> entityPage = confServiceZoneService.queryForPage(query, pageable);
        Page<ConfServiceZoneVO> voPage = entityPage.map(confServiceZoneConverter::convert);
        
        return ResponseData.success(voPage);
    }



    @ApiLogging(operation = "获取所有启用的服务专区", module = "服务专区", type = OperationType.QUERY)
    @Override
    public ResponseData<List<ConfServiceZoneVO>> getEnabled() {
        Map<String, Object> queryMap = MapUtil.<String, Object>builder()
                .put("=(enabled)", true)
                .build();

        
        List<ConfServiceZone> entities = confServiceZoneService.queryForList(queryMap);
        List<ConfServiceZoneVO> vos = confServiceZoneConverter.convert(entities);
        
        // 加载服务清单
//        vos.forEach(vo -> {
//            vo.setServiceItems(confServiceItemConverter.convert(
//                    confServiceItemService.getByServiceZoneId(vo.getId())));
//        });
        
        return ResponseData.success(vos);
    }

    @ApiLogging(operation = "根据ID获取服务专区", module = "服务专区", type = OperationType.QUERY)
    @Override
    public ResponseData<ConfServiceZoneVO> getById(String id) {
        ConfServiceZone entity = confServiceZoneService.queryById(id);
        if (entity == null) {
            throw new BusinessException("服务专区不存在");
        }
        
        ConfServiceZoneVO vo = confServiceZoneConverter.convert(entity);
//        vo.setServiceItems(confServiceItemConverter.convert(
//                confServiceItemService.getByServiceZoneId(id)));
        
        return ResponseData.success(vo);
    }

    @ApiLogging(operation = "添加服务专区", module = "服务专区", type = OperationType.INSERT)
    @Override
    public ResponseData<String> add(ConfServiceZoneDTO dto) {
        ConfServiceZone entity = confServiceZoneConverter.convert(dto);
        if (entity.getEnabled() == null) {
            entity.setEnabled(true);
        }
        
        confServiceZoneService.save(entity);
        
        return ResponseData.success(entity.getId());
    }

    @ApiLogging(operation = "更新服务专区", module = "服务专区", type = OperationType.UPDATE)
    @Override
    public ResponseData<Void> updateById(ConfServiceZoneDTO dto) {
        ConfServiceZone entity = confServiceZoneService.queryById(dto.getId());
        if (entity == null) {
            throw new BusinessException("服务专区不存在");
        }

        confServiceZoneConverter.updateEntity(dto, entity);
        confServiceZoneService.update(entity);

        return ResponseData.success().build();
    }

    @ApiLogging(operation = "删除服务专区", module = "服务专区", type = OperationType.DELETE)
    @Override
    public ResponseData<Void> deleteById(ConfServiceZoneDTO dto) {
        confServiceZoneService.deleteById(dto.getId());
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "更新服务专区启用状态", module = "服务专区", type = OperationType.UPDATE)
    @Override
    public ResponseData<Void> updateEnabled(String id, Boolean enabled) {
        confServiceZoneService.updateEnabled(id, enabled);
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "根据渠道获取服务专区", module = "服务专区", type = OperationType.QUERY)
    @Override
    public ResponseData<List<ConfServiceZoneVO>> getByChannel(String channel) {
        // 查询启用的专区，然后过滤包含指定渠道服务的专区
        Map<String, Object> queryMap = MapUtil.<String, Object>builder()
                .put("=(enabled)", true)
                .build();
        
        List<ConfServiceZone> entities = confServiceZoneService.queryForList(queryMap);
        List<ConfServiceZoneVO> vos = confServiceZoneConverter.convert(entities);

        // 返回推荐服务不为空的专区
        List<ConfServiceZoneVO> result = new ArrayList<>();
        vos.forEach(vo -> {
            List<ConfServiceItem> items = confServiceItemService.getByServiceZoneId(vo.getId())
                    .stream()
                    .filter(item -> item.getChannel().contains(channel))
                    .toList();
            if (!items.isEmpty()){
                result.add(vo);
            }
        });

        // 排序
        result.sort(Comparator.comparingInt(ConfServiceZoneVO::getSort));

        return ResponseData.success(result);
    }

    @NotNull
    private static ServiceZoneQuery getQuery(ServiceZoneQueryDTO queryDTO) {
        ServiceZoneQuery query = new ServiceZoneQuery();
        if (queryDTO != null) {
            if (StrUtil.isNotBlank(queryDTO.getName())) {
                query.setName(queryDTO.getName());
            }
            if (queryDTO.getEnabled() != null) {
                query.setEnabled(queryDTO.getEnabled());
            }
        }
        return query;
    }
}
