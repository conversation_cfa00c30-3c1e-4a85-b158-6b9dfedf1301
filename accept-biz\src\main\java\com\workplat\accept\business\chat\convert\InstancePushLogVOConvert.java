package com.workplat.accept.business.chat.convert;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.push.vo.InstancePushLogVO;
import org.springframework.stereotype.Component;

@Component
public class InstancePushLogVOConvert implements BaseConverter<InstancePushLog, InstancePushLogVO> {

    @Override
    public InstancePushLogVO convert(InstancePushLog source) {
        InstancePushLogVO target = new InstancePushLogVO();
        target.setId(source.getId());
        target.setInstanceId(source.getInstance().getId());
        target.setMatterName(source.getInstance().getMatterName());
        target.setApplicationName(source.getInstance().getApplicationName());
        target.setApplicationIdCard(source.getInstance().getApplicationCertificateCode());
        target.setApplicationMobile(source.getInstance().getApplicationPhone());
        target.setPushStatus(convertPushStatus(source.getPushStatus()));
        target.setPushTime(source.getPushTime());
        target.setPushResult(source.getPushResult());
        target.setApplicationTime(source.getInstance().getApplicationTime());
        target.setApplyDate(source.getInstance().getCreateTime());
        return target;
    }

    private String convertPushStatus(String pushStatus) {
        return switch (pushStatus) {
            case "0" -> "未推送";
            case "1" -> "推送成功";
            case "2" -> "推送失败";
            case null, default -> "未知";
        };
    }
}
