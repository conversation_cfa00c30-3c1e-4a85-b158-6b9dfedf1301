package com.workplat.accept.business.form.dto;

import lombok.Data;
import lombok.Builder;
import java.util.List;

@Data
@Builder
public class FormFieldDTO {
    private String id;
    private Boolean allCases;
    private BindDict bindDict;
    private String matterId;
    private Boolean required;
    private String fieldCode;
    private String fieldName;
    private String fieldAlias;
    private String matterCode;
    private String matterName;
    private String bindMatterIds;
    private String originModelId;
    private Object roleConfigure;
    private Object bindShareField;
    private String bindMatterCodes;
    private String bindMatterNames;
    private String fieldSourceType;
    private String formComponentType;
    private String desensitizationType;
    private String matterJointDataType;
    private String fieldSourceTypeLabel;
    private String fieldValue;  // 添加字段值属性
}

@Data
class BindDict {
    private List<Object> bindDictInfoList;
} 