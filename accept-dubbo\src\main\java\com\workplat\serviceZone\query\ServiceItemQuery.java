package com.workplat.serviceZone.query;

import com.workplat.gss.common.core.annotation.QueryMapBuild;
import com.workplat.gss.common.core.constant.QueryOperator;
import com.workplat.gss.common.core.dto.BaseQuery;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单查询条件
 */
@Setter
@Getter
public class ServiceItemQuery extends BaseQuery {

    @QueryMapBuild(operator = QueryOperator.LIKE)
    private String name;

    @QueryMapBuild(operator = QueryOperator.IN, fieldName  = "channel")
    private List<String> channels;

    @QueryMapBuild(operator = QueryOperator.IN , fieldName  = "category")
    private List<String> categories;

    @QueryMapBuild(operator = QueryOperator.EQUAL)
    private Boolean enabled;

    @QueryMapBuild(operator = QueryOperator.EQUAL)
    private Boolean isHotRecommend;
}
