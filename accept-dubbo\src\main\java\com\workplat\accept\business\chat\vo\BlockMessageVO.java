package com.workplat.accept.business.chat.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * Dify ChatFlow 阻塞式调用响应.
 */
@Data
public class BlockMessageVO implements Serializable {

    /**
     * 不同模式下的事件类型.
     */
    private String event;

    /**
     * 消息唯一 ID.
     */
    private String messageId;

    /**
     * 任务ID.
     */
    private String taskId;

    /**
     * agent_thought id.
     */
    private String id;

    /**
     * 会话 ID.
     */
    @JsonProperty(value = "conversation_id")
    private String conversationId;

    /**
     * App 模式，固定为 chat.
     */
    private String mode;

    /**
     * 完整回复内容.
     */
    private String answer;

    /**
     * 元数据.
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间戳.
     */
    private Long createdAt;

    /**
     * 记录id,作为多个智能体之间的唯一标识符
     */
    private String recordId;

}
