package com.workplat.push.ga.service;

import cn.hutool.core.map.MapUtil;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.doc.pdf.util.PdfConvertUtils;
import com.workplat.gss.file.service.SysFileEntityService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class GaDeclarePushService {

    @Resource
    @Qualifier("gaJdbcTemplate")
    private NamedParameterJdbcTemplate gaJdbcTemplate;

    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;

    @Autowired
    private SysFileEntityService sysFileEntityService;

    @Resource
    @Qualifier("gaTransactionTemplate")
    private TransactionTemplate gaTransactionTemplate;

    public void push(String instanceId, Boolean isFirstSubmit) {
        try {
            log.info("=== GaDeclarePushService.push 开始执行，instanceId: {}, isFirstSubmit: {} ===", instanceId, isFirstSubmit);
            // 打印当前线程名
            log.info("当前线程名：{}", Thread.currentThread().getName());
            gaTransactionTemplate.execute(status -> {
                // 1.推送基本办件信息和表单 信息
                BizInstanceInfoDetailVO detailVOById = bizInstanceInfoService.getDetailVOById(instanceId);
                BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);

                if (detailVOById != null) {
                    String sql = "INSERT INTO biz_ga_instance_info (id, create_time, accepted_num, application_address, application_certificate_code, " +
                            "application_name, application_mobile, matter_code, matter_name, form_obj, fields_map, is_first_submit) " +
                            "VALUES (:uuid, now(), :acceptedNum, :applicationAddress, :applicationCertificateCode, " +
                            ":applicationName, :applicationMobile, :matterCode, :matterName, :formObj, :fieldsMap, :isFirstSubmit)";

                    // 参数
                    MapSqlParameterSource sqlParameterSource = new MapSqlParameterSource();
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    sqlParameterSource.addValue("uuid", uuid);
                    sqlParameterSource.addValue("acceptedNum", detailVOById.getAcceptedNum());
                    sqlParameterSource.addValue("applicationAddress", detailVOById.getApplicationAddress());
                    sqlParameterSource.addValue("applicationCertificateCode", detailVOById.getApplicationCertificateCode());
                    sqlParameterSource.addValue("applicationName", detailVOById.getApplicationName());
                    sqlParameterSource.addValue("applicationMobile", detailVOById.getApplicationPhone());
                    sqlParameterSource.addValue("matterCode", bizInstanceInfo.getMatterCode());
                    sqlParameterSource.addValue("matterName", bizInstanceInfo.getMatterName());
                    sqlParameterSource.addValue("formObj", detailVOById.getBizInstanceFields().getFormObj());
                    sqlParameterSource.addValue("fieldsMap", detailVOById.getBizInstanceFields().getFieldsMap());
                    sqlParameterSource.addValue("isFirstSubmit", isFirstSubmit ? 1 : 0);

                    int updateCount = gaJdbcTemplate.update(sql, sqlParameterSource);
                    if (updateCount > 0) {
                        log.info("推送办件信息成功:{}", detailVOById.getId());
                        // 2.推送材料信息
                        for (BizInstanceMaterialVO materialVO : detailVOById.getBizInstanceMaterialVOS()) {
                            String materialSql = "INSERT INTO biz_ga_instance_material (id, create_time, material_id, material_code, material_description, material_name, instance_id)" +
                                    "VALUES" +
                                    "(:uuid, now(), :materialId, :materialCode, :materialDescription, :materialName, :instanceId)";

                            MapSqlParameterSource materialParams = new MapSqlParameterSource();
                            String materialUuid = UUID.randomUUID().toString().replace("-", "");
                            materialParams.addValue("uuid", materialUuid);
                            materialParams.addValue("materialId", materialVO.getId());
                            materialParams.addValue("materialCode", materialVO.getMaterialCode());
                            materialParams.addValue("materialDescription", materialVO.getMaterialDescription());
                            materialParams.addValue("materialName", materialVO.getMaterialName());
                            materialParams.addValue("instanceId", uuid);

                            int materialUpdateCount = gaJdbcTemplate.update(materialSql, materialParams);
                            if (materialUpdateCount > 0) {
                                // 3.推送材料文件信息
                                Map<String, byte[]> fileMap = pdfConvertPicture(materialVO); // 部分pdf转换为图片
                                for (BizInstanceMaterialFileVO fileVO : materialVO.getMaterialFileVOList()) {
                                    String fileSql = "INSERT INTO biz_ga_instance_material_file (id, create_time, file_name, file_data, material_id)" +
                                            "VALUES" +
                                            "(:uuid, now(),  :fileName, :file_data, :materialId)";

                                    MapSqlParameterSource fileParams = new MapSqlParameterSource();
                                    String fileUuid = UUID.randomUUID().toString().replace("-", "");
                                    fileParams.addValue("uuid", fileUuid);
                                    fileParams.addValue("fileName", fileVO.getFileName());
                                    try {
                                        fileParams.addValue("file_data"
                                                , fileMap.containsKey(fileVO.getFileId()) ?
                                                        fileMap.get(fileVO.getFileId()) : sysFileEntityService.binary(fileVO.getFileId()));
                                    } catch (Exception e) {
                                        log.error("获取文件数据失败，fileId: {}", fileVO.getFileId(), e);
                                        throw new RuntimeException("获取文件数据失败", e);
                                    }
                                    fileParams.addValue("materialId", materialUuid);

                                    int fileUpdateCount = gaJdbcTemplate.update(fileSql, fileParams);
                                    if (fileUpdateCount <= 0) {
                                        throw new RuntimeException("材料文件信息插入失败，fileId: " + fileVO.getFileId());
                                    }
                                }
                                log.info("推送材料信息成功:{}", materialVO.getMaterialName());
                            } else {
                                throw new RuntimeException("材料信息插入失败，materialId: " + materialVO.getId());
                            }
                        }
                    } else {
                        throw new RuntimeException("办件信息插入失败，instanceId: " + instanceId);
                    }
                }
                return null;
            });
        } catch (Exception e) {
            log.error("推送办件信息失败，触发事务回滚", e);
            // 重新抛出异常以触发事务回滚
            throw new BusinessException(StringUtils.substring(ExceptionUtils.getStackTrace(e), 0, 6000), e);
        }
    }

    /**
     * pdf转换为图片
     *
     * @param materialVO
     */
    private Map<String, byte[]> pdfConvertPicture(BizInstanceMaterialVO materialVO) {
        // 材料配置为纸质的时候才转换成图片
        if (!"PAPER".equals(materialVO.getSubmitFormat())) {
            return MapUtil.newHashMap();
        }
        Map<String, byte[]> fileMap = new HashMap<>();
        List<BizInstanceMaterialFileVO> materialFileVOList = materialVO.getMaterialFileVOList();
        int index = 0;
        for (BizInstanceMaterialFileVO fileVO : materialFileVOList) {
            byte[] binary = null;
            try {
                binary = sysFileEntityService.binary(fileVO.getFileId());
            } catch (Exception e) {
                log.error("获取文件数据失败，fileId: {}", fileVO.getFileId(), e);
                throw new RuntimeException("获取文件数据失败", e);
            }
            // 转换为图片,目前业务只会有一张图片
            List<byte[]> image = null;
            try {
                image = PdfConvertUtils.pdf2Image(binary, 50);
            } catch (Exception e) {
                log.error("pdf转换图片失败，fileId: {}", fileVO.getFileId(), e);
                continue;
            }
            if (image.isEmpty()) {
                continue;
            }
            fileMap.put(fileVO.getFileId(), image.get(0));

            // 文件后缀变成.png
            fileVO.setFileName(fileVO.getFileName().replace(".pdf", ".jpg"));
        }
        return fileMap;
    }


}
