package com.workplat.accept.business.chat.api.Impl;

import cn.hutool.http.Header;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.allInOne.AioServiceHelper;
import com.workplat.accept.business.chat.api.ChatApi;
import com.workplat.accept.business.chat.dto.*;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.accept.business.chat.service.DifyServiceHelper;
import com.workplat.accept.business.chat.service.Impl.ChatComponentService;
import com.workplat.accept.business.chat.service.Impl.ChatConversationService;
import com.workplat.accept.business.chat.service.Impl.ChatMessageService;
import com.workplat.accept.business.chat.service.Impl.ChatStreamService;
import com.workplat.accept.business.chat.service.MattersClassifyService;
import com.workplat.accept.business.chat.service.bizInstanceMaterialCustomizeService;
import com.workplat.accept.business.chat.vo.*;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.matter.service.ConfMatterExtendService;
import com.workplat.matter.vo.ConfMatterExtendVO;
import com.workplat.utils.ChatCacheUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatApiImpl implements ChatApi {

    private final ChatComponentService chatComponentService;
    private final ChatConversationService chatConversationService;
    private final ChatMessageService chatMessageService;
    private final ChatStreamService chatStreamService;
    private final DifyServiceHelper difyServiceHelper;
    private final ChatCacheUtil chatCacheUtil;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final MattersClassifyService mattersClassifyService;
    private final bizInstanceMaterialCustomizeService bizInstanceMaterialCustomizeService;
    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceChatService bizInstanceChatService;
    private final ConfMatterExtendService confMatterExtendService;
    private final AioServiceHelper aioServiceHelper;

    @Value("${entranceAgentKey:app-bgqibOyJsvdDBfbbXTpp346p}")
    private String DEFAULT_APP_KEY;

    // 字段提取key
    @Value("${FieldExtractionKey:app-bGVVIyj8ZoTiSuLjVnp9wYxJ}")
    private String FieldExtractionKey = "fileWithdrawKey";

    // 组件运行结束标识 给智能体
    private final String COMPONENT_RUN_END = "suCceSs_coMpoNent_eNd";

    public ChatApiImpl(ChatComponentService chatComponentService,
                       ChatConversationService chatConversationService,
                       ChatMessageService chatMessageService,
                       ChatStreamService chatStreamService,
                       DifyServiceHelper difyServiceHelper,
                       ChatCacheUtil chatCacheUtil,
                       BizInstanceInfoService bizInstanceInfoService,
                       MattersClassifyService mattersClassifyService,
                       bizInstanceMaterialCustomizeService bizInstanceMaterialCustomizeService,
                       BizInstanceFieldsService bizInstanceFieldsService,
                       BizInstanceChatService bizInstanceChatService,
                       ConfMatterExtendService confMatterExtendService, AioServiceHelper aioServiceHelper) {
        this.chatComponentService = chatComponentService;
        this.chatConversationService = chatConversationService;
        this.chatMessageService = chatMessageService;
        this.chatStreamService = chatStreamService;
        this.difyServiceHelper = difyServiceHelper;
        this.chatCacheUtil = chatCacheUtil;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.mattersClassifyService = mattersClassifyService;
        this.bizInstanceMaterialCustomizeService = bizInstanceMaterialCustomizeService;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceChatService = bizInstanceChatService;
        this.confMatterExtendService = confMatterExtendService;
        this.aioServiceHelper = aioServiceHelper;
    }

    @Override
    @ApiLogging(module = "对话", operation = "组件运行", type = OperationType.QUERY)
    public ResponseData<ComponentRunVO> componentRun(ComponentRunDTO componentRunDto) {
        return chatComponentService.componentRun(componentRunDto);
    }

    @Override
    @ApiLogging(module = "对话", operation = "问答", type = OperationType.QUERY)
    public Flux<ServerSentEvent<String>> ask(AskDTO ask) {
        log.debug("Asking question: {}", ask.getQuestions());

        if (ask.getComponentRunDto() == null) {
            return Flux.error(new IllegalArgumentException("ComponentRunDto is null"));
        }

//        ChatProcessDTO chatProcessDTO;
//        String recordId = ask.getRecordId();
//
//        if (recordId == null || recordId.isEmpty()) {
//            BizChatConversation conversation =
//                    chatConversationService.createConversation(ask.getUserId(), ask.getQuestions(), ask.getChannel(), null);
//            recordId = conversation.getId();
//            chatMessageService.saveUserMessage(recordId, ask.getQuestions());
//            ask.setRecordId(recordId);
//            chatProcessDTO = new ChatProcessDTO();
//            chatProcessDTO.setRecordId(recordId);
//        } else {
//            chatMessageService.saveUserMessage(recordId, ask.getQuestions());
//            chatProcessDTO = chatCacheUtil.get(recordId);
//            if (chatProcessDTO != null && StringUtils.isNotBlank(chatProcessDTO.getAgentKey())) {
//                ask.setAppKey(chatProcessDTO.getAgentKey());
//            }
//        }
//
        String appKey = StringUtils.isNotBlank(ask.getAppKey()) ? ask.getAppKey() : DEFAULT_APP_KEY;
//
//        // 处理特定的材料上传
//        if (ask.getFileUrls() != null && !ask.getFileUrls().isEmpty()
//                && "MaterialList".equals(ask.getComponentRunDto().getEngineCode())) {
//            chatMessageService.saveUserMessage(recordId, JSONObject.toJSONString(ask.getFileUrls()));
//            ask.getComponentRunDto().setSubmitData(JSONObject.toJSONString(ask.getFileUrls()));
//            if (chatProcessDTO != null) {
//                chatProcessDTO.setFileUrls(ask.getFileUrls());
//            }
//        }
//
//        // 缓存记录
//        chatCacheUtil.set(recordId, chatProcessDTO);
//
//        // 更新组件运行数据
//        if (ask.getComponentRunDto().getSubmitData() != null
//                && !"{}".equals(ask.getComponentRunDto().getSubmitData().toString())) {
//            // 处理组件运行数据
//            chatComponentService.BackFillData(ask.getComponentRunDto());
//            // 更新组件运行数据
//            chatMessageService.UpdateComponentRun(ask.getComponentRunDto());
//            // 获取下一步指令
//            ask.setQuestions((String) chatComponentService.getNextInstruction(ask.getComponentRunDto()));
//        }

        Flux<StreamMessageVO> streamMessageVOFlux = difyServiceHelper.streamingMessage(ask, appKey);
        return chatStreamService.processStreamMessages(ask, streamMessageVOFlux);
    }

    @Override
    @ApiLogging(module = "对话", operation = "问答阻塞式", type = OperationType.QUERY)
    public ResponseData<Object> askBlock(AskDTO ask) {
        // 初始化
        ChatProcessDTO chatProcessDTO = new ChatProcessDTO();
        init(ask, chatProcessDTO);
        // 获取阻塞消息
        BlockMessageVO blockMessageVO = difyServiceHelper.blockingMessage(ask, ask.getAppKey());
        // 创建会话
        BizChatConversation conversation = BizChatConversation.builder()
                .userId(ask.getUserId())
                .title("我要办理" + chatProcessDTO.getMatterName())
                .channel(ask.getChannel())
                .instanceId(chatProcessDTO.getInstanceId())
                .agentChatId(blockMessageVO.getConversationId())
                .agentKey(ask.getAppKey())
                .build();
        chatConversationService.createConversation(conversation);
        // 添加记录id
        blockMessageVO.setRecordId(conversation.getId());
        // 缓存新的办件的记录id
        chatCacheUtil.set(conversation.getId(), chatProcessDTO);
        return ResponseData.success(blockMessageVO);
    }

    private void init(AskDTO ask, ChatProcessDTO chatProcessDTO) {
        if (ask.getQuestions().startsWith("定义业务办理流程")) {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 从字符串中提取 JSON 部分
                int jsonStartIndex = ask.getQuestions().indexOf('{');
                String jsonString = ask.getQuestions().substring(jsonStartIndex);

                // 解析 JSON 字符串为 JsonNode 对象
                JsonNode jsonNode = objectMapper.readTree(jsonString);

                // 获取 matterId 的值
                String matterId = jsonNode.get("matterId").asText();
                String flowCode = jsonNode.get("flowCode").asText();
                String matterName = jsonNode.get("matterName").asText();

                // 假设给了我们一个数据 {"matterId":"190803ae4c734010af8b757a72ec3e62"}
                BizInstanceInfoVO bizInstanceInfoVO = bizInstanceInfoService.initialize(matterId);

                // 更新办件用户信息
                updateInstanceInfoUserInfo(ask, bizInstanceInfoVO);

                // 更新办件扩展信息
                updateInstanceInfoExtend(bizInstanceInfoVO);

                // 更新进入缓存
                chatProcessDTO.setInstanceId(bizInstanceInfoVO.getId());
                chatProcessDTO.setMatterName(matterName);
                chatProcessDTO.setFlowCode(flowCode);
            } catch (Exception e) {
                throw new RuntimeException("初始化办件失败", e);
            }
        }
    }

    private void updateInstanceInfoUserInfo(AskDTO ask, BizInstanceInfoVO bizInstanceInfoVO) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(bizInstanceInfoVO.getId());
        CommonUserVO commonUserInfo = aioServiceHelper.getCommonUserInfo(ask.getInputs().get("aNetToken").toString());
        if (commonUserInfo != null) {
            // 获取用户类型 判断企业还是个人
            if ("enterprise".equals(commonUserInfo.getUserType())) {
                //  企业
                bizInstanceInfo.setApplicationType("法人");
                bizInstanceInfo.setApplicationName(commonUserInfo.getRealName());
                bizInstanceInfo.setApplicationPhone(commonUserInfo.getMobile());
                bizInstanceInfo.setApplicationCertificateType("统一社会信用代码");
                bizInstanceInfo.setApplicationCertificateCode(commonUserInfo.getCertificateNumber());

                CommonUserVO.AioEnterpriseUserVO enterpriseUserVO = commonUserInfo.getAioEnterpriseUserVO();
                // 经办人
                bizInstanceInfo.setLinkName(enterpriseUserVO.getLinkManName());
                bizInstanceInfo.setLinkPhone(enterpriseUserVO.getLinkManPhone());
                bizInstanceInfo.setLinkCertificateType("身份证");
                bizInstanceInfo.setLinkCertificateCode(enterpriseUserVO.getLinkManPaperCard());

                // 法人
                bizInstanceInfo.setLegalPersonName(enterpriseUserVO.getLegalPersonName());
                bizInstanceInfo.setLegalPersonCertificateType("身份证");
                bizInstanceInfo.setLegalPersonCertificateCode(enterpriseUserVO.getLegalPersonPaperCard());
                bizInstanceInfo.setCompanyName(enterpriseUserVO.getEnterpriseName());
                bizInstanceInfo.setSocialCreditCode(enterpriseUserVO.getEnterpriseLicense());
                bizInstanceInfoService.update(bizInstanceInfo);
            } else {
                bizInstanceInfo.setApplicationType("自然人");
                bizInstanceInfo.setApplicationName(commonUserInfo.getRealName());
                bizInstanceInfo.setApplicationPhone(commonUserInfo.getMobile());
                bizInstanceInfo.setApplicationCertificateType("身份证");
                bizInstanceInfo.setApplicationCertificateCode(commonUserInfo.getCertificateNumber());
                bizInstanceInfo.setLinkName(commonUserInfo.getRealName());
                bizInstanceInfo.setLinkPhone(commonUserInfo.getMobile());
                bizInstanceInfo.setLinkCertificateType("身份证");
                bizInstanceInfo.setLinkCertificateCode(commonUserInfo.getCertificateNumber());
                bizInstanceInfoService.update(bizInstanceInfo);
            }
        }
    }

    private void updateInstanceInfoExtend(BizInstanceInfoVO bizInstanceInfoVO) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(bizInstanceInfoVO.getId());
        ConfMatterExtendVO confMatterExtendVO = confMatterExtendService.findVOByMatterId(bizInstanceInfo.getMatterId());
        bizInstanceInfo.extendJsonAdd(JSONObject.of(BizInstanceExtendJsonEnum.ConfMatterExtendVO.getCode(), confMatterExtendVO));
        bizInstanceInfoService.update(bizInstanceInfo);
    }

    @Override
    @ApiLogging(module = "对话", operation = "用户历史会话列表", type = OperationType.QUERY)
    public ResponseData<Page<BizChatConversationVO>> getConversationPage(BizChatConversationDTO dto, PageableDTO pageable) {
        return ResponseData.success(chatConversationService.getConversationPage(dto, pageable));
    }

    @Override
    @ApiLogging(module = "对话", operation = "获取最新的一个", type = OperationType.QUERY)
    public ResponseData<BizChatConversationVO> getLatestConversation(BizChatConversationDTO dto) {
        try {
            return ResponseData.success(chatConversationService.getLatestConversation(dto));
        } catch (Exception e) {
            log.error("getLatestConversation error", e);
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "对话", operation = "用户历史会话列表删除", type = OperationType.DELETE)
    public ResponseData<Void> deleteConversation(DeleteConversationDTO deleteConversationDTO) {
        chatConversationService.deleteConversation(deleteConversationDTO);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "对话", operation = "用户历史会话详情", type = OperationType.QUERY)
    public ResponseData<List<BizChatMessageVO>> getConversationMessages(BizChatMessageDTO dto) {
        return chatMessageService.getConversationMessages(dto);
    }

    @Override
    @ApiLogging(module = "对话", operation = "更新文件上传列表", type = OperationType.UPDATE)
    public ResponseData<Void> updateFileUploadList(UpdateFileUploadDTO updateFileUploadDTO) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(updateFileUploadDTO.getRecordId());
        chatProcessDTO.setMaterialSubmitVOS(updateFileUploadDTO.getMaterialSubmitVOS());
        chatCacheUtil.set(updateFileUploadDTO.getRecordId(), chatProcessDTO);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "对话", operation = "停止对话", type = OperationType.UPDATE)
    public ResponseData<Void> stopChat(StopChatDTO stopChatDTO) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(stopChatDTO.getRecordId());
        difyServiceHelper.stopChatMessageTask(stopChatDTO.getTaskId(), stopChatDTO.getUserId(),
                Objects.nonNull(chatProcessDTO.getAgentKey()) ? chatProcessDTO.getAgentKey() : DEFAULT_APP_KEY);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "对话", operation = "记录一个对话", type = OperationType.UPDATE)
    public ResponseData<Void> recordChat(BizChatMessageDTO dto) {
        return chatMessageService.recordChat(dto);
    }

    @Override
    @ApiLogging(operation = "文件字段内容提取-百炼", type = OperationType.INSERT, module = "chat")
    public ResponseData<Object> withdrawBl(FileClassifyDTO dto) {
        JSONArray jsonArray = mattersClassifyService.withdrawBl(dto, FieldExtractionKey);
        return ResponseData.success().data(jsonArray.getFirst());
    }

    @Override
    @ApiLogging(module = "授权", operation = "提交签名", type = OperationType.UPDATE)
    public ResponseData<Void> commitSign(CommitSignDTO dto) {
        // 提交签名
        bizInstanceMaterialCustomizeService.sign(dto.getInstanceId(), List.of(dto.getSignFileId()));
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "授权", operation = "更新签名状态", type = OperationType.UPDATE)
    public ResponseData<Void> updateSignatureStatus(CommitSignDTO dto) {
        // 更新签名状态
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(dto.getRecordId());
        chatProcessDTO.setSignStatus(dto.getSignStatus());
        chatCacheUtil.set(dto.getRecordId(), chatProcessDTO);
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "授权", operation = "轮询获取签名状态", type = OperationType.QUERY)
    public ResponseData<String> pollSignatureStatus(String recordId) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(recordId);
        if (chatProcessDTO != null) {
            return ResponseData.success(chatProcessDTO.getSignStatus());
        }
        return ResponseData.success().build();
    }

    @Override
    @ApiLogging(module = "办件", operation = "办件列表", type = OperationType.QUERY)
    public ResponseData<List<BizInstanceChatVO>> bizInstanceList(HttpServletRequest request, BizInstanceChatDTO dto) {
        // 获取请求头Authoritarian
        String authorization = request.getHeader(Header.AUTHORIZATION.getValue());
        if (StringUtils.isNotBlank(authorization)) {
            dto.setToken(authorization);
            List<BizInstanceChatVO> list = bizInstanceChatService.getList(dto);
            return ResponseData.success(list);
        }
        return null;
    }
}
