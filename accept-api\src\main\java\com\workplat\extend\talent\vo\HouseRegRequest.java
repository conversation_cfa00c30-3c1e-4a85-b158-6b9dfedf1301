package com.workplat.extend.talent.vo;

import com.workplat.gss.common.core.dto.PageableDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @package com.workplat.extend.vo
 * @description
 * @date 2025/7/10 9:20
 */
@Data
public class HouseRegRequest {

    @Schema(description = "登记人姓名")
    private String registerName;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

}
