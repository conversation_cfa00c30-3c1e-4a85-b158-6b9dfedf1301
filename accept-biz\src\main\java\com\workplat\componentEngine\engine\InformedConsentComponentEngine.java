package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceReadNoticeVO;
import com.workplat.gss.service.item.dubbo.matter.constant.MatterPublicType;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 描述 ：知情同意组件引擎
 *
 * <AUTHOR>
 * @date 2025/06/09
 */
@Slf4j
@Service
public class InformedConsentComponentEngine extends AbstractComponentEngine {

    private final ConfMatterExtendService confMatterExtendService;
    private final BizInstanceInfoService bizInstanceInfoService;
    private final ConfMatterPublicService confMatterPublicService;

    public InformedConsentComponentEngine(ConfMatterExtendService confMatterExtendService,
                                          BizInstanceInfoService bizInstanceInfoService,
                                          ConfMatterPublicService confMatterPublicService) {
        this.confMatterExtendService = confMatterExtendService;
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.confMatterPublicService = confMatterPublicService;
    }

    private final String CODE = ComponentEngineCode.INFORMED_FORM;

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {

        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        BizInstanceReadNoticeVO bizInstanceReadNoticeVO;
        try {
            String basicJson = confMatterPublicService.getAnyJson(bizInstanceInfo.getMatterPublicId(), MatterPublicType.BASIC_JSON);
            bizInstanceReadNoticeVO = JSONObject.parseObject(basicJson, BizInstanceReadNoticeVO.class);
        } catch (Exception e) {
            throw new RuntimeException("初始化申报阅读须知失败：", e);
        }

        // 转换读取须知
        InstructionFixedVO instructionFixedVO = new InstructionFixedVO();
        instructionFixedVO.setTitle(getPropertyValue(componentDataContext, "title"));
        instructionFixedVO.setContent(getPropertyValue(componentDataContext, "content"));
        instructionFixedVO.setConfirm(getPropertyValue(componentDataContext,"confirm"));
        instructionFixedVO.setIsFaceRecognition(bizInstanceReadNoticeVO.getIsFaceRecognition());

        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(instructionFixedVO)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(List.of(renderData));

        // 设置提示信息
        vo.setTips(getPropertyValue(componentDataContext,"tips"));
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Data
    static class InstructionFixedVO {
        /**
         * 是否人脸核验
         */
        private Boolean isFaceRecognition = false;
        /**
         * 内容
         */
        private String content;

        /**
         * 标题
         */
        private String title;

        /**
         * 确认
         */
        private String confirm;
    }
}
