package com.workplat.push.service.Impl;

import com.workplat.accept.business.chat.convert.InstancePushLogVOConvert;
import com.workplat.accept.business.chat.dto.InstancePushLogDTO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.push.model.InstancePushLogQuery;
import com.workplat.push.service.InstancePushLogService;
import com.workplat.push.vo.InstancePushLogVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName: InstancePushLogServiceImpl
 * @Description:
 * @Author: Yang Fan
 * @Date: 2025-07-10 15:18
 * @Version
 **/
@Service
public class InstancePushLogServiceImpl  extends BaseServiceImpl<InstancePushLog> implements InstancePushLogService {

    private final InstancePushLogVOConvert instancePushLogVOConvert;

    public InstancePushLogServiceImpl(InstancePushLogVOConvert instancePushLogVOConvert) {
        this.instancePushLogVOConvert = instancePushLogVOConvert;
    }

    @Override
    public Page<InstancePushLogVO> queryForPage(InstancePushLogDTO dto, PageableDTO pageable) {
        InstancePushLogQuery query = new InstancePushLogQuery();
        if (StringUtils.isNotBlank(dto.getPushStatus())) {
            query.setPushStatus(List.of(dto.getPushStatus().split(",")));
        }
        if (StringUtils.isNotBlank(dto.getMatterName())) {
            query.setMatterName(dto.getMatterName());
        }
        if (StringUtils.isNotBlank(dto.getName())) {
            query.setApplicationName(dto.getName());
        }
        if (StringUtils.isNotBlank(dto.getIdCard())) {
            query.setApplicationCertificateCode(dto.getIdCard());
        }
        if (StringUtils.isNotBlank(dto.getPhone())) {
            query.setApplicationPhone(dto.getPhone());
        }
        Page<InstancePushLog> page = queryForPage(query, pageable.convertPageable());
        return instancePushLogVOConvert.convert(page);
    }
}
