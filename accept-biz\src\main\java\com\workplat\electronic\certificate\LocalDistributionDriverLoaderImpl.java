//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;

/**
 * 本地存储
 */
public class LocalDistributionDriverLoaderImpl implements DistributionDriverLoader {

    BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
    BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);


    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {
        DistributionDriverOutput output = new DistributionDriverOutput();
        JSONObject oo = new JSONObject();

        // 保存一条会话办件记录

        BizInstanceChat chat = bizInstanceChatService.queryForSingle(ImmutableMap.of("=(instance.id)", input.getInstanceId()));
        try {
            if (chat == null) {
                BizInstanceChat bizInstanceChat = new BizInstanceChat();
                bizInstanceChat.setInstance(bizInstanceInfoService.queryById(input.getInstanceId()));
                bizInstanceChat.setCurrentNode(NodeStatusEnum.FINISH_ACCEPT.name());
                bizInstanceChat.setCurrentNodeName(NodeStatusEnum.FINISH_ACCEPT.getValue());
                bizInstanceChatService.create(bizInstanceChat);
            }
            oo.put("instanceId", input.getInstanceId());
            oo.put("msg", "保存办件会话记录成功");
        } catch (Exception e) {
            output.setSuccess(false);
            output.setMsg(e.getMessage());
        }
        return output;
    }
}