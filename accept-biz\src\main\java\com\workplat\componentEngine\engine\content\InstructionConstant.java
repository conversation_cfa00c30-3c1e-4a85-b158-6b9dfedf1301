package com.workplat.componentEngine.engine.content;

import lombok.Getter;

/**
 * 常量
 */
public enum InstructionConstant {

    KEEP_AT_PRESENT("keEp_AT_pREsenT", "继续当前"),
    SUCCESS_NEXT("suCceSs_NExt_coMpoNent", "跳过一个"),
    RETURN_LAST("RetuRn_laSt_coMpoNent", "返回上一个"),
    SUCCESS_END("suCceSs_coMpoNent_eNd", "正常");


    @Getter
    private String code;
    private String name;

    InstructionConstant(String code, String name) {
        this.code = code;
        this.name = name;
    }


}
