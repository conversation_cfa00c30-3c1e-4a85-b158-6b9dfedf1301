package com.workplat.componentEngine.api.Impl;

import com.workplat.componentEngine.ComponentEngineApi;
import com.workplat.componentEngine.engine.AbstractComponentEngine;
import com.workplat.componentEngine.vo.ComponentEngineVO;
import com.workplat.gss.common.core.response.ResponseData;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
public class ComponentEngineApiImpl implements ComponentEngineApi {


    private final ApplicationContext applicationContext;

    public ComponentEngineApiImpl(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public ResponseData<List<ComponentEngineVO>> list() {
        // 获取所有实现 AbstractComponentEngine 的组件
        Map<String, AbstractComponentEngine> beans = applicationContext.getBeansOfType(AbstractComponentEngine.class);

        List<ComponentEngineVO> componentEngineVOS = new ArrayList<>();
        
        for (Map.Entry<String, AbstractComponentEngine> entry : beans.entrySet()) {
            String beanName = entry.getKey();
            AbstractComponentEngine component = entry.getValue();
            
            ComponentEngineVO vo = new ComponentEngineVO();
            // 使用类的简单名称作为组件名称
            vo.setName(component.getClass().getSimpleName());
            // 使用Bean名称作为组件编码
            vo.setCode(beanName);
            
            componentEngineVOS.add(vo);
        }

        return ResponseData.success(componentEngineVOS);
    }
}
