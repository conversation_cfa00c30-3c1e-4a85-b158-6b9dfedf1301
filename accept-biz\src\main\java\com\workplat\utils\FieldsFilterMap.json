{"tel": "买方手机号", "dkfs": "贷款方式", "dkqk": "贷款情况", "jtcy": "家庭成员", "lyrq": "离异日期", "name": "mfxm", "sorq": "丧偶日期", "dlrlx": "代理人类型", "dlrxm": "代理人姓名", "jtcy1": "家庭一成员", "jtcy2": "家庭二成员", "jtcy3": "家庭三成员", "jtcy4": "家庭四成员", "jtcy5": "家庭五成员", "lzrxm": "领证人姓名", "xzlzr": "选择领证人", "cardno": "买方证件号码", "cqsyfs": "产权所有方式", "dlrsjh": "代理人手机号", "mfhjdz": "买方户籍地址", "mfhyzk": "买方婚姻状况", "mfpoxm": "配偶姓名", "mfssjt": "买方所属家庭", "nation": "买方国籍", "cqsyqxm": "产权所有人姓名", "cqsyrzb": "所有权占比", "dlrzjhm": "代理人证件号码", "dlrzjlx": "代理人证件类型", "helpTip": "自助打印温馨提示", "lzrlxfs": "领证人联系方式", "lzrzjhm": "领证人证件号码", "mpposjh": "配偶手机号", "cardtype": "买方证件类型", "houseTip": "所属家庭温馨提示", "loanBank": "贷款银行", "mfpozjhm": "配偶证件号码", "mfpozjlx": "配偶证件类型", "cqsyqxmFS": "选择共有人", "LZFSpayment": "领证方式", "cqsyqfsbzsm": "产权所有权方式备注说明", "mfsfxytjgyr": "买方是否需要添加共有人", "loanTypeName": "支付方式", "getCertificateTypeTname": "收件人姓名", "getCertificateTypeTmobile": "收件人联系电话", "getCertificateTypeTaddress": "邮件地址"}