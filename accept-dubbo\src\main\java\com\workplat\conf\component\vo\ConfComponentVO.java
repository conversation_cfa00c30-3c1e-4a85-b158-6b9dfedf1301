package com.workplat.conf.component.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "组件展示模型")
public class ConfComponentVO {

    @Schema(description ="组件id")
    private String id;

    @Schema(description ="组件名称")
    private String name;

    @Schema(description ="组件编码")
    private String code;

    @Schema(description ="组件类型")
    private String type;

    @Schema(description ="组件描述")
    private String desc;

    @Schema(description ="组件内容")
    private String content;
}
