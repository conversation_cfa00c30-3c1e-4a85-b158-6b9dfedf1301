## 开发指南

### 框架和语言
> 本项目基于Spring生态系统，并遵循特定的模块化开发模式。

**框架考虑因素:**
- 版本兼容性: 确保所有依赖与选择的框架版本兼容（SpringBoot 3.2.4、Spring Cloud 2023.0.1）
- 功能使用: 利用框架特定功能，而不是重新发明解决方案
- 性能模式: 遵循推荐的模式以获得最佳性能
- 升级策略: 规划未来框架更新，以最小化对系统的影响
- 框架重要注意事项: 
	* 项目启动时需要添加VM参数: `--add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED`
	* 项目启动时Flowable可能无法自动建表，需要在数据库连接添加配置：`nullCatalogMeansCurrent=true`
	* 查询若报错Error attempting to apply AttributeConverter，检查数据库表create_by字段是否为正确的用户ID

**语言最佳实践:**
- 类型安全: 使用JDK 21的强类型特性防止运行时错误
- 现代特性: 利用Java 21的现代语言特性，同时保持兼容性
- 一致性: 在整个代码库中应用一致的编码模式
- 文档: 记录特定于语言的实现和解决方法

### 代码抽象和可重用性
> 在开发过程中，优先考虑代码抽象和可重用性，以确保模块化和基于组件的功能。在重新发明轮子之前，先尝试搜索现有的解决方案。

**模块化设计原则:**
- 单一职责: 每个模块只负责一个功能
- 高内聚，低耦合: 相关功能集中，减少模块之间的依赖
- 稳定接口: 对外暴露稳定接口，内部实现可以变化

**可重用组件库:**
```
ai-central-platform
├── accept-api       // 对外交互API、DTO层
├── accept-dubbo     // 对内Dubbo服务层 
├── accept-biz       // 业务实现层
    ├── utils        // 工具类目录：包含各类通用工具
    └── ...
```

### 编码标准和工具
**代码格式化工具:**
- Lombok: 简化对象封装工具
- Maven: 项目构建和依赖管理工具

**命名和结构约定:**
- 语义命名: 变量/函数名应明确表达其目的
- 一致的命名风格: 使用驼峰命名法
- 目录结构遵循功能责任划分
- 数据库表命名规范:
  * 系统配置: 表前缀`sys_`, 实体类前缀`Sys`, 路径前缀`/system/*`
  * 业务配置: 表前缀`conf_`, 实体类前缀`Conf`, 路径前缀`/conf/*`
  * 业务相关: 表前缀`biz_`, 实体类前缀`Biz`, 路径前缀`/business/*`
  * 日志相关: 表前缀`log_`, 实体类前缀`Log`, 路径前缀`/system/log/*`
  * 定制相关: 表前缀`cus_`, 实体类前缀`Cus`, 路径前缀`/business/custom/*`

### 前后端协作标准
**API设计和文档:**
- RESTful设计原则
	* API接口路径规范: `/api/{中心名称}/{模块名称}/{功能}/{......}`
	* 例：配置中心字典模块根据code查询字典功能: `/api/configuration/dict/getByCode`
- 及时的接口文档更新
	* 使用Knife4j自动生成API文档
- 统一的错误处理规范

**数据流:**
- 清晰的后端状态管理
- 前后端都进行数据验证
- 标准化的异步操作处理

### 性能和安全
**性能优化重点:**
- 使用Caffeine进行内存缓存
- 使用Redis进行分布式缓存
- 使用Druid数据库连接池优化数据库连接

**安全措施:**
- 输入验证和过滤
	* 验证用户输入并清理数据
- 敏感信息保护
	* 使用Sa-Token进行安全认证和授权
- 访问控制机制
	* 实现基于角色的访问控制

### Git规范
**Git提交规范:**
- 安装idea插件：`Git Commit Message Helper`
- 提交代码时，勾选提交类型、填写相应的功能模块以及简要描述

**分支开发规范:**
- 功能开发过程中以新建分支的形式进行
- 新建分支的命名规则以分支名称+功能模块名称+功能名称，如：`dev-xxxx-xxxx`
- 完整的功能模块开发完成后合并回dev分支
- 合并后及时删除原有开发分支

**分支说明:**
- master: 主分支
- release: 发布分支
- test: 测试分支
- dev: 开发分支