package com.workplat.electronic.certificate;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceMaterial;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.script.model.instance.MaterialsBackFillRenderInput;
import com.workplat.gss.common.script.model.instance.MaterialsBackFillRenderOutput;
import com.workplat.gss.common.script.service.instance.MaterialsBackFillRenderLoader;

import java.util.List;

public class MaterialsBackFillRenderLoaderImpl implements MaterialsBackFillRenderLoader {
    @Override
    public MaterialsBackFillRenderOutput materialsBackFillRender(MaterialsBackFillRenderInput input) {
        BizInstanceFieldsService bizInstanceFieldsService = ApplicationContextUtil.getBean(BizInstanceFieldsService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);

        String instanceFieldId = input.getInstanceFieldId();
        BizInstanceFields bizInstanceFields = bizInstanceFieldsService.queryById(instanceFieldId);
        // 获得表单数据
        JSONObject jsonObject = JSONObject.parseObject(bizInstanceFields.getFormObj());

        List<String> materialNames = bizInstanceMaterialService.getInstanceMaterial(input.getInstanceId()).stream().map(BizInstanceMaterialVO::getMaterialName)
                .toList();

        jsonObject.put("clgxbs", String.join(",", materialNames));

        // 返回
        MaterialsBackFillRenderOutput output = new MaterialsBackFillRenderOutput();
        output.setJsonObject(jsonObject);
        return output;
    }
}
