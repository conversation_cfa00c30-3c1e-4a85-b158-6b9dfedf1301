package com.workplat.accept.business.chat.service.Impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.Header;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.allInOne.AioServiceHelper;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.convert.BizInstanceChatVOConvert;
import com.workplat.accept.business.chat.dto.BizInstanceChatDTO;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.accept.business.chat.vo.BizInstanceChatVO;
import com.workplat.accept.business.chat.vo.CommonUserVO;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.application.dubbo.constant.InstanceApplicationStatusEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class BizInstanceChatServiceImpl extends BaseServiceImpl<BizInstanceChat> implements BizInstanceChatService {

    @Autowired
    private BizInstanceChatVOConvert bizInstanceChatVOConvert;
    @Autowired
    private BizChatConversationService bizChatConversationService;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    AioServiceHelper aioServiceHelper;

    /**
     * 获取办件记录 三部分：本地办件记录 + allinone办件记录+ 对话记录产生的办件记录 + 档案查询
     * @param dto
     * @return {@link List }<{@link BizInstanceChatVO }>
     */
    @Override
    public List<BizInstanceChatVO> getList(BizInstanceChatDTO dto) {
        // 1.数据一：查询本地办件记录
        CommonUserVO commonUserInfo = new CommonUserVO();
        if (StringUtils.isNotBlank(dto.getToken()) && dto.getToken().contains("test")) {
            commonUserInfo.setId("1");
            commonUserInfo.setRealName("测试用户");
            Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "610431199905063016").put("name", "杨帆").build();
            String token = JSONUtil.parseObj(GatewayUtils.getAllinoneApiResult("/api/zzj/selfServiceLogin", formMap)).getStr("token");
            dto.setToken("Bearer " + token);
        } else {
            commonUserInfo = aioServiceHelper.getCommonUserInfo(dto.getToken());
            if (commonUserInfo == null){
                return new ArrayList<>();
            }
        }
        String certificateNumber = commonUserInfo.getCertificateNumber();

        Map<String, Object> localParams = new HashMap<>();
        localParams.put("=(instance.applicationCertificateCode)", StringUtils.isNotBlank(certificateNumber) ? certificateNumber : "610431199905063016");
        if (StringUtils.isNotBlank(dto.getStatus()) && !dto.getStatus().equals("all")) {
            localParams.put("=(currentNode)", dto.getStatus().toUpperCase());
        }
        // 创建时间为七天 前
        long lastSeekTimeStamp = System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000;
        localParams.put(">(createTime)", lastSeekTimeStamp);

        List<BizInstanceChat> list = queryForList(localParams);
        List<BizInstanceChatVO> convert = bizInstanceChatVOConvert.convert(list);
        // 2.数据二：查询allinone办件记录 start 全部all  草稿draft_accept  办理中business_accepting  退回reject_business  完成finish_accept
        Map<String, Object> params = new HashMap<>();
        params.put("status", StringUtils.isBlank(dto.getStatus()) ? "all" : dto.getStatus());
        params.put("page", 1);
        params.put("limit", 99);
        params.put("sort", "businessTime,desc");
        String executeRequest = GatewayUtils.executeGetRequest("/allinone-api/api/aioBusiness/findPage",
                ImmutableMap.of(Header.AUTHORIZATION.getValue(), dto.getToken()),
                params, false, false);
        ConvertToAllInOne(executeRequest, convert);
        // 3.数据三：查询对话记录产生的办件记录
        if (dto.getStatus().equals("all") || dto.getStatus().equals("draft_accept")) {
            ImmutableMap<String, Object> chatParams = ImmutableMap.of("=(userId)", commonUserInfo.getId(),
                    "=(channel)", dto.getChannel(),
                    "<>(instanceId)", "-1", ">(createTime)", lastSeekTimeStamp);
            List<BizChatConversation> bizChatConversations = bizChatConversationService.queryForList(chatParams);
            bizChatConversations.forEach(bizChatConversation -> {
                BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(bizChatConversation.getInstanceId());
                if (InstanceApplicationStatusEnum.TEMP.equals(bizInstanceInfo.getApplicationStatusEnum())) {
                    BizInstanceChatVO bizInstanceChatVO = new BizInstanceChatVO();
                    bizInstanceChatVO.setId(bizChatConversation.getId());
                    bizInstanceChatVO.setInstanceId(bizChatConversation.getInstanceId());
                    bizInstanceChatVO.setCurrentNode(NodeStatusEnum.DRAFT_ACCEPT.name());
                    bizInstanceChatVO.setCurrentNodeName(NodeStatusEnum.DRAFT_ACCEPT.getValue());
                    bizInstanceChatVO.setName(bizInstanceInfo.getMatterName());
                    bizInstanceChatVO.setTime(bizInstanceInfo.getCreateTime());
                    bizInstanceChatVO.setUserName(bizInstanceInfo.getApplicationName());
                    bizInstanceChatVO.setChannel("chat");
                    convert.add(bizInstanceChatVO);
                }
            });
        }
        // 4.数据四：查询档案查询记录-申请记录
        String archiveRequest = GatewayUtils.executeGetRequest("/allinone-api/api/archives/record/findByPage",
                ImmutableMap.of(Header.AUTHORIZATION.getValue(), dto.getToken()),
                Map.of("recordType","1","pageNum","1","pageSize","99"), false, false);
        convert.addAll(ConvertToArchive(archiveRequest, dto.getStatus(), commonUserInfo.getRealName()));
        // 根据时间降序排序
        convert.sort(Comparator.comparing(BizInstanceChatVO::getTime).reversed());
        return convert;
    }

    private List<BizInstanceChatVO> ConvertToArchive(String archiveRequest, String status, String name) {
        List<BizInstanceChatVO> list = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(archiveRequest);
        if (jsonObject == null || jsonObject.getJSONArray("content") == null) {
            return list;
        }
        // 转换追加办件记录
        for (Object o : jsonObject.getJSONArray("content")) {
            JSONObject o1 = JSONObject.parseObject(JSON.toJSONString(o));
            BizInstanceChatVO bizInstanceChatVO = new BizInstanceChatVO();

            switch (o1.getString("applyStatus")){
                case "NOHANDLE":
                    bizInstanceChatVO.setCurrentNode(NodeStatusEnum.BUSINESS_ACCEPTING.name());
                    bizInstanceChatVO.setCurrentNodeName(NodeStatusEnum.BUSINESS_ACCEPTING.getValue());
                    break;
                case "HANDLEDSUCCESS","HANDLEDFAIL":
                    bizInstanceChatVO.setCurrentNode(NodeStatusEnum.FINISH_ACCEPT.name());
                    bizInstanceChatVO.setCurrentNodeName(NodeStatusEnum.FINISH_ACCEPT.getValue());
                    break;
            }
            bizInstanceChatVO.setNo(o1.getString("archivesRecordId"));
            bizInstanceChatVO.setName(o1.getString("archivesTypeName"));
            bizInstanceChatVO.setTime(o1.getDate("recordTime"));
            bizInstanceChatVO.setUserName(name);
            bizInstanceChatVO.setChannel("allinone");
            bizInstanceChatVO.setMobileUrl("https://zwfw.tcsjj.cn/applications/files-query/#/archiveRecordsApplyIndex?fromComponent=cdsq");
            list.add(bizInstanceChatVO);
        }
        // 如果是all 直接返回
        if ("all".equals(status)){
            return list;
        }
        // 如果是其他状态，过滤
       return list.stream().filter(item -> status.toUpperCase().equals(item.getCurrentNode())).toList();
    }

    private void ConvertToAllInOne(String executeRequest, List<BizInstanceChatVO> convert) {
        JSONObject jsonObject = JSONObject.parseObject(executeRequest);
        if (jsonObject == null || jsonObject.getJSONArray("content") == null) {
            return;
        }
        // 转换追加办件记录
        for (Object o : jsonObject.getJSONArray("content")) {
            JSONObject o1 = JSONObject.parseObject(JSON.toJSONString(o));
            BizInstanceChatVO bizInstanceChatVO = new BizInstanceChatVO();
            bizInstanceChatVO.setCurrentNode(o1.getString("status"));
            bizInstanceChatVO.setCurrentNodeName(o1.getString("status"));
            bizInstanceChatVO.setNo(o1.getString("businessNo"));
            bizInstanceChatVO.setName(o1.getString("businessName"));
            bizInstanceChatVO.setTime(o1.getDate("businessTime"));
            bizInstanceChatVO.setUserName(o1.getString("businessObjectName"));
            bizInstanceChatVO.setChannel("allinone");
            bizInstanceChatVO.setSkipUrl(o1.getString("goToUrl"));
            bizInstanceChatVO.setMobileUrl(o1.getString("goToAPPUrl"));
            convert.add(bizInstanceChatVO);
        }
    }
}
