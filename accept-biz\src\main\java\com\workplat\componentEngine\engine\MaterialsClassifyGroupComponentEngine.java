package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.service.MaterialClassificationService;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.biz.converter.BizInstanceFieldsConvert;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialGroupVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.utils.FormFieldFilterUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 材料分类分组组件引擎
 * 处理分组材料的分类，主要功能包括：
 * 1. 材料分组管理
 * 2. 分组材料的分类处理
 * 3. 分组进度跟踪
 *
 * <AUTHOR> Fan
 * @date 2025-06-18 14:37
 */
@Slf4j
@Service
public class MaterialsClassifyGroupComponentEngine extends AbstractMaterialClassifyEngine {

    /**
     * 业务实例材料服务
     */
    private final BizInstanceMaterialService bizInstanceMaterialService;


    private final String CODE = ComponentEngineCode.MATERIAL_UPLOAD_GROUP;

    /**
     * 构造函数
     *
     * @param materialClassificationService 材料分类服务
     * @param bizInstanceMaterialService    业务实例材料服务
     * @param instanceFieldsService         实例字段服务
     * @param chatCacheUtil                 聊天缓存工具
     * @param eventPublisher                事件发布者
     */
    public MaterialsClassifyGroupComponentEngine(MaterialClassificationService materialClassificationService,
                                                 BizInstanceMaterialService bizInstanceMaterialService,
                                                 BizInstanceFieldsService instanceFieldsService,
                                                 ChatCacheUtil chatCacheUtil,
                                                 BizInstanceFieldsConvert bizInstanceFieldsConvert) {
        super(materialClassificationService, chatCacheUtil, instanceFieldsService, bizInstanceMaterialService, bizInstanceFieldsConvert);
        this.bizInstanceMaterialService = bizInstanceMaterialService;
    }

    /**
     * 执行组件操作
     * 主要流程：
     * 1. 获取材料分组信息
     * 2. 更新分组进度
     * 3. 获取当前分组的材料列表
     * 4. 处理材料分类
     *
     * @return 组件运行结果
     */
    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());

        List<BizInstanceMaterialGroupVO> materialGroup =
                bizInstanceMaterialService.getMaterialGroup(componentDataContext.getInstanceId());

        // 设置材料分组总数
        chatProcessDTO.setMaterialGroupCount(materialGroup.size());
        // 设置当前材料分组步数
        int materialGroupIndex = chatProcessDTO.getMaterialGroupIndex() == null ? 1 : chatProcessDTO.getMaterialGroupIndex();
        chatProcessDTO.setMaterialGroupIndex(materialGroupIndex);

        // 更新缓存
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

        BizInstanceMaterialGroupVO bizInstanceMaterialGroupVO = materialGroup.get(materialGroupIndex - 1);

        // 排序材料列表根据 bizInstanceMaterialGroupVO.getMaterialCodes() xx,xxx,xxx
        List<BizInstanceMaterialVO> instanceMaterialVOList = bizInstanceMaterialGroupVO.getInstanceMaterialVOList().stream()
                .sorted(Comparator.comparingInt(material -> {
                    String[] materialCodes = bizInstanceMaterialGroupVO.getMaterialCodes().split(",");
                    for (int i = 0; i < materialCodes.length; i++) {
                        if (materialCodes[i].equals(material.getMaterialCode())) {
                            return i;
                        }
                    }
                    return Integer.MAX_VALUE; // 如果未找到，放到最后
                }))
                .toList();

        bizInstanceMaterialGroupVO.setInstanceMaterialVOList(instanceMaterialVOList);


        // 组装渲染数据
        ComponentRunVO vo = new ComponentRunVO();
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(List.of(bizInstanceMaterialGroupVO))
                .build();

        vo.setRenderData(List.of(renderData));
        vo.setTips(getPropertyValue(componentDataContext, "tips"));
        return vo;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        // 填充数据
        Object submitData = componentDataContext.getSubmitData();
        log.info("MaterialsClassifyGroupComponentEngine submitData:{}", JSON.toJSONString(submitData));

        Collection<ComponentRunVO.RenderData> renderDataList =
                JSONArray.parseArray(submitData.toString(), ComponentRunVO.RenderData.class);
        ComponentRunVO.RenderData renderData = renderDataList.iterator().next();

        List<BizInstanceMaterialGroupVO> list = JSONArray.parseArray(renderData.getComponentInfo().toString(), BizInstanceMaterialGroupVO.class);
        BizInstanceMaterialGroupVO groupVO = list.getFirst();
        // 转换成List<BizInstanceMaterialSubmitDTO>
        List<BizInstanceMaterialSubmitDTO> bizInstanceMaterialSubmitDTOS =
                convertInstanceMaterialSubmitDTOS(groupVO.getInstanceMaterialVOList(), componentDataContext.getInstanceId());
        // 调用批量保存接口
        bizInstanceMaterialService.tempSubmit(bizInstanceMaterialSubmitDTOS);

        // 更新当前分组数
        chatProcessDTO.setMaterialGroupIndex(chatProcessDTO.getMaterialGroupIndex() + 1);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        if (chatProcessDTO != null
                && chatProcessDTO.getMaterialGroupIndex() != null
                && chatProcessDTO.getMaterialGroupCount() >= chatProcessDTO.getMaterialGroupIndex()) {
            return InstructionConstant.KEEP_AT_PRESENT.getCode();
        }
        // 提取字段
        List<BizInstanceMaterialVO> materialNonInit = bizInstanceMaterialService.getInstanceMaterialNonInit(componentDataContext.getInstanceId());
        Map<String, Object> map = processMaterialRecognize(materialNonInit, componentDataContext);

        if (map != null) {
            if (chatProcessDTO != null) {
                chatProcessDTO.setAiExtractFields(map);
            }
            chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);

            BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            String formObj = bizInstanceFields.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(map);
                bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFields.setFormObj(JSON.toJSONString(map));
            }
            bizInstanceFieldsService.update(bizInstanceFields);

            try {
                // 获取表单字段过滤结果
                FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                        bizInstanceFields.getFormJson(),
                        bizInstanceFields.getFormObj(),
                        false
                );
                // 判断是否还有字段可以填写
                if (!FormStepProcessor.hasFieldsToFill(filterResult.getFilteredFormJson(), bizInstanceFields.getFieldsFilterMap())) {
                    return InstructionConstant.SUCCESS_NEXT.getCode();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return super.getNextInstruction(componentDataContext);
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }
}
