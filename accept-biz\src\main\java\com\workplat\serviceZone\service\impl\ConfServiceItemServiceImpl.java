package com.workplat.serviceZone.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.serviceZone.query.ServiceItemQuery;
import com.workplat.serviceZone.service.ConfServiceItemService;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置Service实现类
 */
@Service
public class ConfServiceItemServiceImpl extends BaseServiceImpl<ConfServiceItem> implements ConfServiceItemService {

    @Autowired
    private ConfServiceItemZoneRelationService relationService;

    @Override
    public List<ConfServiceItem> getByServiceZoneId(String serviceZoneId) {
        // 1. 先查询关联关系（只查关联表，不包含详细数据）
        List<ConfServiceItemZoneRelation> relations = relationService.getByServiceZoneId(serviceZoneId);

        // 2. 提取服务清单ID列表
        List<String> serviceItemIds = relations.stream()
                .map(relation -> relation.getServiceItem().getId())
                .distinct()
                .toList();

        // 3. 批量查询服务清单详情
        List<ConfServiceItem> serviceItems = findByIds(serviceItemIds);

        // 4. 按照关联表的排序重新排序，并过滤启用状态
        Map<String, Integer> sortMap = relations.stream()
                .collect(Collectors.toMap(
                    r -> r.getServiceItem().getId(),
                    ConfServiceItemZoneRelation::getSort,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        return serviceItems.stream()
                .filter(item -> item.getEnabled() == null || item.getEnabled())
                .sorted((a, b) -> {
                    // 获取关联关系中的排序值
                    Integer sortA = sortMap.getOrDefault(a.getId(), Integer.MAX_VALUE);
                    Integer sortB = sortMap.getOrDefault(b.getId(), Integer.MAX_VALUE);
                    return sortA.compareTo(sortB);
                })
                .distinct()
                .toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnabled(String id, Boolean enabled) {
        ConfServiceItem serviceItem = queryById(id);
        if (serviceItem == null) {
            throw new BusinessException("服务清单不存在");
        }
        serviceItem.setEnabled(enabled);
        update(serviceItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveServiceItemZoneRelations(String serviceItemId, List<String> serviceZoneIds) {
        relationService.saveRelations(serviceItemId, serviceZoneIds);
    }

    @Override
    public List<ConfServiceItem> findByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }
        return queryByIds(ids);
    }
}
