package com.workplat.accept.business.third.enums;


public enum ThirdEnum {
    review("0"),
    pass("1"),
    unpass("2"),
    processed("3");

    private final String value;

    ThirdEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static String getCodeValue(String code) {
        for (ThirdEnum status : ThirdEnum.values()) {
            if (status.name().equals(code)) {
                return status.getValue();
            }
        }
        return null;
    }
}
