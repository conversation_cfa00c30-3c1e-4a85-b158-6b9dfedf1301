package com.workplat.serve.service.impl;

import com.alibaba.excel.EasyExcel;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.dict.service.SysDictionarySubService;
import com.workplat.serve.importData.TechTalentApartExcel;
import com.workplat.serve.importData.TechTalentApartExcelListener;
import com.workplat.serve.service.BizServeInfoService;
import com.workplat.serve.service.BizServeMethodService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: qian cheng
 * @package: com.workplat.serve.service.impl
 * @description: 服务信息ServiceImpl
 * @date: 2025/5/14 15:45
 */
@Service
public class BizServeInfoServiceImpl extends BaseServiceImpl<BizServeInfo> implements BizServeInfoService {



}
