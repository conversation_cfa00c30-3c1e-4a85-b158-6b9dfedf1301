package com.workplat.accept.business.home.vo;

import com.workplat.accept.business.home.entity.HomeConf;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Data;
import org.hibernate.annotations.Comment;

/**
 * @Author: SuricSun
 * @Date: 2024/10/17 9:14
 * @Desc: NoDesc
 */
@Data
public class HomeConfVO {
    /**
     * 成员变量备注见{@link HomeConf}
     */
    private String businessDesc;
    private String iconAddress;
    private String hoverIconAddress;
    private String skipUrl;
    private String isExternalLink;
    private String menuStatus;
    private String menuRemark;
    private String relatedConfMatterId;
    private String matterName;
    private String matterType;
}
