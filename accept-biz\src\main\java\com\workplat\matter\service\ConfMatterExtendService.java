package com.workplat.matter.service;

import com.workplat.gss.common.core.service.BaseService;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.vo.ConfMatterExtendVO;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.service
 * @description 事项扩展信息Service
 * @date 2025/5/20 14:04
 */
public interface ConfMatterExtendService extends BaseService<ConfMatterExtend> {

    ConfMatterExtend findByMatterId(String matterId);

    ConfMatterExtendVO findVOByMatterId(String matterId);

}
