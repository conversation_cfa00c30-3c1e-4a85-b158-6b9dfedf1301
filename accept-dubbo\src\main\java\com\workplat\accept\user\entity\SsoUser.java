package com.workplat.accept.user.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

import java.util.Date;

/**
 * @Author: Odin
 * @Date: 2024/9/23 10:48
 * @Description:
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "cus_sso_user")
public class SsoUser extends BaseEntity {

    @Comment("用户ID")
    @Column(name = "user_id", length = 32)
    private String userId;

    @Comment("用户名称")
    @Column(name = "username", length = 32)
    private String username;

    @Comment("实际姓名")
    @Column(name = "real_name", length = 32)
    private String realName;

    @Comment("性别")
    @Column(name = "gender", length = 32)
    private String gender;

    @Comment("证件类型")
    @Column(name = "certificate_type", length = 32)
    private String certificateType;

    @Comment("证件号")
    @Column(name = "certificate_number", length = 32)
    private String certificateNumber;

    @Comment("邮件")
    @Column(name = "email", length = 32)
    private String email;

    @Comment("是否工作人员 1:是 0:否")
    @Column(name = "isWorker", length = 10)
    private String isWorker;

    @Column(name = "office_phone", length = 32)
    private String officePhone;

    @Column(name = "office_phone_short", length = 32)
    private String officePhoneShort;

    @Column(name = "office_fax",length = 32)
    private String officeFax;

    @Column(name = "office_address", length = 32)
    private String officeAddress;

    @Column(name = "hire_date")
    private Date hireDate;

    @Column(name = "staff_no")
    private String staffNo;

    @Column(name = "introduction")
    private String introduction;

    @Column(name = "position")
    private String position;

    @Column(name = "birthday")
    private String birthday;

}
