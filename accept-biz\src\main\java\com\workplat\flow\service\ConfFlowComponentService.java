package com.workplat.flow.service;

import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.componentEngine.request.ComponentUsageRequest;
import com.workplat.componentEngine.vo.ComponentUsageSituationVO;
import com.workplat.gss.common.core.service.BaseService;
import org.springframework.data.domain.Page;

/**
 * @author: qian cheng
 * @package: com.workplat.flow.service
 * @description: 流程组件关联Service
 * @date: 2025/5/20 9:58
 */
public interface ConfFlowComponentService extends BaseService<ConfFlowComponent> {

    /**
     * 查询组件使用情况
     * @param request
     * @return
     */
    Page<ComponentUsageSituationVO> queryComponentUsage(ComponentUsageRequest request);
}
