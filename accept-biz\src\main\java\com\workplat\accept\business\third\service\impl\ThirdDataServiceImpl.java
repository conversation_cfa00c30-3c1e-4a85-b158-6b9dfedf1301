package com.workplat.accept.business.third.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.third.constant.ThirdConstant;
import com.workplat.accept.business.third.dto.ThirdDataQueryDto;
import com.workplat.accept.business.third.entity.ThirdData;
import com.workplat.accept.business.third.enums.ThirdEnum;
import com.workplat.accept.business.third.request.ThirdDataQueryRequest;
import com.workplat.accept.business.third.service.ThirdDataService;
import com.workplat.accept.business.third.vo.ThirdDataVO;
import com.workplat.utils.ApiSend;
import com.workplat.accept.user.entity.SsoUser;
import com.workplat.accept.user.service.SsoUserService;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.core.util.CacheDictUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Author: Odin
 * @Date: 2024/9/26 10:09
 * @Description:
 */

@Slf4j
@Service
public class ThirdDataServiceImpl extends BaseServiceImpl<ThirdData> implements ThirdDataService {

    @Resource
    private SsoUserService ssoUserService;

    @Override
    public ThirdDataVO queryThirdDataList(ThirdDataQueryDto dto) {
        return queryThirdData(dto);
    }

    private ThirdDataVO queryThirdData(ThirdDataQueryDto dto) {
        String userId = StpUtil.getLoginIdAsString();
        SsoUser ssoUser = ssoUserService.loadByUserId(userId);
        Map<String, String> urlDict = CacheDictUtils.getDictMapByCode("URL_DICT");
        dto.setStatus(ThirdEnum.getCodeValue(dto.getStatus()));
        // 查看当前用户是否工作人员
        if ("1".equals(ssoUser.getIsWorker())) {
            dto.setWorker(Boolean.TRUE);
        } else {
            dto.setWorker(Boolean.FALSE);
            dto.setCardId(ssoUser.getCertificateNumber());
        }
        String queryJson = JSONObject.toJSONString(BeanUtil.copyProperties(dto, ThirdDataQueryRequest.class));
        String response = null;
        switch (dto.getInterfaceCode()) {
            case ThirdConstant.ONE_HAND -> {
                response = ApiSend.post(urlDict.get(ThirdConstant.ONE_HAND_URL_TEST), queryJson);
                break;
            }
            case ThirdConstant.TWO_HAND -> {
                response = ApiSend.post(urlDict.get(ThirdConstant.TWO_HAND_URL_TEST), queryJson);
                break;
            }
        }
        ThirdDataVO thirdDataVO = JSONObject.parseObject(response, ThirdDataVO.class);
        return thirdDataVO;
    }

}
