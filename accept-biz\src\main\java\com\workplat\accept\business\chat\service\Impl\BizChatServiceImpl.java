package com.workplat.accept.business.chat.service.Impl;

import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.entity.BizChatMessage;
import com.workplat.accept.business.chat.repository.BizChatConversationRepository;
import com.workplat.accept.business.chat.repository.BizChatMessageRepository;
import com.workplat.accept.business.chat.service.BizChatService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Service
public class BizChatServiceImpl implements BizChatService {

}