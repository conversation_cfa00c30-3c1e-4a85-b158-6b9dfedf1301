package com.workplat.serve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "应用dto")
public class BizServeInfoDto {
    @Schema(description = "应用id")
    private String id;
    @Schema(description = "服务名称")
    private String name;
    @Schema(description = "服务类型 数据字典:serve_type")
    private String type;
    @Schema(description = "办事指南类型  数据字典:guide_type")
    private String guideType;
    @Schema(description = "办事指南地址")
    private String guideUrl;
    @Schema(description = "服务描述")
    private String description;
    @Schema(description = "服务编码")
    private String code;
    @Schema(description = "是否启用")
    private Boolean enable;
    @Schema(description = "是否第三方服务")
    private Boolean thirdParty;
    @Schema(description = "服务方式list")
    private List<BizServeMethodDto> methodList;



}
