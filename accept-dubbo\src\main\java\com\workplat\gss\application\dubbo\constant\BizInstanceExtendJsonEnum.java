package com.workplat.gss.application.dubbo.constant;

import com.alibaba.fastjson2.JSON;
import com.workplat.accept.business.chat.vo.CustomNameListVO;
import com.workplat.accept.business.chat.vo.ResultFileListVO;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.vo.BizInstanceQuotaVO;
import com.workplat.matter.vo.ConfMatterExtendVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * instance扩展字段
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/24 14:22
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BizInstanceExtendJsonEnum {

    NET_WORK_WINDOW("情形筛选网点信息", "NET_WORK_WINDOW", String.class),
    ConfMatterExtendVO("事项扩展信息", "ConfMatterExtendVO", ConfMatterExtendVO.class),
    CUSTOM_NAME_LIST("最终展示自定义列表", "CUSTOM_NAME_LIST", CustomNameListVO.class),
    RESULT_FILE_LIST("结果文件列表", "RESULT_FILE_LIST", ResultFileListVO.class),
    BIZ_INSTANCE_QUOTA_LIST("办件选择指标信息", "BIZ_INSTANCE_QUOTA_LIST", BizInstanceQuotaVO.class);

    private String value;
    private String code;
    private Class aClass;

    public Object convertObject(BizInstanceInfo bizInstanceInfo) {
        String s = bizInstanceInfo.extendJsonGet(this.getCode()) == null ?
                null : String.valueOf(bizInstanceInfo.extendJsonGet(this.getCode()));
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        if (s.startsWith("{")) {
            return JSON.parseObject(s, this.getAClass());
        } else if (s.startsWith("[")) {
            return JSON.parseArray(s, this.getAClass());
        } else {
            return s;
        }
    }


}
