//package com.workplat.serve.entity;
//
//import com.workplat.accept.business.serve.entity.BizServeInfo;
//import com.workplat.gss.common.core.entity.BaseEntity;
//import jakarta.persistence.*;
//import lombok.Getter;
//import lombok.Setter;
//import org.hibernate.annotations.Comment;
//
///**
// * <AUTHOR> cheng
// * @className: com.workplat.serve.entity
// * @description: 服务办理方式
// * @date 2025/5/14 14:07
// */
//@Setter
//@Getter
//@Entity
//@Table(name = "biz_serve_channel")
//public class BizServeMethod extends BaseEntity {
//
//    @ManyToOne
//    @JoinColumn(name = "serve_id")
//    private BizServeInfo serve;
//
//    /**
//     * 数据字典:serve_method
//     */
//    @Comment("渠道类型")
//    @Column(name = "type", length = 32)
//    private String type;
//
//    @Comment("图标id")
//    @Column(name = "name", length = 32)
//    private String iconId;
//
//    @Comment("渠道描述")
//    @Column(name = "description", length = 32)
//    private String description;
//
//    /**
//     * 数据字典:serve_certification_level
//     */
//    @Comment("认证等级")
//    @Column(name = "certification_level")
//    private int certificationLevel;
//
//    @Comment("内容")
//    @Column(name = "content")
//    private String content;
//}
