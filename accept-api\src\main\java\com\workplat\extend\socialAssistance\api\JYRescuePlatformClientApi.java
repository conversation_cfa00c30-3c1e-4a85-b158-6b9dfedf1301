package com.workplat.extend.socialAssistance.api;

import com.workplat.extend.socialAssistance.vo.AppealInfoVo;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 社会救助平台接口测试
 * 去todesk获取江阴正式环境token
 */
@Tag(name = "社会救助平台接口测试")
@RestController
@RequestMapping("/api/social/assistance")
public interface JYRescuePlatformClientApi {

    @Operation(summary = "获取认证凭证")
    @GetMapping("/getAuthToken")
    ResponseData<String> getAuthToken(String token);

    @Operation(summary = "提交诉求申请")
    @PostMapping("/submitAppeal")
    ResponseData submitAppeal(String token);

    @Operation(summary = "获取诉求申请详情")
    @GetMapping("/getAppealInfo")
    ResponseData<AppealInfoVo> getAppealInfo(String id, String token);

}
