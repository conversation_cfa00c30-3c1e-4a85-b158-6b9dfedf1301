package com.workplat.serviceZone.converter;

import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.serviceZone.dto.ConfServiceZoneDTO;
import com.workplat.serviceZone.vo.ConfServiceZoneVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置转换器
 */
@Component
public class ConfServiceZoneConverter {

    /**
     * Entity转VO
     */
    public ConfServiceZoneVO convert(ConfServiceZone entity) {
        if (entity == null) {
            return null;
        }
        ConfServiceZoneVO vo = new ConfServiceZoneVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    /**
     * Entity列表转VO列表
     */
    public List<ConfServiceZoneVO> convert(List<ConfServiceZone> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * DTO转Entity
     */
    public ConfServiceZone convert(ConfServiceZoneDTO dto) {
        if (dto == null) {
            return null;
        }
        ConfServiceZone entity = new ConfServiceZone();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * DTO转Entity（更新）
     */
    public void updateEntity(ConfServiceZoneDTO dto, ConfServiceZone entity) {
        if (dto == null || entity == null) {
            return;
        }
        BeanUtils.copyProperties(dto, entity, "id,createTime");
    }
}
