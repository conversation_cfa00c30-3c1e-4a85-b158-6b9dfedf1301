package com.workplat.accept.business.chat.listener;

import com.workplat.accept.business.chat.entity.BizChatMessage;
import com.workplat.accept.business.chat.event.ChatMessageEvent;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.service.BizChatMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ChatMessageListener {

    private final BizChatMessageService bizChatMessageService;
    private final BizChatConversationService bizChatConversationService;

    public ChatMessageListener(BizChatMessageService bizChatMessageService,
                               BizChatConversationService bizChatConversationService) {
        this.bizChatMessageService = bizChatMessageService;
        this.bizChatConversationService = bizChatConversationService;
    }

    @Async
    @EventListener
    public void handleChatMessage(ChatMessageEvent event) {
        try {
            if (StringUtils.isNoneBlank(event.getContent())) {
                BizChatMessage message = new BizChatMessage();
                message.setConversationId(event.getConversationId());
                message.setContent(event.getContent());
                message.setSender(event.getSender());

                bizChatMessageService.createMessage(message);
                // AI消息才更新会话
                if ("ASSISTANT".equals(event.getSender())){
                    bizChatConversationService.updateConversation(event.getConversationId());
                }
                log.info("Successfully processed chat message event for conversation: {}", event.getConversationId());
            }
        } catch (Exception e) {
            log.error("Error processing chat message event: {}", e.getMessage(), e);
        }
    }
}