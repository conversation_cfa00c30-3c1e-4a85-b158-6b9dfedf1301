package com.workplat.serve.importData;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.workplat.gss.common.core.exception.BusinessException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/9/19
 */
public class TechTalentApartExcelListener extends AnalysisEventListener<TechTalentApartExcel> {

    private int skipCount = 2; // 要跳过的行数
    private boolean shouldSkip = true; // 是否应该跳过当前行

    private final ExcelValidator<TechTalentApartExcel> validator;
    // 提供一个方法来获取所有的验证错误信息
    @Getter
    private final List<String> validationErrors = new ArrayList<>();

    // 构造方法，初始化 ExcelValidator，beginIndex 可以自定义，也可以设为 0
    public TechTalentApartExcelListener() {
        this.validator = new ExcelValidator<>(skipCount);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        super.onException(exception, context);
    }

    private boolean isNumeric(String strNum) {
        if (strNum == null) {
            return true;
        }
        try {
            Double.parseDouble(strNum);
        } catch (NumberFormatException nfe) {
            return false;
        }
        return true;
    }

    @Override
    public void invoke(TechTalentApartExcel data, AnalysisContext context) {
//        if (shouldSkip) {
//            if (skipCount > 0) {
//                skipCount--;
//                shouldSkip = skipCount > 0;
//                return; // 跳过这一行
//            }
//        }
        // 对当前行数据进行验证
        String validationResult = validator.validate(data);
        // 如果有验证错误，记录错误信息
        if (validationResult != null) {
            validationErrors.add(validationResult);
        }

    }

    private static boolean checkABCFormat(String input) {
        // 正则表达式模式
        String regex = "^([\u4e00-\u9fffA-Za-z0-9/]+)$";
        Pattern pattern = Pattern.compile(regex);

        // 创建Matcher对象
        Matcher matcher = pattern.matcher(input);

        // 使用Matcher对象检查输入字符串是否符合模式
        return matcher.matches();
    }

    public static void main(String[] args) {
        System.out.println(checkABCFormat("全部分组/"));
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        super.extra(extra, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (!validationErrors.isEmpty()) {
            throw new BusinessException("Excel数据验证失败: " + validationErrors);
        }
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return super.hasNext(context);
    }

}
