package com.workplat.accept.business.home.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

/**
 * @Author: Odin
 * @Date: 2024/9/24 16:17
 * @Description:首页配置信息
 */

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "cus_home_conf")
@Getter
@Setter
public class HomeConf extends BaseEntity {

    @Comment("简介")
    @Column(name = "business_desc", length = 200)
    private String businessDesc;

    @Comment("默认图标")
    @Column(name = "icon_address", length = 200)
    private String iconAddress;

    @Comment("点亮图标")
    @Column(name = "hover_icon_address", length = 200)
    private String hoverIconAddress;

    @Comment("跳转地址")
    @Column(name = "skip_url", length = 200)
    private String skipUrl;

    @Comment("是否外链 0:否 1:是")
    @Column(name = "is_external_link", length = 32)
    private Boolean isExternalLink;

    @Comment("菜单状态 0:维护中 1:正常")
    @Column(name = "menu_status")
    private String menuStatus;

    @Comment("菜单备注 禁用后提示内容")
    @Column(name = "menu_remark")
    private String menuRemark;

    @Comment("所属的事项id")
    @Column(name = "related_conf_matter_id", length = 32)
    private String relatedConfMatterId;
}
