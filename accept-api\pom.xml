<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.workplat</groupId>
        <artifactId>ai-central-platform</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>accept-api</artifactId>
    <packaging>jar</packaging>
    <description>api接口</description>

    <dependencies>
        <!-- accept-dubbo -->
        <dependency>
            <groupId>com.workplat</groupId>
            <artifactId>accept-dubbo</artifactId>
            <version>${accept.cloud.version}</version>
        </dependency>
    </dependencies>
</project>
