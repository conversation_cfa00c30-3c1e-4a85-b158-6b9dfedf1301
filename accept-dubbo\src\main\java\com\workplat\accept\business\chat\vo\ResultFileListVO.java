package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(name = "结果文件列表模型")
public class ResultFileListVO {

    @Schema(description = "文件名")
    private String name;

    @Schema(description = "文件预览地址")
    private String previewUrl;

    @Schema(description = "文件下载地址")
    private String downloadUrl;
}
