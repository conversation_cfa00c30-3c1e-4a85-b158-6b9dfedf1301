package com.workplat.serviceZone.converter;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.serviceZone.dto.ConfServiceItemZoneRelationDTO;
import com.workplat.serviceZone.vo.ConfServiceItemZoneRelationVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联关系转换器
 */
@Component
public class ConfServiceItemZoneRelationConverter {

    /**
     * Entity转VO
     */
    public ConfServiceItemZoneRelationVO convert(ConfServiceItemZoneRelation entity) {
        if (entity == null) {
            return null;
        }
        ConfServiceItemZoneRelationVO vo = new ConfServiceItemZoneRelationVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 设置服务清单信息
        if (entity.getServiceItem() != null) {
            vo.setServiceItemId(entity.getServiceItem().getId());
            vo.setServiceItemName(entity.getServiceItem().getName());
        }
        
        // 设置专区信息
        if (entity.getServiceZone() != null) {
            vo.setServiceZoneId(entity.getServiceZone().getId());
            vo.setServiceZoneName(entity.getServiceZone().getName());
        }
        
        return vo;
    }

    /**
     * Entity列表转VO列表
     */
    public List<ConfServiceItemZoneRelationVO> convert(List<ConfServiceItemZoneRelation> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * DTO转Entity
     */
    public ConfServiceItemZoneRelation convert(ConfServiceItemZoneRelationDTO dto) {
        if (dto == null) {
            return null;
        }
        ConfServiceItemZoneRelation entity = new ConfServiceItemZoneRelation();
        BeanUtils.copyProperties(dto, entity);
        
        // 设置服务清单
        if (dto.getServiceItemId() != null) {
            ConfServiceItem serviceItem = new ConfServiceItem();
            serviceItem.setId(dto.getServiceItemId());
            entity.setServiceItem(serviceItem);
        }
        
        // 设置专区
        if (dto.getServiceZoneId() != null) {
            ConfServiceZone serviceZone = new ConfServiceZone();
            serviceZone.setId(dto.getServiceZoneId());
            entity.setServiceZone(serviceZone);
        }
        
        return entity;
    }

    /**
     * DTO列表转Entity列表
     */
    public List<ConfServiceItemZoneRelation> convertFromDTO(List<ConfServiceItemZoneRelationDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }
}