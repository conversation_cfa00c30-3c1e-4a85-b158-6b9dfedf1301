package com.workplat.utils;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.gss.common.core.util.RedisUtil;
import org.springframework.stereotype.Component;

@Component
public class ChatCacheUtil {

    private final RedisUtil redisUtil;

    public ChatCacheUtil(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    private final String CHAT_PROCESS_KEY = "chat:process:data:";


    /**
     * 缓存会话处理数据
     *
     * @param key            唯一键
     * @param chatProcessDTO 数据
     */
    public void set(String key, ChatProcessDTO chatProcessDTO) {
        redisUtil.set(CHAT_PROCESS_KEY + key, JSONObject.toJSONString(chatProcessDTO), 60 * 60 * 24 * 7);
    }

    /**
     * 缓存会话处理数据
     *
     * @param key            唯一键
     * @param chatProcessDTO 数据
     * @param time           过期时间 单位秒
     */
    public void set(String key, ChatProcessDTO chatProcessDTO, long time) {
        redisUtil.set(CHAT_PROCESS_KEY + key, JSONObject.toJSONString(chatProcessDTO), time);
    }

    /**
     * 获取会话处理数据
     *
     * @param key 唯一键
     * @return 数据
     */
    public ChatProcessDTO get(String key) {
        return JSONObject.parseObject((String) redisUtil.get(CHAT_PROCESS_KEY + key), ChatProcessDTO.class);
    }

}
