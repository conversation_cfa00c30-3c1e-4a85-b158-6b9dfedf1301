package com.workplat.serviceZone.service.impl;

import cn.hutool.core.map.MapUtil;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.gss.common.core.util.EntityValidateUtils;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import com.workplat.serviceZone.service.ConfServiceZoneService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置Service实现类
 */
@Service
public class ConfServiceZoneServiceImpl extends BaseServiceImpl<ConfServiceZone> implements ConfServiceZoneService {

    @Autowired
    private ConfServiceItemZoneRelationService relationService;

    @Override
    public void entityValidate(ConfServiceZone entity) {
        if (EntityValidateUtils.duplicate(this, entity.getId(), "name", entity.getName())) {
            throw new BusinessException("专区名称：" + entity.getName() + " 已存在");
        }
    }

    @Override
    public ConfServiceZone getByName(String name) {
        return queryForSingle(MapUtil.<String, Object>builder()
                .put("=(name)", name)
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnabled(String id, Boolean enabled) {
        ConfServiceZone serviceZone = queryById(id);
        if (serviceZone == null) {
            throw new BusinessException("专区不存在");
        }
        serviceZone.setEnabled(enabled);
        update(serviceZone);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveZoneServiceItemRelations(String serviceZoneId, List<String> serviceItemIds) {
        relationService.saveRelationsByZone(serviceZoneId, serviceItemIds);
    }
}
