package com.workplat.accept.business.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class BizChatConversationDTO {

    @NotBlank(message = "用户id不能为空")
    @Schema(description = "用户id")
    String userId;

    @NotBlank(message = "渠道不能为空")
    @Schema(description = "渠道（pc/mobile）")
    String channel;

    @Schema(description = "是否办件初始化")
    Boolean isInit = true;
}
