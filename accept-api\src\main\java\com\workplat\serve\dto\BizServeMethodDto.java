package com.workplat.serve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "服务方式dto")
public class BizServeMethodDto {
    @Schema(description = "服务方式id")
    private String id;
    @Schema(description = "排序")
    private Integer sort;
    @Schema(description = "应用实体")
    private String serveInfoId;
    @Schema(description = "渠道类型 数据字典:serve_method")
    private String type;
    @Schema(description = "图标id")
    private String iconId;
    @Schema(description = "渠道描述")
    private String description;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "内容")
    private Integer certificationLevel;


}
