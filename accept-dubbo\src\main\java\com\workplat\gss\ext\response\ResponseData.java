package com.workplat.gss.ext.response;

import com.alibaba.fastjson2.JSON;
import com.workplat.gss.common.core.constant.ResponseCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.io.Serializable;

/**
 * ResponseEntity
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Getter
@Setter
@Schema(description = "响应数据")
public class ResponseData<T> implements Serializable {

    @Schema(description = "状态码", example = "200")
    private int code;

    @Schema(description = "是否成功", example = "true")
    private boolean success;

    @Schema(description = "响应信息", example = "请求成功")
    private String message;

    @Schema(description = "错误信息", example = "报错信息")
    private String errorMessage;

    @Schema(description = "响应数据")
    private T data;

    private ResponseData(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    private ResponseData(int code, boolean success, String message, T data) {
        this.code = code;
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public static ResponseDataBuilder code(ResponseCode code) {
        return new DefaultBuilder(code);
    }

    public static ResponseDataBuilder code(int status) {
        return new DefaultBuilder(status);
    }

    public static ResponseDataBuilder success() {
        return code(ResponseCode.SUCCESS);
    }

    public static <T> ResponseData<T> success(@Nullable T data) {
        return success().data(data);
    }

    public static <T> ResponseData<T> success(String message, @Nullable T data) {
        return success().message(message).data(data);
    }

    public static ResponseDataBuilder error() {
        return code(ResponseCode.INTERNAL_SERVER_ERROR);
    }

    public static ResponseData<Void> error(String message) {
        return error().message(message).build();
    }

    public String toJsonString() {
        return JSON.toJSONString(this);
    }

    public interface ResponseDataBuilder {

        ResponseDataBuilder code(ResponseCode code);

        ResponseDataBuilder message(String message);

        ResponseDataBuilder error(String errorMessage);

        <T> ResponseData<T> build();

        <T> ResponseData<T> data(T data);

        String toJsonString();
    }

    private static class DefaultBuilder implements ResponseDataBuilder {

        private int code;

        private boolean success;

        private String message;

        private String errorMessage;

        private Object data;

        public DefaultBuilder(ResponseCode code) {
            this.code = code.getCode();
            this.success = ResponseCode.SUCCESS == code ? true : false;
            this.message = code.getMessage();
        }

        public DefaultBuilder(int code) {
            this.code = code;
        }

        public DefaultBuilder(String message) {
            this.message = message;
        }

        public ResponseDataBuilder code(ResponseCode code) {
            this.code = code.getCode();
            this.message = code.getMessage();
            return this;
        }

        public ResponseDataBuilder message(String message) {
            this.message = message;
            return this;
        }

        @Override
        public ResponseDataBuilder error(String errorMessage) {
            this.errorMessage = errorMessage;
            return this;
        }

        public <T> ResponseData<T> build() {
            return this.data(null);
        }

        public <T> ResponseData<T> data(T data) {
            return new ResponseData<>(this.code, this.success, message, data);
        }

        public String toJsonString() {
            return JSON.toJSONString(this);
        }
    }
}
