package com.workplat.push.power.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;


/**
 * @ClassName: MattersDeclareFormRequest
 * @Description:
 * @Author: Yang Fan
 * @Date: 2025-03-09 16:28
 * @Version
 **/
@Data
public class MattersDeclareFormRequest {

    private String id;

    /**
     * 事项名称
     */
    private String taskName;

    /**
     * 业务类型
     */
    private String taskType;

    /**
     * 事项id
     */
    private String basicId;

    /**
     * 事项所属部门
     */
    private String taskDept;

    /**
     * 事项所属部门
     */
    private String taskDeptCode;

    /**
     * 业务流水号
     */
    private String acceptanceSn;

    /**
     * 项目名称
     */
    private String projectName;

    private String operator;

    /**
     * 项目所在区域
     */
    private String projectArea;

    /**
     * 申请人类型
     */
    private String applicantType;

    /**
     * 申请人基本信息
     */
    private String applicantName;

    /**
     * 申请人证件类型
     */
    private String applicantPaperType;

    /**
     * 申请人证件编号
     */
    private String applicantPaperCode;

    /**
     * 申请人手机号码
     */
    private String applicantMobile;

    /**
     * 申请人联系地址
     */
    private String applicantAddress;

    /**
     * 邮政编码
     */
    private String applicantZipCode;

    /**
     * 法定代表人姓名
     */
    private String operManName;

    /**
     * 法定代表人身份证号码
     */
    private String operManPaperCode;

    /**
     * 是否本人办理
     */
    private boolean oneSelfHandle;

    /**
     * 联系人姓名
     */
    private String linkManName;

    /**
     * 联系人证件类型
     */
    private String linkManPaperType;

    /**
     * 经办人证件编码
     */
    private String linkManPaperCode;

    /**
     * 经办人手机号码
     */
    private String linkManMobile;

    /**
     * 经办人联系电话
     */
    private String linkManPhone;

    /**
     * 经办人邮政编码
     */
    private String linkManZipCode;

    /**
     * 经办人联系地址
     */
    private String linkManAddress;

    /**
     * 是否邮寄
     */
    private boolean sendEms;

    /**
     * EMS 接收人姓名
     */
    private String receiverName;

    /**
     * EMS 接收人地址
     */
    private String receiverAddress;

    /**
     * 接收人电话
     */
    private String receiverMobile;

    /**
     * 接收人邮政编码
     */
    private String receiverZipCode;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 一网通办用户id
     */
    private String webUserId;


    /**
     * 业务所在环节
     */
    private String acceptanceStage;

    /**
     * 业务所在环节code
     */
    String progressStageCode;


    /**
     * 业务办理编码
     */
    private String businessCode;
}
