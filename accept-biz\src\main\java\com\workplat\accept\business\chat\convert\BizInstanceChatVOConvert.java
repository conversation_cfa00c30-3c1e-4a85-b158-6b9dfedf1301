package com.workplat.accept.business.chat.convert;

import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.vo.BizInstanceChatVO;
import com.workplat.gss.common.core.converter.BaseConverter;
import org.springframework.stereotype.Component;

@Component
public class BizInstanceChatVOConvert implements BaseConverter<BizInstanceChat, BizInstanceChatVO> {

    public BizInstanceChatVO convert(BizInstanceChat source) {
        BizInstanceChatVO target = new BizInstanceChatVO();
        target.setId(source.getId());
        target.setInstanceId(source.getInstance().getId());
        target.setCurrentNode(source.getCurrentNode());
        target.setCurrentNodeName(source.getCurrentNodeName());
        target.setNo(source.getInstance().getAcceptedNum());
        target.setName(source.getInstance().getMatterName());
        target.setTime(source.getInstance().getCreateTime());
        target.setUserName(source.getInstance().getApplicationName());
        target.setChannel("local");
        return target;
    }
}
