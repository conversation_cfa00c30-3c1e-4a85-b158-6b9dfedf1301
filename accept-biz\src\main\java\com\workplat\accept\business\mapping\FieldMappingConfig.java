package com.workplat.accept.business.mapping;

import com.alibaba.fastjson2.JSON;
import lombok.Getter;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Getter
@Configuration
public class FieldMappingConfig {
    
    private String thirdToTemplate;  // 第三方字段到模板字段的映射
    private String templateToForm;        // 模板字段到表单字段的映射
    
    public FieldMappingConfig() {

        // 表单映射 示例mappings{ 模板编码：表单编码}
        Map<String, String> templateToForm = new HashMap<>();
        templateToForm.put("contractNo", "no");
        templateToForm.put("projectName", "name");


        // 1. 处理数组数据的配置
        Map<String, String> arrayConfig = new HashMap<>();
        arrayConfig.put("data[*].fwbh", "contractNo");
        arrayConfig.put("data[*].xmmc", "projectName");
        arrayConfig.put("data[*].fwzmj", "area");

// 2. 处理对象数据的配置
        Map<String, String> objectConfig = new HashMap<>();
        objectConfig.put("data.fwbh", "contractNo");
        objectConfig.put("data.xmmc", "projectName");
        objectConfig.put("data.fwzmj", "area");

// 3. 处理直接对象的配置
        Map<String, String> directConfig = new HashMap<>();
        directConfig.put("fwbh", "contractNo");
        directConfig.put("xmmc", "projectName");
        directConfig.put("fwzmj", "area");

// 4. 处理嵌套对象的配置
        Map<String, String> nestedConfig = new HashMap<>();
        nestedConfig.put("result.data.fwbh", "contractNo");
        nestedConfig.put("result.data.xmmc", "projectName");
        nestedConfig.put("result.data.fwzmj", "area");

// 保存配置
        ConfFieldMapping config = ConfFieldMapping.builder()
                .thirdToTemplate(JSON.toJSONString(arrayConfig))
                .templateToForm(JSON.toJSONString(templateToForm))
                .build();

        this.thirdToTemplate = config.getThirdToTemplate();
        this.templateToForm = config.getTemplateToForm();

    }



}