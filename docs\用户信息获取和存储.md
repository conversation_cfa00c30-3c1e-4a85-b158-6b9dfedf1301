# 用户信息获取和存储

## 用户信息和办件信息取值说明

在系统中，用户信息分为个人用户和企业用户两种类型，根据用户类型不同，需要获取的信息字段也不同。办件信息是基于用户信息来填充的。

### 1. 用户信息结构

用户信息存储在<font color='blue'>CommonUserVO</font> 对象中，主要字段包括：

#### 个人用户字段：
- <font color='oragre'>userType</font>: 用户类型，个人为"person"
- <font color='oragre'>realName</font>: 真实姓名
- <font color='oragre'>mobile</font>: 手机号码
- <font color='oragre'>certificateNumber</font>: 身份证号码

#### 企业用户字段：
- <font color='oragre'>userType</font>: 用户类型，企业为"enterprise"
- <font color='oragre'>realName</font>: 企业用户的真实姓名
- <font color='oragre'>mobile</font>: 企业用户手机号码
- <font color='oragre'>certificateNumber</font>: 统一社会信用代码

企业用户还包含一个 <font color='blue'>AioEnterpriseUserVO</font> 子对象，包含以下字段：
- <font color='oragre'>enterpriseName</font>: 企业名称
- <font color='oragre'>enterpriseLicense</font>: 统一社会信用代码
- <font color='oragre'>legalPersonName</font>: 法人姓名
- <font color='oragre'>legalPersonPaperCard</font>: 法人身份证号码
- <font color='oragre'>linkManName</font>: 经办人姓名
- <font color='oragre'>linkManPhone</font>: 经办人手机号码
- <font color='oragre'>linkManPaperCard</font>: 经办人身份证号码

### 2. 办件信息填充规则

根据用户类型，系统会填充 <font color='blue'>BizInstanceInfo</font> 对象的不同字段：

#### 个人用户办件信息填充：
- <font color='oragre'>applicationType</font>: "自然人"
- <font color='oragre'>applicationName</font>: 用户真实姓名
- <font color='oragre'>applicationPhone</font>: 用户手机号码
- <font color='oragre'>applicationCertificateType</font>: "身份证"
- <font color='oragre'>applicationCertificateCode</font>: 用户身份证号码
- <font color='oragre'>linkName</font>: 用户真实姓名（经办人姓名）
- <font color='oragre'>linkPhone</font>: 用户手机号码（经办人电话）
- <font color='oragre'>linkCertificateType</font>: "身份证"（经办人证件类型）
- <font color='oragre'>linkCertificateCode</font>: 用户身份证号码（经办人证件号码）

#### 企业用户办件信息填充：
- <font color='oragre'>applicationType</font>: "法人"
- <font color='oragre'>applicationName</font>: 用户真实姓名（公司名称）
- <font color='oragre'>applicationPhone</font>: 用户手机号码
- <font color='oragre'>applicationCertificateType</font>: "统一社会信用代码"
- <font color='oragre'>applicationCertificateCode</font>: 统一社会信用代码

经办人信息：
- <font color='oragre'>linkName</font>: 经办人姓名
- <font color='oragre'>linkPhone</font>: 经办人手机号码
- <font color='oragre'>linkCertificateType</font>: "身份证"
- <font color='oragre'>linkCertificateCode</font>: 经办人身份证号码

法人信息：
- <font color='oragre'>legalPersonName</font>: 法人姓名
- <font color='oragre'>legalPersonCertificateType</font>: "身份证"
- <font color='oragre'>legalPersonCertificateCode</font>: 法人身份证号码

企业信息：
- <font color='oragre'>companyName</font>: 企业名称
- <font color='oragre'>socialCreditCode</font>: 统一社会信用代码

### 3. 获取方式

通过 `aioServiceHelper.getCommonUserInfo(token)` 方法获取用户信息，然后根据 <font color='blue'>userType</font> 字段判断用户类型，并按照上述规则填充办件信息。

### 4. 脚本使用
脚本使用示例：
```java
  public FormFillBackOutput formFillBack(FormFillBackInput input) {
    FormFillBackOutput output = new FormFillBackOutput();
    Map<String, String> formMap = new HashMap<>();

    BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
    BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(input.getInstanceId());

    // 个人
    formMap.put("申请人", bizInstanceInfo.getApplicationName());
    formMap.put("证件号码", bizInstanceInfo.getApplicationCertificateCode());
    formMap.put("联系电话", bizInstanceInfo.getApplicationPhone());

    formMap.put("经办人姓名", bizInstanceInfo.getLinkName());
    formMap.put("经办人身份证", bizInstanceInfo.getLinkCertificateCode());
    formMap.put("经办人手机", bizInstanceInfo.getLinkPhone());

    // 企业
    formMap.put("企业名称", bizInstanceInfo.getCompanyName());
    formMap.put("统一社会信用代码", bizInstanceInfo.getSocialCreditCode());

    formMap.put("法定代表人姓名", bizInstanceInfo.getLegalPersonName());
    formMap.put("法定代表人身份证号码", bizInstanceInfo.getLegalPersonCertificateCode());

    formMap.put("联系人姓名", bizInstanceInfo.getLinkName());
    formMap.put("联系人手机", bizInstanceInfo.getLinkPhone());
    formMap.put("联系人身份证号码", bizInstanceInfo.getLinkCertificateCode());

    output.setFormMap(formMap);
    return output;
}
```