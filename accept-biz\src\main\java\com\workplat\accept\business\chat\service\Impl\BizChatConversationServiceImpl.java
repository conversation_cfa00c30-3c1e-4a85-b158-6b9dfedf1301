package com.workplat.accept.business.chat.service.Impl;

import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.convert.BizChatConversationVOConvert;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.vo.BizChatConversationVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BizChatConversationServiceImpl extends BaseServiceImpl<BizChatConversation> implements BizChatConversationService {

    final BizChatConversationVOConvert bizChatConversationVOConvert;

    public BizChatConversationServiceImpl(BizChatConversationVOConvert bizChatConversationVOConvert) {
        this.bizChatConversationVOConvert = bizChatConversationVOConvert;
    }


    @Override
    public BizChatConversation createConversation(BizChatConversation conversation) {
        conversation.setMessageCount(0);
        return super.save(conversation);
    }

    @Override
    public void updateConversation(String conversationId) {
        BizChatConversation bizChatConversation = super.queryById(conversationId);
        if (bizChatConversation != null) {
            bizChatConversation.setUpdateTime(new Date());
            // 向下取整, 在整数运算中，3/2的结果就是1
            int newMessageCount = (bizChatConversation.getMessageCount() + 1) / 2;
            bizChatConversation.setMessageCount(newMessageCount == 0 ? 1 : newMessageCount);
            super.update(bizChatConversation);
        } else {
            // 如果查询结果为空，记录日志并抛出异常
            log.error("Conversation with ID {} not found", conversationId);
            throw new RuntimeException("Conversation not found");
        }
    }
}