package com.workplat.serviceZone.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置VO
 */
@Data
public class ConfServiceItemVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 发送内容
     */
    private String sendContent;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 分类标签
     */
    private String category;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 是否热门推荐
     */
    private Boolean isHotRecommend;

    /**
     * 排序号（来自BaseEntity）
     */
    private Integer sort;

    /**
     * 关联的专区ID列表（多对多关系）
     */
    private List<String> serviceZoneIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
