# AI中央平台License验证系统使用指南

## 功能概述

本系统为AI中央平台提供了专业的License验证功能，支持RSA数字签名验证、硬件绑定验证、功能权限控制等企业级授权验证机制。

## 功能特性

- 🔐 **RSA数字签名验证**：验证License文件完整性，防止篡改
- 💻 **硬件绑定验证**：验证License与机器指纹的绑定关系
- ⏰ **时间验证**：验证License的生效期和过期期
- 🎛️ **功能权限控制**：基于License控制不同功能模块的访问权限
- 📁 **License文件解析**：支持加密License文件的解析和验证
- 🔄 **实时验证**：启动时和运行时的License有效性验证
- 📊 **状态监控**：详细的License状态查询和监控接口
- ⚡ **性能优化**：验证结果缓存机制，提高验证性能

## 技术实现

### 核心组件

1. **LicenseInfo** - License信息数据模型
2. **LicenseValidationResult** - 验证结果模型
3. **LicenseValidator** - License验证器
4. **LicenseValidationInterceptor** - License验证拦截器
5. **LicenseConfig** - License配置管理类
6. **LicenseManagementController** - License状态查询接口
7. **MachineCodeUtil** - 硬件指纹工具
8. **RSASignatureUtil** - 数字签名验证工具

### 拦截器优先级

```
1. LicenseValidationInterceptor (order=1) - License验证
2. SaInterceptor (order=2) - Sa-Token认证
3. ApplicationMvcInterceptor (order=3) - 应用拦截器
```

## 配置说明

### Nacos配置

在Nacos配置中心的应用配置文件中添加以下配置：

```yaml
# 授权配置
license:
  # 是否启用授权验证（默认：true）
  enabled: true
  
  # 授权开始时间（格式：yyyy-MM-dd HH:mm:ss）
  start-time: "2025-01-01 00:00:00"
  
  # 授权有效期（天数，默认：30天）
  valid-days: 30
  
  # 授权过期提示信息
  expired-message: "系统授权已过期，请联系管理员续期"
```

### 配置项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `license.enabled` | Boolean | true | 是否启用授权验证 |
| `license.start-time` | String | - | 授权开始时间，格式：yyyy-MM-dd HH:mm:ss |
| `license.valid-days` | Integer | 30 | 授权有效期天数 |
| `license.expired-message` | String | 系统授权已过期，请联系管理员续期 | 过期提示信息 |

## 使用方法

### 1. 配置License验证

在Nacos配置中心中设置License验证配置：

```yaml
license:
  validation-enabled: true
  file-path: "./license.lic"
  public-key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA..."
  product-name: "AI中央平台"
```

### 2. 查询License状态

调用License状态查询接口：

```http
GET /api/license/status
```

响应示例：
```json
{
  "code": 200,
  "success": true,
  "message": "请求成功",
  "data": {
    "enabled": true,
    "startTime": "2025-01-01 00:00:00",
    "endTime": "2025-01-31 00:00:00",
    "validDays": 30,
    "remainingDays": 15,
    "expired": false,
    "status": "正常",
    "statusMessage": "授权正常，剩余 15 天"
  }
}
```

### 3. License过期处理

当License过期后，所有API请求将返回：

```json
{
  "code": 4001,
  "success": false,
  "message": "系统授权已过期，请联系管理员续期，已过期 5 天",
  "data": null
}
```

## 状态说明

### 授权状态

- **正常** - 授权有效，剩余时间充足
- **即将过期** - 剩余时间不足7天
- **已过期** - 授权已过期
- **已禁用** - 授权验证已禁用
- **配置错误** - 授权配置有误

### 响应码

| 状态码 | 说明 |
|--------|------|
| 4001 | 授权已过期 |
| 4002 | 授权配置错误 |
| 4003 | 授权验证失败 |
| 4004 | 授权未配置 |
| 2001 | 授权即将过期（警告） |

## 常见场景

### 1. 生产环境部署

```yaml
license:
  enabled: true
  start-time: "2025-01-01 00:00:00"
  valid-days: 365  # 一年授权
  expired-message: "系统授权已过期，请联系技术支持续期"
```

### 2. 测试环境

```yaml
license:
  enabled: false  # 禁用授权验证
```

### 3. 临时禁用

```yaml
license:
  enabled: false  # 临时禁用，无需重启应用
```

## 注意事项

1. **时间格式**：授权开始时间必须严格按照 `yyyy-MM-dd HH:mm:ss` 格式
2. **配置刷新**：修改配置后需要等待配置刷新或重启应用
3. **系统时间**：确保服务器系统时间准确
4. **日志监控**：关注授权相关的日志信息
5. **提前续期**：建议在授权到期前提前续期

## 故障排除

### 1. 配置错误

**问题**：授权配置格式错误
**解决**：检查时间格式和配置项拼写

### 2. 时间不准确

**问题**：服务器时间与实际时间不符
**解决**：同步服务器时间

### 3. 配置不生效

**问题**：修改配置后不生效
**解决**：检查Nacos配置是否正确发布，或重启应用

## 开发调试

### 测试授权过期

```yaml
license:
  enabled: true
  start-time: "2024-01-01 00:00:00"
  valid-days: 1
  expired-message: "测试环境：授权已过期"
```

### 查看日志

授权验证相关日志会记录在应用日志中，关键字：`授权`、`license`

## 扩展功能

如需扩展功能，可以考虑：

1. 添加授权续期接口
2. 支持多级授权（不同功能模块不同授权期限）
3. 添加授权使用统计
4. 集成外部授权服务

---

**注意**：此功能涉及系统核心安全机制，请谨慎配置和使用。
