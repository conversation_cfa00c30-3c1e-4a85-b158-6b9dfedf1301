package com.workplat.serviceZone.repository;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.gss.common.core.repository.BaseRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联表Repository接口
 */
@Repository
public interface ConfServiceItemZoneRelationRepository extends BaseRepository<ConfServiceItemZoneRelation> {

    /**
     * 根据专区ID查询关联关系（只查关联表数据，不JOIN FETCH）
     * @param serviceZoneId 专区ID
     * @return 关联关系列表
     */
    @Query("SELECT r FROM ConfServiceItemZoneRelation r " +
           "WHERE r.serviceZone.id = :serviceZoneId AND r.deleted = false " +
           "ORDER BY r.sort ASC")
    List<ConfServiceItemZoneRelation> findByServiceZoneIdAndDeletedFalseOrderBySortAsc(@Param("serviceZoneId") String serviceZoneId);

    /**
     * 根据服务清单ID查询关联关系（只查关联表数据，不JOIN FETCH）
     * @param serviceItemId 服务清单ID
     * @return 关联关系列表
     */
    @Query("SELECT r FROM ConfServiceItemZoneRelation r " +
           "WHERE r.serviceItem.id = :serviceItemId AND r.deleted = false")
    List<ConfServiceItemZoneRelation> findByServiceItemIdAndDeletedFalse(@Param("serviceItemId") String serviceItemId);

    /**
     * 根据专区ID查询关联关系（包含完整的关联数据，用于转换VO）
     * @param serviceZoneId 专区ID
     * @return 关联关系列表
     */
    @Query("SELECT r FROM ConfServiceItemZoneRelation r " +
           "JOIN FETCH r.serviceItem " +
           "JOIN FETCH r.serviceZone " +
           "WHERE r.serviceZone.id = :serviceZoneId AND r.deleted = false " +
           "ORDER BY r.sort ASC")
    List<ConfServiceItemZoneRelation> findByServiceZoneIdWithDetails(@Param("serviceZoneId") String serviceZoneId);

    /**
     * 根据服务清单ID查询关联关系（包含完整的关联数据，用于转换VO）
     * @param serviceItemId 服务清单ID
     * @return 关联关系列表
     */
    @Query("SELECT r FROM ConfServiceItemZoneRelation r " +
           "JOIN FETCH r.serviceItem " +
           "JOIN FETCH r.serviceZone " +
           "WHERE r.serviceItem.id = :serviceItemId AND r.deleted = false")
    List<ConfServiceItemZoneRelation> findByServiceItemIdWithDetails(@Param("serviceItemId") String serviceItemId);

    /**
     * 删除专区下的所有关联关系
     * @param serviceZoneId 专区ID
     */
    @Modifying
    @Query("UPDATE ConfServiceItemZoneRelation r SET r.deleted = true WHERE r.serviceZone.id = :serviceZoneId")
    void deleteByServiceZoneId(@Param("serviceZoneId") String serviceZoneId);

    /**
     * 删除服务清单的所有关联关系
     * @param serviceItemId 服务清单ID
     */
    @Modifying
    @Query("UPDATE ConfServiceItemZoneRelation r SET r.deleted = true WHERE r.serviceItem.id = :serviceItemId")
    void deleteByServiceItemId(@Param("serviceItemId") String serviceItemId);
}