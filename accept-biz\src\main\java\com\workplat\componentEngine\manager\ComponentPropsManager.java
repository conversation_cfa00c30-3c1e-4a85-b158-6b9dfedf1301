package com.workplat.componentEngine.manager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.matter.vo.ConfMatterExtendVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 组件属性管理器
 * 统一处理组件属性的获取、合并和管理
 * 
 * <AUTHOR>
 * @date 2025/07/23
 */
@Slf4j
@Component
public class ComponentPropsManager {

    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;

    /**
     * 获取组件的完整属性配置
     * 优先级：extendVO中的组件属性 > content中的默认配置
     * 
     * @param componentDataContext 组件数据上下文
     * @return 合并后的属性配置
     */
    public JSONObject getMergedComponentProps(ComponentDataContext componentDataContext) {
        ConfComponent confComponent = componentDataContext.getConfComponent();
        if (confComponent == null) {
            return new JSONObject();
        }

        // 1. 获取默认配置（content字段）
        JSONObject defaultConfig = getDefaultConfig(confComponent);
        
        // 2. 获取扩展属性（extendVO中的配置）
        Optional<ComponentPropsDTO> extendProps = getExtendComponentProps(componentDataContext);
        
        // 3. 合并配置
        return mergeConfigs(defaultConfig, extendProps);
    }

    /**
     * 获取指定属性值
     * 
     * @param componentDataContext 组件数据上下文
     * @param propertyName 属性名称
     * @return 属性值
     */
    public String getPropertyValue(ComponentDataContext componentDataContext, String propertyName) {
        return getPropertyValue(componentDataContext, propertyName, null);
    }

    /**
     * 获取指定属性值（带默认值）
     * 
     * @param componentDataContext 组件数据上下文
     * @param propertyName 属性名称
     * @param defaultValue 默认值
     * @return 属性值
     */
    public String getPropertyValue(ComponentDataContext componentDataContext, String propertyName, String defaultValue) {
        JSONObject mergedConfig = getMergedComponentProps(componentDataContext);
        String value = mergedConfig.getString(propertyName);
        return StringUtils.isNotBlank(value) ? value : defaultValue;
    }

    /**
     * 获取指定类型的属性值
     * 
     * @param componentDataContext 组件数据上下文
     * @param propertyName 属性名称
     * @param targetType 目标类型
     * @param defaultValue 默认值
     * @return 转换后的属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getPropertyValue(ComponentDataContext componentDataContext, String propertyName, Class<T> targetType, T defaultValue) {
        JSONObject mergedConfig = getMergedComponentProps(componentDataContext);
        Object value = mergedConfig.get(propertyName);
        
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return convertValue(value.toString(), targetType);
        } catch (Exception e) {
            log.warn("属性值转换失败: property={}, value={}, targetType={}, error={}", 
                    propertyName, value, targetType.getSimpleName(), e.getMessage());
            return defaultValue;
        }
    }

    /**
     * 检查属性是否存在且不为空
     * 
     * @param componentDataContext 组件数据上下文
     * @param propertyName 属性名称
     * @return 是否存在且不为空
     */
    public boolean hasProperty(ComponentDataContext componentDataContext, String propertyName) {
        String value = getPropertyValue(componentDataContext, propertyName);
        return StringUtils.isNotBlank(value);
    }

    /**
     * 获取所有属性的Map
     * 
     * @param componentDataContext 组件数据上下文
     * @return 属性Map
     */
    public Map<String, Object> getAllProperties(ComponentDataContext componentDataContext) {
        JSONObject mergedConfig = getMergedComponentProps(componentDataContext);
        return new HashMap<>(mergedConfig);
    }

    /**
     * 获取扩展组件属性（从extendVO中）
     * 
     * @param componentDataContext 组件数据上下文
     * @return 扩展组件属性
     */
    public Optional<ComponentPropsDTO> getExtendComponentProps(ComponentDataContext componentDataContext) {
        try {
            BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());
            if (bizInstanceInfo == null) {
                return Optional.empty();
            }

            ConfMatterExtendVO extendVO = (ConfMatterExtendVO) BizInstanceExtendJsonEnum.ConfMatterExtendVO.convertObject(bizInstanceInfo);
            if (extendVO == null || StringUtils.isBlank(extendVO.getComponentProps())) {
                return Optional.empty();
            }

            String componentCode = componentDataContext.getConfComponent().getCode();
            List<ComponentPropsDTO> componentProps = JSON.parseArray(extendVO.getComponentProps(), ComponentPropsDTO.class);
            
            if (componentProps == null || componentProps.isEmpty()) {
                return Optional.empty();
            }

            return componentProps.stream()
                    .filter(item -> componentCode.equals(item.getComponentCode()))
                    .findFirst();
        } catch (Exception e) {
            log.error("获取扩展组件属性失败: instanceId={}, componentCode={}, error={}", 
                    componentDataContext.getInstanceId(), 
                    componentDataContext.getConfComponent().getCode(), 
                    e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 获取默认配置（从content字段）
     * 
     * @param confComponent 组件配置
     * @return 默认配置
     */
    private JSONObject getDefaultConfig(ConfComponent confComponent) {
        try {
            String content = confComponent.getContent();
            if (StringUtils.isBlank(content)) {
                return new JSONObject();
            }
            return JSON.parseObject(content);
        } catch (Exception e) {
            log.warn("解析组件默认配置失败: componentCode={}, content={}, error={}", 
                    confComponent.getCode(), confComponent.getContent(), e.getMessage());
            return new JSONObject();
        }
    }

    /**
     * 合并配置
     * 扩展属性会覆盖默认配置中的同名属性
     * 
     * @param defaultConfig 默认配置
     * @param extendProps 扩展属性
     * @return 合并后的配置
     */
    private JSONObject mergeConfigs(JSONObject defaultConfig, Optional<ComponentPropsDTO> extendProps) {
        JSONObject mergedConfig = new JSONObject();
        
        // 1. 先添加默认配置
        if (defaultConfig != null) {
            mergedConfig.putAll(defaultConfig);
        }
        
        // 2. 用扩展属性覆盖默认配置
        if (extendProps.isPresent() && extendProps.get().getProps() != null) {
            Map<String, String> propsMap = extendProps.get().getProps().stream()
                    .filter(item -> StringUtils.isNotBlank(item.getValue()))
                    .collect(java.util.stream.Collectors.toMap(ComponentPropsDTO.PropsVO::getName, ComponentPropsDTO.PropsVO::getValue));
            for (Map.Entry<String, String> entry : propsMap.entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue())) {
                    mergedConfig.put(entry.getKey(), entry.getValue());
                }
            }
        }
        
        return mergedConfig;
    }

    /**
     * 值类型转换
     * 
     * @param value 原始值
     * @param targetType 目标类型
     * @return 转换后的值
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(String value, Class<T> targetType) {
        if (targetType == String.class) {
            return (T) value;
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return (T) Boolean.valueOf(Boolean.parseBoolean(value));
        } else if (targetType == Integer.class || targetType == int.class) {
            return (T) Integer.valueOf(Integer.parseInt(value));
        } else if (targetType == Long.class || targetType == long.class) {
            return (T) Long.valueOf(Long.parseLong(value));
        } else if (targetType == Double.class || targetType == double.class) {
            return (T) Double.valueOf(Double.parseDouble(value));
        }
        
        return (T) value;
    }
}
