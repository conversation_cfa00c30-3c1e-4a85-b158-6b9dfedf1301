package com.workplat.extend.socialAssistance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/28 15:57
 */
@Getter
@Setter
@Schema(description = "救助平台用户信息实体类")
public class RescueUserInfoVO {

    /**
     * 不可享受的项目信息
     */
    private List<Info> bksx;
    /**
     * 拟可享受的项目信息
     */
    private List<Info> nkxs;


    @Getter
    @Setter
    @Schema(description = "信息")
    public static class Info {

        @Schema(description = "姓名")
        private String xm;

        @Schema(description = "身份证号")
        private String sfz;

        @Schema(description = "部门名称")
        private String pname;

        @Schema(description = "项目名称")
        private String name;

        @Schema(description = "不可享受原因")
        private String bxsyy;

        @Schema(description = "拟可享受提示语")
        private String nkxstsy;
    }


}
