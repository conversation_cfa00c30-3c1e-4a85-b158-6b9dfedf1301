spring:
  application:
    name: ai-central-platform
  profiles:
    active: dev
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        server-addr: 192.168.124.215:8848
        namespace: ai-central-platform-${spring.profiles.active}
        group: ${spring.profiles.active}
        prefix: ${spring.application.name}
        file-extension: yaml
        shared-configs:
          - data-id: shared-config-mysql.yaml
            refresh: true
            group: SHARED_CONFIG
          - data-id: shared-config-redis.yaml
            refresh: true
            group: SHARED_CONFIG
          - data-id: shared-config-mongodb.yaml
            refresh: true
            group: SHARED_CONFIG
          - data-id: shared-config-satoken.yaml
            refresh: true
            group: SHARED_CONFIG
          - data-id: ga-datasource.yaml  # 新增独立配置
            refresh: true
            group: DEFAULT_GROUP
      username: nacos
      password: Risesoft123!@



