package com.workplat.extend.socialAssistance.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 救助平台用户信息实体类
 *
 * <AUTHOR>
 * @date 2025/06/30
 */

/**
 * 救助平台用户信息实体类
 */
@Data
@Schema(name = "救助平台用户信息实体类")
public class RescueUserInfoDto {

    /**
     * 主键(UUID)
     */
    @Schema(description = "主键(UUID)该字段在新增场景时不传", required = false)
    private String id;

    /**
     * 姓名
     */
    @Schema(description = "姓名", required = true)
    private String xm;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号", required = true)
    private String sfz;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码", required = true)
    private String lxdh;

    /**
     * 户籍所在地
     */
    @Schema(description = "户籍所在地", required = true)
    private String ssdq;

    /**
     * 户籍所在地(区划翻译)
     */
    @Schema(description = "户籍所在地(区划翻译)", required = false)
    private String ssdqCn;

    /**
     * 户籍地详细地址
     */
    @Schema(description = "户籍地详细地址", required = false)
    private String ssdq_xxdz;

    /**
     * 现居地区
     */
    @Schema(description = "现居地区", required = true)
    private String xjzd;

    /**
     * 现居地区(区划翻译)
     */
    @Schema(description = "现居地区(区划翻译)", required = false)
    private String xjzdCn;

    /**
     * 现居地详细地址
     */
    @Schema(description = "现居地详细地址", required = false)
    private String xjzd_xjdz;

    /**
     * 是否本地户籍(4.1.6字典)
     */
    @Schema(description = "是否本地户籍(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String localHj;

    /**
     * 家庭人口数
     */
    @Schema(description = "家庭人口数", required = true)
    private int jtrks;

    /**
     * 家庭成员人均月收入(元)
     */
    @Schema(description = "家庭成员人均月收入(元)", required = true)
    private int jtcyrjsr;

    /**
     * 性别(4.1.7字典)
     */
    @Schema(description = "性别(4.1.7字典)", required = true, allowableValues = {"1男", "2女"})
    private String sex;

    /**
     * 年龄
     */
    @Schema(description = "年龄", required = true)
    private int age;

    /**
     * 月收入
     */
    @Schema(description = "月收入", required = true)
    private int ysr;

    /**
     * 是否有唯一住房(4.1.6字典)
     */
    @Schema(description = "是否有唯一住房(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfwyzf;

    /**
     * 是否危房(4.1.6字典)
     */
    @Schema(description = "是否有危房(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfwf;

    /**
     * 家庭是否有在校学生(4.1.6字典)
     */
    @Schema(description = "家庭是否有在校学生(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfyzxxs;

    /**
     * 家庭是否有老人(4.1.6字典)
     */
    @Schema(description = "家庭是否有老人(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfylr;

    /**
     * 是否离校两年内未就业(4.1.6字典)
     */
    @Schema(description = "是否离校两年内未就业(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String bylxsj;

    /**
     * 是否就业(4.1.6字典)
     */
    @Schema(description = "是否就业(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfjy;

    /**
     * 是否为孤儿(4.1.6字典)
     */
    @Schema(description = "是否为孤儿(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfwge;

    /**
     * 是否患重病或罕见病(4.1.6字典)
     */
    @Schema(description = "是否患重病或罕见病(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfhzbhhjb;

    /**
     * 是否发生重大特殊情况(4.1.6字典)
     */
    @Schema(description = "是否发生重大特殊情况(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sffszdtsqk;

    /**
     * 是否遭遇灾难性危害(4.1.6字典)
     */
    @Schema(description = "是否遭遇灾难性危害(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfzyznxwh;

    /**
     * 是否生活必须支出突然增加(自负教育、医疗等)(4.1.6字典)
     */
    @Schema(description = "是否生活必须支出突然增加(自负教育、医疗等)(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfshbxzctrzj;

    /**
     * 是否已确诊宫颈癌或乳腺浸润癌(4.1.6字典)
     */
    @Schema(description = "是否已确诊宫颈癌或乳腺浸润癌(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfyqzgjahrxjra;

    /**
     * 是否需要法律援助(4.1.6字典)
     */
    @Schema(description = "是否需要法律援助(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfxyflyz;

    /**
     * 是否残疾(4.1.6字典)
     */
    @Schema(description = "是否残疾(4.1.6字典)", required = true, allowableValues = {"1是", "0否"})
    private String sfcj;

    /**
     * 是否丧失劳动能力（4.1.6 字典）
     */
    @Schema(description = "是否丧失劳动能力（4.1.6 字典）", required = true, allowableValues = {"1是", "0否"})
    private String sfssldnl;

    /**
     * 残疾类别(4.1.9字典)
     */
    @Schema(description = "残疾类别(4.1.9字典)", required = false
            , allowableValues = {"1视力残疾", "2听力残疾", "3言语残疾", "4肢体残疾", "5智力残疾", "6精神残疾", "7多重残疾", "8其他残疾"})
    private String cjlb;

    /**
     * 残疾类别(字典翻译)
     */
    @Schema(description = "残疾类别(字典翻译)", required = false)
    private String cjlbCn;

    /**
     * 残疾等级(4.1.10字典)
     */
    @Schema(description = "残疾等级(4.1.10字典)", required = false, allowableValues = {"1一级", "2二级", "3三级", "4四级"})
    private String cjdj;

    /**
     * 残疾等级(字典翻译)
     */
    @Schema(description = "残疾等级(字典翻译)", required = false)
    private String cjdjCn;

    /**
     * 重病类型(4.1.8字典)
     */
    @Schema(description = "重病类型(4.1.8字典)", required = false, allowableValues = {"1白血病", "2尿毒症", "3红斑狼疮症", "4其他"})
    private String zblx;

    /**
     * 重病类型(字典翻译)
     */
    @Schema(description = "重病类型(字典翻译)", required = false)
    private String zblxCn;

    /**
     * 灾害类型（4.1.12 字典）
     */
    @Schema(description = "灾害类型（4.1.12 字典）", required = false, allowableValues = {"1自然灾害", "2人为灾害"})
    private String zhlx;

    /**
     * 住房数量
     */
    @Schema(description = "住房数量", required = true)
    private String zfsl;

    /**
     * 是否法人（4.1.6 字典）
     */
    @Schema(description = "是否法人（4.1.6 字典）", required = true, allowableValues = {"1是", "0否"})
    private String sffr;

    /**
     * 车辆数量
     */
    @Schema(description = "车辆数量", required = true)
    private String clsl;

    /**
     * 大型农机具、经营性船舶数量
     */
    @Schema(description = "大型农机具、经营性船舶数量", required = true)
    private String dxnjcbsl;

    /**
     * 金融资金
     */
    @Schema(description = "金融资金", required = true)
    private String jrzc;

    /**
     * 困难说明
     */
    @Schema(description = "困难说明", required = false)
    private String knsm;

    /**
     * 家庭成员集合
     */
    private List<FamilyMember> jtcyListYd;

    /**
     * 家庭成员信息
     */
    @Data
    @Schema(description = "家庭成员信息")
    public static class FamilyMember {
        /**
         * 姓名
         */
        @Schema(description = "姓名", required = false)
        private String xm;

        /**
         * 身份证
         */
        @Schema(description = "身份证", required = false)
        private String sfz;

        /**
         * 性别(4.1.7字典)
         */
        @Schema(description = "性别(4.1.7字典)", required = false, allowableValues = {"1男", "2女"})
        private String sex;

        /**
         * 年龄
         */
        @Schema(description = "年龄", required = false)
        private int age;

        /**
         * 与申请人关系(4.1.5字典)
         */
        @Schema(description = "与申请人关系(4.1.5字典)", required = false)
        private String gx;

        /**
         * 与申请人关系(字典翻译)
         */
        @Schema(description = "与申请人关系(字典翻译)", required = false)
        private String gxCn;

        /**
         * 职业(4.1.4字典)
         */
        @Schema(description = "职业(4.1.4字典)", required = false)
        private String zy;

        /**
         * 职业(字典翻译)
         */
        @Schema(description = "职业(字典翻译)", required = false)
        private String zyCn;

        /**
         * 月收入
         */
        @Schema(description = "月收入", required = false)
        private int ysr;

        /**
         * 是否有唯一住房(4.1.6字典)
         */
        @Schema(description = "是否有唯一住房(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfwyzf;

        /**
         * 是否危房(4.1.6字典)
         */
        @Schema(description = "是否有危房(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfwf;

        /**
         * 家庭是否有在校学生(4.1.6字典)
         */
        @Schema(description = "家庭是否有在校学生(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfyzxxs;

        /**
         * 家庭是否有老人(4.1.6字典)
         */
        @Schema(description = "家庭是否有老人(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfylr;

        /**
         * 是否离校两年内未就业(4.1.6字典)
         */
        @Schema(description = "是否离校两年内未就业(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String bylxsj;

        /**
         * 是否就业(4.1.6字典)
         */
        @Schema(description = "是否就业(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfjy;

        /**
         * 是否为孤儿(4.1.6字典)
         */
        @Schema(description = "是否为孤儿(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfwge;

        /**
         * 是否患重病或罕见病(4.1.6字典)
         */
        @Schema(description = "是否患重病或罕见病(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfhzbhhjb;

        /**
         * 是否发生重大特殊情况(4.1.6字典)
         */
        @Schema(description = "是否发生重大特殊情况(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sffszdtsqk;

        /**
         * 是否遭遇灾难性危害(4.1.6字典)
         */
        @Schema(description = "是否遭遇灾难性危害(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfzyznxwh;

        /**
         * 是否生活必须支出突然增加(自负教育、医疗等)(4.1.6字典)
         */
        @Schema(description = "是否生活必须支出突然增加(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfshbxzctrzj;

        /**
         * 是否已确诊宫颈癌或乳腺浸润癌(4.1.6字典)
         */
        @Schema(description = "是否已确诊宫颈癌或乳腺浸润癌(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfyqzgjahrxjra;

        /**
         * 是否需要法律援助(4.1.6字典)
         */
        @Schema(description = "是否需要法律援助(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfxyflyz;

        /**
         * 是否残疾(4.1.6字典)
         */
        @Schema(description = "是否残疾(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfcj;

        /**
         * 重病类型(4.1.8字典)
         */
        @Schema(description = "重病类型(4.1.8字典)", required = false)
        private String zblx;

        /**
         * 重病类型(字典翻译)
         */
        @Schema(description = "重病类型(字典翻译)", required = false)
        private String zblxCn;

        /**
         * 是否在读(4.1.6字典)
         */
        @Schema(description = "是否在读(4.1.6字典)", required = false, allowableValues = {"1是", "0否"})
        private String sfzd;

        /**
         * 在读年级(4.1.11字典)
         */
        @Schema(description = "在读年级(4.1.11字典)", required = false)
        private String zdnj;

        /**
         * 在读年级(字典翻译)
         */
        @Schema(description = "在读年级(字典翻译)", required = false)
        private String zdnjCn;
    }
}
