package com.workplat.accept.business.chat.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "biz_chat_conversation")
@Getter
@Setter
public class BizChatConversation extends BaseEntity {

    @Comment("用户ID")
    @Column(name = "user_id", length = 64)
    private String userId;

    @Comment("会话标题")
    @Column(name = "title", length = 255)
    private String title;

    @Comment("渠道")
    @Column(name = "channel", length = 32)
    private String channel;

    @Comment("智能体key")
    @Column(name = "agent_key", length = 255)
    private String agentKey;

    @Comment("智能体会话ID")
    @Column(name = "agent_chat_id", length = 64)
    private String agentChatId;

    @Comment("办件id")
    @Column(name = "instance_id", length = 64)
    private String instanceId;

    @Comment("对话数量")
    @Column(name = "message_count", length = 11)
    private Integer messageCount;
} 