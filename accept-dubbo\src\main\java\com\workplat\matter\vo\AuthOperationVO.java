package com.workplat.matter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/6/6 14:38
 */
@Data
@Schema(description = "授权操作DTO")
public class AuthOperationVO {

    @Schema(description = "实例id")
    private String instanceId;

    @Schema(description = "是否人脸核验")
    private boolean face;

    @Schema(description = "签名文件地址")
    private String signFileUrl;
}
