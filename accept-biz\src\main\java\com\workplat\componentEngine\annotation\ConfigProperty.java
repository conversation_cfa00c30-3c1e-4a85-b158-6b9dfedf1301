package com.workplat.componentEngine.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 配置属性注解
 * <AUTHOR>
 * @date 2025/07/23
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ConfigProperty {
    
    /**
     * 配置属性名称
     */
    String value();
    
    /**
     * 默认值
     */
    String defaultValue() default "";
    
    /**
     * 是否必需
     */
    boolean required() default false;
    
    /**
     * 描述信息
     */
    String description() default "";
}
