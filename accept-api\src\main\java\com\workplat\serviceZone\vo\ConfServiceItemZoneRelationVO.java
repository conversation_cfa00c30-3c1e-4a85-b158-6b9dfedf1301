package com.workplat.serviceZone.vo;

import lombok.Data;

import java.util.Date;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联关系VO
 */
@Data
public class ConfServiceItemZoneRelationVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 服务清单ID
     */
    private String serviceItemId;

    /**
     * 服务清单名称
     */
    private String serviceItemName;

    /**
     * 专区ID
     */
    private String serviceZoneId;

    /**
     * 专区名称
     */
    private String serviceZoneName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}