package com.workplat.accept.business.home.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.workplat.gss.common.core.constant.DateFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/10/19 21:40
 * @Description:
 */

@Data
public class ConfMatterCatalogVO {
    @Schema(description ="id")
    private String id;
    @Schema(description = "事项列表")
    private List<ConfMatterVO> children;

    /**
     * 事项名称
     */
    @Schema(description = "事项名称")
    private String matterName;

    /**
     * 事项编码
     */
    @Schema(description = "事项编码")
    private String matterCode;

    /**
     * 事项层级
     */
    @Schema(description = "事项层级")
    private String matterLevel;

    /**
     * 事项类型
     */
    @Schema(description = "事项类型")
    private String matterType;

    /**
     * 是否发布
     */
    @Schema(description = "是否发布")
    private String isPublic;
    /**
     * 逻辑删除状态
     */
    private boolean deleted;

    /**
     * 排序
     */
    private Integer sort ;

    /**
     * 版本
     */
    private Integer version ;

    /**
     * 创建时间
     */
    @JSONField(format = DateFormat.DATE_MINI_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @JSONField(format = DateFormat.DATE_MINI_PATTERN)
    private Date updateTime;

    /**
     * 创建人
     */

    private String createdBy;

    /**
     * 更新人
     */

    private String updatedBy;

    private List<VersionVO> history;

}
