package com.workplat.conf.component.api;

import com.workplat.componentEngine.request.ComponentUsageRequest;
import com.workplat.componentEngine.vo.ComponentUsageSituationVO;
import com.workplat.conf.component.vo.ConfComponentVO;
import com.workplat.conf.component.dto.ConfComponentDTO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "边聊边办组件配置")
@RestController
@RequestMapping(value = "/api/conf/component")
public interface ConfComponentApi {

    @PostMapping("/save")
    @Operation(summary = "保存组件")
    ResponseData<ConfComponentVO> save(@Valid @RequestBody ConfComponentDTO confComponentDto);

    @GetMapping("/list")
    @Operation(summary = "获取组件列表")
    ResponseData<List<ConfComponentVO>> getConfComponentList(String keyword);

    @GetMapping("/page")
    @Operation(summary = "分页获取组件列表")
    ResponseData<Page<ConfComponentVO>> getConfComponentPage(String keyword, PageableDTO pageDTO);

    @GetMapping("/delete")
    @Operation(summary = "删除组件")
    ResponseData<Void> delete(@RequestParam String id);

    @GetMapping("/getByCode")
    @Operation(summary = "通过编码获取组件")
    ResponseData<ConfComponentVO> getByCode(@RequestParam String code);

    @GetMapping("/getById")
    @Operation(summary = "通过id获取组件")
    ResponseData<ConfComponentVO> getById(String id);

    @PostMapping("/getList")
    @Operation(summary = "获取组件清单")
    ResponseData<Page<ComponentUsageSituationVO>> getList(@Valid @RequestBody ComponentUsageRequest request);

}
