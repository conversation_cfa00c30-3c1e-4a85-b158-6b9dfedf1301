package com.workplat.gss.service.item.dubbo.matter.constant;

import com.workplat.gss.common.core.annotation.dict.DictCode;
import com.workplat.gss.common.core.annotation.dict.DictGroup;
import com.workplat.gss.common.core.annotation.dict.DictName;
import com.workplat.gss.common.core.constant.DictGroupConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: INSTANCE_BINDING_TYPE
 * @Description: 绑定类型
 * @Author: Jwk
 * @CreateDate: 2024/8/20
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
@Slf4j
@DictGroup(group = DictGroupConstant.BUSINESS, name = "绑定类型", code = "INSTANCE_BINDING_TYPE")
public enum BindingTypeEnum {

    @DictCode
    FIELD("字段"),
    @DictCode
    MATTER("事项"),
    @DictCode
    MATERIAL("材料"),
    @DictCode
    DOCUMENT("文书"),
    @DictCode
    QUOTA("指标"),
    @DictCode
    OPTION("选项"),
    @DictCode
    NetWorkWindow("网点");


    @DictName
    private String name;


}
