package com.workplat.matter.converter;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.matter.entity.BizQuestionRecommend;
import com.workplat.matter.dto.QuestionRecommendDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.converter
 * @description
 * @date 2025/5/27 13:56
 */
@Component
public class BizQuestionRecommendConverter implements BaseConverter<BizQuestionRecommend, QuestionRecommendDTO> {
}
