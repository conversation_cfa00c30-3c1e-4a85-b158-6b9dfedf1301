package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.adapter.RemoteDataAdapter;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description: 档案组件
 * @date 2025/06/04
 */
@Slf4j
@Service
public class ArchivesComponentEngine extends AbstractComponentEngine{

    private final RemoteDataAdapter remoteDataAdapter;
    private final BizInstanceFieldsService bizInstanceFieldsService;

    public ArchivesComponentEngine(@Qualifier("gatewayRemoteDataAdapter") RemoteDataAdapter remoteDataAdapter,
                                   BizInstanceFieldsService bizInstanceFieldsService) {
        this.remoteDataAdapter = remoteDataAdapter;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ComponentRunVO result = new ComponentRunVO();
        ResultData resultData = new ResultData();

        // 配置获取
        String content = componentDataContext.getConfComponent().getContent();
        String downloadUrl = getPropertyValue(componentDataContext, "downloadUrl");
        String existText = getPropertyValue(componentDataContext, "existText", "档案存在");
        String notExistText = getPropertyValue(componentDataContext, "notExistText", "档案不存在");
        String typeId = getPropertyValue(componentDataContext, "typeId");

        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(componentDataContext.getInstanceId());
        // 获取 init 配置
        JSONObject initConfig = remoteDataAdapter.getConfig(content, "init");

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.AUTHORIZATION, componentDataContext.getInputs().get("aNetToken").toString());

        // 执行远程调用
        String url = initConfig.getString("url");
        String methodStr = initConfig.getString("method");


//        Map<String, Object> userMap = new HashMap<>();
//        userMap.put("fatherName", "朱坤");
//        userMap.put("fatherCardNum", "342401199505179675");
//        userMap.put("motherName", "张俊");
//        userMap.put("motherCardNum", "342401199609112520");

        JSONObject formObj = JSON.parseObject(bizInstanceFieldsVO.getFormObj());
        formObj.remove("applicationDate");

        Map<String, Object> mapBody = Map.of("typeId", typeId, "archiveParams", formObj);

        try {
            String responseBody = remoteDataAdapter.fetchData(url, HttpMethod.valueOf(methodStr.toUpperCase()), headers, JSON.toJSONString(mapBody), null);
            if (responseBody != null && !"[]".equals(responseBody)) {
                log.info("档案查询结果：{}", responseBody);
                JSONArray dataArray = JSON.parseArray(responseBody);
                List<FileData> files = new ArrayList<>();
                for (Object o : dataArray) {
                    JSONObject jsonObject = JSON.parseObject(o.toString());
                    String fileId = jsonObject.getString("fileId");
                    String fileName = jsonObject.getString("fileName");
                    String fileType = jsonObject.getString("fileType");
                    String fileUrl =  downloadUrl + fileId;
                    files.add(FileData.builder()
                            .fileId(fileId)
                            .fileName(fileName)
                            .fileType(fileType)
                            .fileUrl(fileUrl)
                            .build());
                }
                resultData.setStatus("exist");
                resultData.setData(HouseholdRegData.builder()
                        .text(existText)
                        .files(files)
                        .build());

                result.setRenderData(List.of(ComponentRunVO.RenderData.builder()
                        .componentName(getArchive())
                        .componentInfo(resultData)
                        .build()));

                // 提交这个办件
                bizInstanceInfoService.endSubmit(componentDataContext.getInstanceId());
                return result;
            }
        } catch (Exception e) {
            log.error("远程数据获取失败: url={}, method={}, error={}", url, methodStr, e.getMessage());
        }
        // 未查询到
        resultData.setStatus("notExist");
        resultData.setData(HouseholdRegData.builder()
                .text(notExistText)
                .files(null)
                .build());

        result.setRenderData(List.of(ComponentRunVO.RenderData.builder()
                .componentName(getArchive())
                .componentInfo(resultData)
                .build()));
        return result;
    }

    @NotNull
    private static String getArchive() {
        return ComponentEngineCode.ARCHIVE;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return getArchive().equals(context.getConfComponent().getEngineCode());
    }

    @Data
    public static class ResultData {
        // 0: 未查询 1: 查询成功 2: 查询失败
        private String status;
        // 查询结果
        private HouseholdRegData data;
    }


    @Data
    @Builder
    public static class HouseholdRegData {
        // 提示信息
        private String text;
        // 文件列表
        private List<FileData> files;
    }

    @Data
    @Builder
    public static class FileData {
        // 文件id
        private String fileId;
        // 文件名称
        private String fileName;
        // 文件类型
        private String fileType;
        // 文件url
        private String fileUrl;
    }
}
