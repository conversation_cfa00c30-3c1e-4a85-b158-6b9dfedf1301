package com.workplat.matter.dto;

import com.workplat.gss.common.core.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/5/27 13:34
 */
@Data
@Schema(description = "问题推荐DTO")
public class QuestionRecommendDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "问题名称")
    private String name;

    @Schema(description = "问题类型")
    @Dict(dictCode = "question_type")
    private String type;
}
