package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.biz.converter.BizInstanceFieldsConvert;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.utils.FormFieldFilterUtil;
import com.workplat.utils.FormStepProcessor;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 表单分组组件
 *
 * <AUTHOR>
 * @date 2025/06/04
 */
@Slf4j
@Service
public class FormGroupComponentEngine extends AbstractComponentEngine {

    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceFieldsConvert bizInstanceFieldsConvert;
    private final ChatCacheUtil chatCacheUtil;


    public FormGroupComponentEngine(BizInstanceFieldsService instanceFieldsService,
                                    BizInstanceFieldsConvert bizInstanceFieldsConvert,
                                    ChatCacheUtil chatCacheUtil) {
        this.bizInstanceFieldsService = instanceFieldsService;
        this.bizInstanceFieldsConvert = bizInstanceFieldsConvert;
        this.chatCacheUtil = chatCacheUtil;
    }

    public static final String CODE = ComponentEngineCode.FORM_GROUP;

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        // 获取表单信息
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(chatProcessDTO.getInstanceId());
        // 获取并存储表单分组信息
        String fullFormJson = bizInstanceFieldsVO.getFormJson();
        int formStepCount = FormStepProcessor.getFormStepCount(fullFormJson);
        chatProcessDTO.setFormStepCount(formStepCount);
        // 设置过滤的表单字段map
        Map<String, String> formAreaFields = new HashMap<>();

        // 处理当前表单分组索引
        int formStepIndex = chatProcessDTO.getFormStepIndex() == null ? 1 : chatProcessDTO.getFormStepIndex();

        // 获取当前步骤的表单数据
        JSONObject formStepData = FormStepProcessor.getStepData(fullFormJson, formStepIndex);

        //  设置表单数据
        bizInstanceFieldsVO.setFormJson(formStepData.toJSONString());
        formAreaFields = FormStepProcessor.extractFormFieldsMap(formStepData.toJSONString());
        // 循环处理表单字段并跳过已填写完成的步骤
        if (bizInstanceFieldsVO.getFormObj() != null) {
            try {
                boolean stepProcessed = false;
                while (!stepProcessed && formStepIndex <= formStepCount) {
                    // 使用增强版过滤方法，自动处理空表单域
                    FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                            formStepData.toJSONString(),
                            bizInstanceFieldsVO.getFormObj(),
                            false // 移除模式
                    );

                    String filteredFormJson = filterResult.getFilteredFormJson();

                    // 记录被移除的表单域信息（用于日志和调试）
                    if (!filterResult.getRemovedFormAreaIndexes().isEmpty()) {
                        log.info("表单分组 {} 中移除了空表单域，索引: {}, 字段: {}",
                                formStepIndex,
                                filterResult.getRemovedFormAreaIndexes(),
                                filterResult.getRemovedFormAreaFields());
                    }

                    // 检查当前步骤是否有可填写字段
                    if (FormStepProcessor.hasFieldsToFill(filteredFormJson, bizInstanceFieldsVO.getFieldsFilterMap())) {
                        // 当前步骤有可填写字段，结束循环
                        bizInstanceFieldsVO.setFormJson(filteredFormJson);
                        formAreaFields = FormStepProcessor.extractFormFieldsMap(filteredFormJson);
                        stepProcessed = true;
                    } else {
                        // 当前步骤无可填写字段，尝试下一个步骤
                        if (formStepIndex < formStepCount) {
                            log.info("表单分组 {} 无可填写字段，跳转到下一步", formStepIndex);
                            formStepIndex++;
                            chatProcessDTO.setFormStepIndex(formStepIndex);
                            formStepData = FormStepProcessor.getStepData(fullFormJson, formStepIndex);
                        } else {
                            // 所有步骤都处理完毕且没有可填写字段
                            log.info("所有表单分组都没有可填写字段");
                            bizInstanceFieldsVO.setFormJson(filteredFormJson);
                            formAreaFields = FormStepProcessor.extractFormFieldsMap(filteredFormJson);
                            stepProcessed = true;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("表单分组组件执行筛选表单元数据结构异常", e);
                // 保留原始表单数据作为降级方案
                bizInstanceFieldsVO.setFormJson(formStepData.toJSONString());
            }
        }
        // 更新缓存
        chatProcessDTO.setFormStepIndex(formStepIndex);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
        //  输出结果
        JSONObject fieldsJson = JSON.parseObject(JSON.toJSONString(bizInstanceFieldsVO));
        fieldsJson.put("apiExtractCode", chatProcessDTO.getApiExtractCode());
        fieldsJson.put("formAreaFieldsMap", JSON.toJSONString(formAreaFields));
        fieldsJson.remove("fieldJsonObject");
        ComponentRunVO.RenderData fieldFormRenderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(fieldsJson)
                .build();
        // 组装结果
        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(fieldFormRenderData);
        vo.setRenderData(renderDataList);
        // 设置提示信息
        vo.setTips(getPropertyValue(componentDataContext, "tips"));
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 填充数据
        log.info("表单分组组件填充数据");
        Object submitData = componentDataContext.getSubmitData();
        log.info("submitData:{}", JSON.toJSONString(submitData));
        // 更新填报的数据
        @SuppressWarnings("unchecked")
        Map<String, Object> submitDataMap = JSON.parseObject(submitData.toString(), Map.class);
        if (submitDataMap != null) {
            BizInstanceFields bizInstanceFields = bizInstanceFieldsService.
                    queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", componentDataContext.getInstanceId()).build());
            String formObj = bizInstanceFields.getFormObj();
            if (formObj != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
                formObjMap.putAll(submitDataMap);
                bizInstanceFields.setFormObj(JSON.toJSONString(formObjMap));
            } else {
                bizInstanceFields.setFormObj(JSON.toJSONString(submitDataMap));
            }

            bizInstanceFields.setFormObj(bizInstanceFields.getFormObj());
            bizInstanceFieldsService.update(bizInstanceFields);
        }
        // 对表单域进行数量增加
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        chatProcessDTO.setFormStepIndex(chatProcessDTO.getFormStepIndex() + 1);
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
        log.info("表单分组组件填充数据结束, 步骤{}", chatProcessDTO.getFormStepIndex());
    }

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        if (chatProcessDTO != null
                && chatProcessDTO.getFormStepCount() != null
                && chatProcessDTO.getFormStepCount() >= chatProcessDTO.getFormStepIndex()) {
            return InstructionConstant.KEEP_AT_PRESENT.getCode();

        }
        return super.getNextInstruction(componentDataContext);
    }
}
