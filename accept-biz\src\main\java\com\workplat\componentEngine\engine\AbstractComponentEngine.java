package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.componentEngine.manager.ComponentPropsManager;
import com.workplat.componentEngine.service.ComponentEngineInterface;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.utils.ChatCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractComponentEngine implements ComponentEngineInterface {


    @Autowired
    protected ChatCacheUtil chatCacheUtil;
    @Autowired
    protected BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    protected ComponentPropsManager componentPropsManager;

    // 抽象方法：由子类实现具体的业务逻辑
    protected abstract ComponentRunVO doExecute(ComponentDataContext componentDataContext);

    // 公共入口方法：统一处理上下文设置和充实
    public final ComponentRunVO execute(ComponentDataContext componentDataContext) {

        enrichContext();

        ComponentRunVO doneExecute = doExecute(componentDataContext);
        // 设置基本属性
        setBasicProperties(doneExecute, componentDataContext);
        return doneExecute;
    }

    /**
     * 设置组件运行结果的基本属性
     */
    protected void setBasicProperties(ComponentRunVO result, ComponentDataContext componentDataContext) {
        ConfComponent confComponent = componentDataContext.getConfComponent();
        if (result != null && confComponent != null) {
            // 设置实例ID
            result.setInstanceId(componentDataContext.getInstanceId());
            // 设置组件基本信息
            if (StringUtils.isEmpty(result.getEngineCode())) {
                result.setEngineCode(confComponent.getEngineCode());
            }
            result.setComponentCode(confComponent.getCode());
            result.setMetadata(confComponent.getContent());
        }
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
    }

    // 充实上下文内容，子类可以覆盖此方法以提供特定的上下文充实逻辑
    protected void enrichContext() {
        // 默认实现不做任何操作
    }

    // 示例方法：模拟接口调用
    private String fetchEnrichedDataFromApi() {
        return "Enriched Data from API";
    }


    /**
     * 由子类实现的具体判断逻辑
     */
    public abstract boolean canHandle(ComponentDataContext context);

    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        // 默认返回成功，子类可以覆盖此方法以提供自定义的返回逻辑
        return InstructionConstant.SUCCESS_END.getCode();
    }

    /**
     * 获取配置值（推荐使用）
     */
    protected String getPropertyValue(ComponentDataContext context, String key) {
        return componentPropsManager.getPropertyValue(context, key);
    }

    /**
     * 获取配置值（带默认值）
     */
    protected String getPropertyValue(ComponentDataContext context, String key, String defaultValue) {
        return componentPropsManager.getPropertyValue(context, key, defaultValue);
    }

    /**
     * 获取指定类型的配置值
     */
    protected <T> T getPropertyValue(ComponentDataContext context, String key, Class<T> targetType, T defaultValue) {
        return componentPropsManager.getPropertyValue(context, key, targetType, defaultValue);
    }

    /**
     * 检查属性是否存在
     */
    protected boolean hasProperty(ComponentDataContext context, String key) {
        return componentPropsManager.hasProperty(context, key);
    }
}
