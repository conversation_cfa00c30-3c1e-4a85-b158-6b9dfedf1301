package com.workplat.accept.business.accept.api;

import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Tag(name = "申报表单模块-自定义")
@Validated
@RestController
@RequestMapping("/api/biz/instance/form")
public interface BizInstanceFieldsCustomizeApi {

    @Operation(summary = "获取表单域字段映射")
    @GetMapping("/getFormAreaMapping")
    ResponseData<Map<String, String>> getFormAreaMapping(@Parameter(description = "办件ID") String instanceId,
                                                         @Parameter(description = "表单标题") String title);

}
