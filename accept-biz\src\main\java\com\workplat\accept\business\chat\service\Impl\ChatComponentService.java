package com.workplat.accept.business.chat.service.Impl;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.componentEngine.service.ComponentEngineFactory;
import com.workplat.gss.common.core.response.ResponseData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ChatComponentService {
    
    private final ComponentEngineFactory componentEngineFactory;
    private final ChatMessageService chatMessageService;
    
    public ChatComponentService(ComponentEngineFactory componentEngineFactory,
                                ChatMessageService chatMessageService) {
        this.componentEngineFactory = componentEngineFactory;
        this.chatMessageService = chatMessageService;
    }
    
    public ResponseData<ComponentRunVO> componentRun(ComponentRunDTO componentRunDto) {
        Object object = componentEngineFactory.run(componentRunDto);
        if (object instanceof ComponentRunVO componentRunVO) {
            chatMessageService.saveAiMessage(componentRunDto.getRecordId(), JSONObject.toJSONString(componentRunVO));
            return ResponseData.success(componentRunVO);
        }
        return ResponseData.success(null);
    }

    public void BackFillData(ComponentRunDTO componentRunDto) {
            // 处理提交数据
            componentEngineFactory.backFillData(componentRunDto);
    }

    public Object getNextInstruction(ComponentRunDTO componentRunDto) {
        return componentEngineFactory.getNextInstruction(componentRunDto);
    }
}