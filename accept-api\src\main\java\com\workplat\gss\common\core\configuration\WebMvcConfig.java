package com.workplat.gss.common.core.configuration;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter;
import com.workplat.gss.common.core.constant.DateFormatEnum;
import com.workplat.gss.common.core.context.RequestContextHolder;
import com.workplat.gss.common.core.filter.DesensitizationFilter;
import com.workplat.gss.common.core.filter.DictTransFilter;
import com.workplat.gss.common.core.interceptor.ApplicationMvcInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/9/29 13:34
 * @Description:
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private SaTokenConfig saTokenConfig;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        try {
            SaInterceptor saInterceptor = new SaInterceptor(handler -> {

                String method = RequestContextHolder.getRequestContext().getRequest().getMethod();
                if (StringUtils.equalsIgnoreCase("options", method)) {
                    return;
                }

                //检查登录状态
//                StpUtil.checkLogin();
                //续签
                StpUtil.renewTimeout(saTokenConfig.getTimeout());
            });

            registry.addInterceptor(saInterceptor)
                    .addPathPatterns("/**")
                    .excludePathPatterns(ListUtil.toList(
                            "/api/authentication/doAuth",
                            "/api/validate/code/captcha",
                            "/api/authentication/ssoAuthCenter",
//                            "/api/conf/matter/accept/form/save",
                            "/api/flow/flowable/form/saveOrUpdate",
                            "/api/biz/instance/**",
                            "/api/configuration/dictSub/getDictCacheMapByCode",
                            "/api/configuration/dictSub/getDictAllList",
                            "/api/flow/flowable/**",
                            "/api/conf/matter/accept/form/**",
                            "/api/home-conf/list",
                            "/api/user/rmcLoginByUserId",
                            "/api/chat/ask"
                    ));
            registry.addInterceptor(new ApplicationMvcInterceptor()).addPathPatterns("/**");
//            registry.addInterceptor(loginTicketInterceptor).addPathPatterns("/**")
//                    .excludePathPatterns(ListUtil.toList(
//                            "/api/botc/user/login",
//                            "/api/botc/user/register",
//                            "/api/botc/user/code",
//                            "/api/botc/user/info"
//            ));
        } catch (Exception e) {
            log.info("未引入Satoken，拦截器添加失败。");
        }
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        FastJsonConfig config = new FastJsonConfig();
        config.setDateFormat(DateFormatEnum.DATE_TIME.getPattern());
        config.setReaderFeatures(JSONReader.Feature.FieldBased, JSONReader.Feature.SupportArrayToBean);
        config.setWriterFeatures(
                JSONWriter.Feature.WriteMapNullValue,
                JSONWriter.Feature.PrettyFormat,
                JSONWriter.Feature.WriteNullListAsEmpty,
                JSONWriter.Feature.WriteNullStringAsEmpty,
                JSONWriter.Feature.WriteNullNumberAsZero,
                JSONWriter.Feature.WriteNullBooleanAsFalse
        );
        config.setWriterFilters(new DesensitizationFilter(), new DictTransFilter());
        converter.setFastJsonConfig(config);
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.APPLICATION_JSON));
        converters.add(0, converter);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedHeaders("*")
                .allowedMethods("*");
    }

}
