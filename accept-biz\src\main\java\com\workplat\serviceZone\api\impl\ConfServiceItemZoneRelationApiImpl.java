package com.workplat.serviceZone.api.impl;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.serviceZone.api.ConfServiceItemZoneRelationApi;
import com.workplat.serviceZone.converter.ConfServiceItemZoneRelationConverter;
import com.workplat.serviceZone.dto.ConfServiceItemZoneRelationDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.service.ConfServiceItemService;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import com.workplat.serviceZone.service.ConfServiceZoneService;
import com.workplat.serviceZone.vo.ConfServiceItemZoneRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联关系API实现类
 */
@Slf4j
@RestController
public class ConfServiceItemZoneRelationApiImpl implements ConfServiceItemZoneRelationApi {

    @Autowired
    private ConfServiceItemZoneRelationService relationService;

    @Autowired
    private ConfServiceItemZoneRelationConverter relationConverter;

    @Autowired
    private ConfServiceZoneService confServiceZoneService;

    @Autowired
    private ConfServiceItemService confServiceItemService;


    @ApiLogging(operation = "根据专区ID查询关联关系", module = "服务清单专区关联关系", type = OperationType.QUERY)
    @Override
    public ResponseData<List<ConfServiceItemZoneRelationVO>> getByServiceZoneId(String serviceZoneId) {
        List<ConfServiceItemZoneRelation> entities = relationService.getByServiceZoneIdWithDetails(serviceZoneId);
        List<ConfServiceItemZoneRelationVO> vos = relationConverter.convert(entities);
        return ResponseData.success(vos);
    }

    @ApiLogging(operation = "根据服务清单ID查询关联关系", module = "服务清单专区关联关系", type = OperationType.QUERY)
    @Override
    public ResponseData<List<ConfServiceItemZoneRelationVO>> getByServiceItemId(String serviceItemId) {
        List<ConfServiceItemZoneRelation> entities = relationService.getByServiceItemIdWithDetails(serviceItemId);
        List<ConfServiceItemZoneRelationVO> vos = relationConverter.convert(entities);
        return ResponseData.success(vos);
    }

    @ApiLogging(operation = "批量保存服务清单关联关系", module = "服务清单专区关联关系", type = OperationType.INSERT)
    @Override
    public ResponseData<Void> saveServiceItemZoneRelations(ConfZoneServiceItemRelationsDTO relationsDTO) {
        if (relationsDTO == null) {
            throw new BusinessException("关联关系列表不能为空");
        }
        confServiceItemService.saveServiceItemZoneRelations(relationsDTO.getServiceItemId(), relationsDTO.getBindIds());
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "批量保存专区关联关系", module = "服务清单专区关联关系", type = OperationType.INSERT)
    @Override
    public ResponseData<Void> saveZoneServiceItemRelations(ConfZoneServiceItemRelationsDTO relationsDTO) {
        if (relationsDTO == null) {
            throw new BusinessException("关联关系列表不能为空");
        }
        confServiceZoneService.saveZoneServiceItemRelations(relationsDTO.getServiceZoneId(), relationsDTO.getBindIds());
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "更新关联关系排序", module = "服务清单专区关联关系", type = OperationType.UPDATE)
    @Override
    public ResponseData<Void> updateSort(String id, Integer sort) {
        relationService.updateSort(id, sort);
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "批量更新关联关系排序", module = "服务清单专区关联关系", type = OperationType.UPDATE)
    @Override
    public ResponseData<Void> batchUpdateSort(List<ConfServiceItemZoneRelationDTO> relations) {
        if (relations == null || relations.isEmpty()) {
            throw new BusinessException("关联关系列表不能为空");
        }
        
        List<ConfServiceItemZoneRelation> entities = relationConverter.convertFromDTO(relations);
        relationService.batchUpdateSort(entities);
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "删除关联关系", module = "服务清单专区关联关系", type = OperationType.DELETE)
    @Override
    public ResponseData<Void> deleteById(String id) {
        relationService.deleteById(id);
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "删除专区关联关系", module = "服务清单专区关联关系", type = OperationType.DELETE)
    @Override
    public ResponseData<Void> deleteByServiceZoneId(String serviceZoneId) {
        relationService.deleteByServiceZoneId(serviceZoneId);
        return ResponseData.success().build();
    }

    @ApiLogging(operation = "删除服务清单关联关系", module = "服务清单专区关联关系", type = OperationType.DELETE)
    @Override
    public ResponseData<Void> deleteByServiceItemId(String serviceItemId) {
        relationService.deleteByServiceItemId(serviceItemId);
        return ResponseData.success().build();
    }
}