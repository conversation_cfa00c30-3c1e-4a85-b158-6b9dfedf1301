package com.workplat.extend.socialAssistance.vo;

import lombok.Data;

import java.util.List;

/**
 * 救助平台用户信息实体类
 *
 * <AUTHOR>
 * @date 2025/06/30
 */
@Data
public class AppealInfoVo {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号码
     */
    private String sfz;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别代码
     */
    private String sex;

    /**
     * 性别中文描述
     */
    private String sexCn;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 户籍所在地代码
     */
    private String ssdq;

    /**
     * 户籍所在地中文描述
     */
    private String ssdqCn;

    /**
     * 户籍地详细地址
     */
    private String ssdqXxdz;

    /**
     * 现居地区代码
     */
    private String xjzd;

    /**
     * 现居地区中文描述
     */
    private String xjzdCn;

    /**
     * 现居地详细地址
     */
    private String xjzdXjzd;

    /**
     * 是否有唯一住房标识
     */
    private String sfwyzf;

    /**
     * 是否危房标识
     */
    private String sfwf;

    /**
     * 家庭人口数
     */
    private Integer jtrks;

    /**
     * 家庭是否有在校学生标识
     */
    private String sfyzxxs;

    /**
     * 家庭是否有老人标识
     */
    private String sfylr;

    /**
     * 家庭成员列表
     */
    private List<FamilyMember> jtcyListYd;

    /**
     * 是否离校两年内未就业标识
     */
    private String bylxsj;

    /**
     * 是否离校两年内未就业名称
     */
    private String bylxsjName;

    /**
     * 是否离校两年内未就业中文描述
     */
    private String bylxsjCn;

    /**
     * 是否就业标识
     */
    private String sfjy;

    /**
     * 是否就业中文描述
     */
    private String sfjyCn;

    /**
     * 是否在读标识
     */
    private String sfzd;

    /**
     * 是否在读中文描述
     */
    private String sfzdCn;

    /**
     * 在读年级代码
     */
    private String zdnj;

    /**
     * 在读年级中文描述
     */
    private String zdnjCn;

    /**
     * 是否为孤儿标识
     */
    private String sfwge;

    /**
     * 是否为孤儿中文描述
     */
    private String sfwgeCn;

    /**
     * 是否残疾标识
     */
    private String sfcj;

    /**
     * 是否残疾中文描述
     */
    private String sfcjCn;

    /**
     * 残疾类别代码
     */
    private String cjlb;

    /**
     * 残疾类别中文描述
     */
    private String cjlbCn;

    /**
     * 残疾等级代码
     */
    private String cjdj;

    /**
     * 残疾等级中文描述
     */
    private String cjdjCn;

    /**
     * 是否患重病或罕见病标识
     */
    private String sfhzbhhjb;

    /**
     * 是否患重病或罕见病中文描述
     */
    private String sfhzbhhjbCn;

    /**
     * 重病类型代码
     */
    private String zblx;

    /**
     * 重病类型中文描述
     */
    private String zblxCn;

    /**
     * 是否已确诊宫颈癌或乳腺浸润癌标识
     */
    private String sfyqzgjahrxjra;

    /**
     * 是否已确诊宫颈癌或乳腺浸润癌中文描述
     */
    private String sfyqzgjahrxjraCn;

    /**
     * 是否低保标识
     */
    private String sfdb;

    /**
     * 低保姓名
     */
    private String dbName;

    /**
     * 低保手机号
     */
    private String dbPhone;

    /**
     * 月收入
     */
    private Double ysr;

    /**
     * 是否生活必须支出突然增加标识
     */
    private String sfshbxzctrzj;

    /**
     * 是否生活必须支出突然增加中文描述
     */
    private String sfshbxzctrzjCn;

    /**
     * 是否发生重大特殊情况标识
     */
    private String sffszdtsqk;

    /**
     * 是否发生重大特殊情况中文描述
     */
    private String sffszdtsqkCn;

    /**
     * 是否遭遇灾难性危害标识
     */
    private String sfzyznxwh;

    /**
     * 是否遭遇灾难性危害中文描述
     */
    private String sfzyznxwhCn;

    /**
     * 是否需要法律援助标识
     */
    private String sfxyflyz;

    /**
     * 是否需要法律援助中文描述
     */
    private String sfxyflyzCn;

    /**
     * 诉求状态代码
     */
    private String status;

    /**
     * 诉求状态中文描述
     */
    private String statusCn;

    /**
     * 困难说明
     */
    private String knsm;

    /**
     * 职业代码
     */
    private String zy;

    /**
     * 职业中文描述
     */
    private String zyCn;

    /**
     * 主ID
     */
    private String primaryId;

    /**
     * 是否本地户籍标识
     */
    private String localHj;

    /**
     * 是否本地户籍中文描述
     */
    private String localHjCn;

    /**
     * 诉求来源
     */
    private String sqly;

    /**
     * 额外信息
     */
    private Object extra;

    /**
     * 家庭成员信息内部类
     */
    @Data
    public static class FamilyMember {
        /**
         * 与申请人关系代码
         */
        private String gx;

        /**
         * 与申请人关系中文描述
         */
        private String gxCn;

        /**
         * 姓名
         */
        private String xm;

        /**
         * 身份证号码
         */
        private String sfz;

        /**
         * 年龄
         */
        private Integer age;

        /**
         * 职业代码
         */
        private String zy;

        /**
         * 职业中文描述
         */
        private String zyCn;
    }
}
