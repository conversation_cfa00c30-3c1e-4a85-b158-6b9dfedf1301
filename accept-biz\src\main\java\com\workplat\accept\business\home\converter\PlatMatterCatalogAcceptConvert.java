package com.workplat.accept.business.home.converter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.workplat.accept.business.home.vo.ConfMatterCatalogVO;
import com.workplat.accept.business.home.vo.ConfMatterVO;
import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterCatalog;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/10/19 21:51
 * @Description:
 */
@Component
public class PlatMatterCatalogAcceptConvert implements BaseConverter<ConfMatterCatalog, ConfMatterCatalogVO> {

    @Override
    public ConfMatterCatalogVO convert(ConfMatterCatalog source) {
        ConfMatterCatalogVO target = BaseConverter.super.convert(source);
        // 创建人
        target.setCreatedBy(source.getCreatedBy().getRealName());
        target.setUpdatedBy(source.getUpdatedBy().getRealName());
        List<ConfMatter> children = source.getChildren();
        if (CollectionUtil.isNotEmpty(children)){
            target.setChildren(BeanUtil.copyToList(children, ConfMatterVO.class));
        }
        return target;
    }
}
