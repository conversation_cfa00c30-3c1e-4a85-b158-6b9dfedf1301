package com.workplat.accept.business.chat.service.Impl;

import com.workplat.accept.business.chat.service.bizInstanceMaterialCustomizeService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialSignService;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.gss.file.constant.SysFileSource;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.utils.ImagesUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

@Service
public class bizInstanceMaterialCustomizeServiceImpl implements bizInstanceMaterialCustomizeService {

    private final BizInstanceMaterialSignService bizInstanceMaterialSignService;
    private final SysFileEntityService sysFileEntityService;

    public bizInstanceMaterialCustomizeServiceImpl(BizInstanceMaterialSignService bizInstanceMaterialSignService,
                                                   SysFileEntityService sysFileEntityService) {
        this.bizInstanceMaterialSignService = bizInstanceMaterialSignService;
        this.sysFileEntityService = sysFileEntityService;
    }


    @Override
    public void sign(String instanceId, List<String> signFileIds) {
        // 1. 获取待签名文件
        List<BizInstanceMaterialVO> signList = bizInstanceMaterialSignService.getSignList(instanceId);
        // 2.旋转图片
        List<String> list = signFileIds.stream().map(fileId -> {
            try {
                byte[] bytes = sysFileEntityService.binary(fileId);
                BufferedImage bufferedImage = ImagesUtil.rotateImageByDegrees(ImageIO.read(new ByteArrayInputStream(bytes)), 270);
                // 保存图片
                SysFileUploadModel model = new SysFileUploadModel();
                ByteArrayOutputStream base = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "PNG", base);
                model.setContent(new ByteArrayInputStream(base.toByteArray()));
                model.setFileName("签字.png");
                model.setContentType("image/png");
                model.setLength((long) bytes.length);
                model.setFileSource(SysFileSource.SYSTEM_USER_PRIVATE_FILE.name());
                model.setMd5(DigestUtils.md5Hex(bytes));
                SysFileEntity sysFileEntity = sysFileEntityService.upload(model);
                return sysFileEntity.getId();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }).toList();
        // 2. 保存签名文件
        signList.forEach(v -> bizInstanceMaterialSignService.saveSignImages(v.getId(), list));
        // 3. 签名合成
        try {
            bizInstanceMaterialSignService.signSynthesis(instanceId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
