package com.workplat.flow.api.Impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.componentEngine.dto.ComponentPropsDTO;
import com.workplat.componentEngine.vo.ComponentPropsVO;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.flow.ConfFlowApi;
import com.workplat.flow.converter.ConfFlowDTOConverter;
import com.workplat.flow.dto.ConfFlowDTO;
import com.workplat.flow.dto.ProgressPercentageDTO;
import com.workplat.flow.entity.ConfFlow;
import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.flow.service.ConfFlowComponentService;
import com.workplat.flow.service.ConfFlowService;
import com.workplat.flow.vo.ProgressPercentageVO;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.service.ConfMatterExtendService;
import com.workplat.utils.ChatCacheUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName ConfFlowApiImpl
 * @Description
 * @Date 2025/5/22
 * @Version 1.0.0
 **/
@RestController
public class ConfFlowApiImpl implements ConfFlowApi {

    @Value("${dify.api:http://***************/v1}")
    private String url;
    @Value("${businessWorkFlowKey:app-Jxa8uKSI1A5EVGi0ZyYlbNmq}")
    private String businessWorkFlowKey;

    @Autowired
    private ConfFlowService confFlowService;

    @Autowired
    private ConfFlowComponentService confFlowComponentService;

    @Autowired
    private ConfComponentService confComponentService;

    @Autowired
    private ConfFlowDTOConverter confFlowDTOConverter;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BizInstanceFieldsService instanceFieldsService;

    @Autowired
    private BizChatConversationService chatConversationService;

    @Autowired
    private ChatCacheUtil chatCacheUtil;

    @Autowired
    ConfMatterExtendService confMatterExtendService;

    @Autowired
    BizInstanceInfoService bizInstanceInfoService;

    @Override
    public ResponseData<Void> save(ConfFlowDTO confFlowDTO) {

        ConfFlow confFlow = new ConfFlow();
        // 编辑操作
        if (StringUtils.isNotBlank(confFlowDTO.getId())){
            confFlow = confFlowService.queryById(confFlowDTO.getId());
            // 删除流程的原组件
            confFlowComponentService.deleteByParams(MapUtil.<String, Object>builder().put("=(confFlow.id)", confFlowDTO.getId()).build());
            // 重复编码判断
        } else if (confFlowService.queryForCount(MapUtil.<String, Object>builder().put("=(code)", confFlowDTO.getCode()).build()) > 0){
            return ResponseData.error("流程编码已存在");
        }
        if (StringUtils.isNotBlank(confFlowDTO.getName())){
            confFlow.setName(confFlowDTO.getName());
        }
        if (StringUtils.isNotBlank(confFlowDTO.getCode())){
            confFlow.setCode(confFlowDTO.getCode());
        }
        if (StringUtils.isNotBlank(confFlowDTO.getDescription())){
            confFlow.setDescription(confFlowDTO.getDescription());
        }
        confFlowService.save(confFlow);

        for (ConfFlowDTO.ConfComponentDTO confComponentDTO : confFlowDTO.getComponents()) {
            ConfComponent confComponent = confComponentService.queryById(confComponentDTO.getComponentId());
            ConfFlowComponent confFlowComponent = new ConfFlowComponent();
            confFlowComponent.setConfComponent(confComponent);
            confFlowComponent.setConfFlow(confFlow);
            confFlowComponent.setConfiguration(confComponentDTO.getConfiguration());
            confFlowComponent.setSequenceOrder(confComponentDTO.getSequenceOrder());
            confFlowComponentService.save(confFlowComponent);
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Page<ConfFlowDTO>> page(PageableDTO pageDTO, String keyword) {
        HashMap<String, Object> param = new HashMap<>();
        if (StringUtil.isNotBlank(keyword)) {
            param.put("like(name)", keyword);
        }
        Page<ConfFlow> page = confFlowService.queryForPage(param, pageDTO.convertPageable());
        return ResponseData.success(confFlowDTOConverter.convert(page));
    }

    @Override
    public ResponseData<ConfFlowDTO> queryById(String id) {
        ConfFlow confFlow = confFlowService.queryById(id);
        if (confFlow != null){
            return ResponseData.success(confFlowDTOConverter.convert(confFlow));
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<ConfFlowDTO> queryByCode(String code) {
        ConfFlow confFlow = confFlowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", code).build());
        if (confFlow != null){
            return ResponseData.success(confFlowDTOConverter.convert(confFlow));
        }
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<Void> deleteById(String id) {
        confFlowService.deleteById(id);
        confFlowComponentService.deleteByParams(MapUtil.<String, Object>builder().put("=(confFlow.id)", id).build());
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<ProgressPercentageVO> getProgressPercentage(ProgressPercentageDTO dto) {
        ProgressPercentageVO vo = new ProgressPercentageVO();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(dto.getInstanceId());
        vo.setMatterName(bizInstanceInfo.getMatterName());

        String getConversationsUrl = url + "/conversations/" + dto.getChatId() + "/variables?user=" + dto.getUserId();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(businessWorkFlowKey);
        HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);
        ResponseEntity<String> strResult = restTemplate.exchange(getConversationsUrl, HttpMethod.GET, requestEntity, String.class);
        JSONArray array = Objects.requireNonNull(JSON.parseObject(strResult.getBody())).getJSONArray("data");
        // <当前流程节点>环境变量
        String currentFlow = "";
        // <流程节点>环境变量
        String flowNode = "";
        for (int i = 0; i < array.size(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            if ("currentFlow".equals(jsonObject.getString("name"))){
                currentFlow = jsonObject.getString("value");
            }
            if ("flowNode".equals(jsonObject.getString("name"))){
                flowNode = jsonObject.getString("value");
            }
        }
        String[] nodesArray = flowNode.replaceAll("[\\[\\]' ]", "").split(",");
        // 查找当前节点的索引位置
        int currentIndex = -1;
        for (int i = 0; i < nodesArray.length; i++) {
            if (currentFlow.equals(nodesArray[i])) {
                currentIndex = i;
                break;
            }
        }
         // 如果没找到当前节点，返回0
        if (currentIndex == -1) {
            vo.setPercentage("0%");
            return ResponseData.success(vo);
        }
        // 如果是PC端，只通过组件进行百分比计算
        if (StringUtils.isNotBlank(dto.getChannel()) && "pc".equals(dto.getChannel())){
            vo.setPercentage((currentIndex + 1) * 100 / nodesArray.length + "%");
            return ResponseData.success(vo);
        }
        int  percentage = (currentIndex + 1) * 50 / nodesArray.length;
        // 已经到最后一个流程节点，返回100%
        if (percentage == 50){
            vo.setPercentage("100%");
            return ResponseData.success(vo);
        }
        // 通过表单域计算表单填写进度
        BizChatConversation chatConversation = chatConversationService.queryForSingle(ImmutableMap.of("=(instanceId)", dto.getInstanceId()));
        if (chatConversation == null){
            vo.setPercentage("0%");
            return ResponseData.success(vo);
        }
        // chatConversation.getId() recordId
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(chatConversation.getId());
        if (chatProcessDTO != null && chatProcessDTO.getFormStepCount() != null && chatProcessDTO.getFormStepIndex() != null){
            int completedFields = chatProcessDTO.getFormStepIndex() * 50 / chatProcessDTO.getFormStepCount() ;
            completedFields = Math.min(completedFields, 50);
            percentage = completedFields + percentage;

        }
        percentage = Math.min(percentage, 95);
        vo.setPercentage(percentage + "%");
        return ResponseData.success(vo);
    }

    @Override
    public ResponseData<List<ComponentPropsVO>> getComponentProperties(String matterCode, String codes) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterCode)", matterCode)
                .build());
        Map<String, Map<String, String>> extendPropsMap = getExtendPropsMap(extend.getId());
        List<ComponentPropsVO> list = Lists.newArrayList();
        if (StringUtils.isNotBlank(codes)) {
            String[] split = codes.split(",");
            for (String code : split) {
                ResponseData<ConfFlowDTO> confFlowDTOResponseData = this.queryByCode(code);
                if (confFlowDTOResponseData.getData() != null) {
                    for (ConfFlowDTO.ConfComponentDTO component : confFlowDTOResponseData.getData().getComponents()) {
                        // 根据组件code去重
                        boolean exists = false;
                        for (ComponentPropsVO dto : list) {
                            if (dto.getComponentCode().equals(component.getComponentCode())) {
                                exists = true;
                                break;
                            }
                        }
                        if (!exists) {
                            ComponentPropsVO propsVO = new ComponentPropsVO();
                            propsVO.setComponentName(component.getComponentName());
                            propsVO.setComponentCode(component.getComponentCode());
                            propsVO.setSequenceOrder(component.getSequenceOrder());

                            List<ComponentPropsVO.PropsVO> props = Lists.newArrayList();

                            if (StringUtils.isNotBlank(component.getReplaceableProps())){

                                // 解析JSON时保持顺序
                                Map<String, String> replaceablePropsMap =
                                        JSON.parseObject(component.getReplaceableProps(), new TypeReference<LinkedHashMap<String, String>>() {});
                                Map<String, String> componentPropsMap = extendPropsMap.get(component.getComponentCode());

                                replaceablePropsMap.forEach((key, value) -> {
                                    ComponentPropsVO.PropsVO propsDTO = new ComponentPropsVO.PropsVO();
                                    propsDTO.setLabel(value);
                                    propsDTO.setName(key);
                                    propsDTO.setValue(componentPropsMap == null ? null : componentPropsMap.get(key));
                                    props.add(propsDTO);
                                });
                                propsVO.setProps(props);
                                // 如果组件没有可替换属性，则不添加
                                if (!props.isEmpty()) {
                                    list.add(propsVO);
                                }
                            }
                        }
                    }
                }
            }
        }
        return ResponseData.success(list.stream().sorted(Comparator.comparing(ComponentPropsVO::getSequenceOrder)).toList());
    }

    /**
     * 根据事项扩展ID获取扩展属性映射
     *
     * @param matterExtendId 事项扩展ID
     * @return 属性名到属性值的映射关系，如果未找到对应属性则返回空Map
     */
    private Map<String, Map<String, String>> getExtendPropsMap(String matterExtendId) {
        ConfMatterExtend confMatterExtend = confMatterExtendService.queryById(matterExtendId);
        List<ComponentPropsDTO> componentProps = JSON.parseArray(confMatterExtend.getComponentProps(), ComponentPropsDTO.class);
        Map<String, Map<String, String>> extendPropsMap = new HashMap<>();
        if (componentProps == null || componentProps.isEmpty()) {
            return new HashMap<>();
        }
        componentProps.forEach(componentPropsDTO -> {
            if (componentPropsDTO.getProps() == null || componentPropsDTO.getProps().isEmpty()) {
                return;
            }
            Map<String, String> componentPropsMap = new HashMap<>();
            componentPropsDTO.getProps().forEach(propsVO -> {
                if (StringUtils.isNotBlank(propsVO.getValue())) {
                    componentPropsMap.put(propsVO.getName(), propsVO.getValue());
                }
            });
            extendPropsMap.put(componentPropsDTO.getComponentCode(), componentPropsMap);
        });
        return extendPropsMap;
    }
}



