package com.workplat.accept.business.chat.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;

@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "biz_chat_message")
@Getter
@Setter
public class BizChatMessage extends BaseEntity {

    @Comment("会话ID")
    @Column(name = "conversation_id")
    private String conversationId;

    @Comment("发送者")
    @Column(name = "sender", length = 32)
    private String sender;

    @Comment("消息内容")
    @Lob
    @Column(name = "content", columnDefinition = "LONGTEXT")
    private String content;
} 