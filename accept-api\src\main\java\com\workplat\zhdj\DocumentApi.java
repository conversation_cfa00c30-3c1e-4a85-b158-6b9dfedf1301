package com.workplat.zhdj;

import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "zhdj")
@RestController
@RequestMapping(value = "/api/zhdj/document")
@CrossOrigin(origins = "*", maxAge = 3600)
public interface DocumentApi {

    @PostMapping("/updateFile")
    @Operation(summary = "文件保存")
    String documentUpdate(String bizInstanceId, String fileId, HttpServletRequest request, HttpServletResponse response);

}
