package com.workplat.accept.business.chat.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @ClassName CommonUserVO
 * @Description 用户视图对象
 * <AUTHOR>
 * @Date 2021/7/7 14:22
 * @Version V1.0
 **/
@Data
public class CommonUserVO {

    /**
     * 用户ID
     */
    private String id;
    /**
     * 用户登录名
     */
    private String loginName;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 身份证号码
     */
    private String certificateNumber;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 姓名简拼
     */
    private String pinyin;
    /**
     * 姓名全拼
     */
    private String pinyinFull;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 用户性别
     */
    private String gender;
    /**
     * 民族
     */
    private String nation;
    /**
     * 出生日期
     */
    private Date birthday;
    /**
     * 省名称
     */
    private String province;
    /**
     * 市名称
     */
    private String city;
    /**
     * 区名称
     */
    private String county;
    /**
     * 户口性质
     */
    private String householdNature;
    /**
     * 户籍所在地址
     */
    private String householdAddress;
    /**
     * 文化程度
     */
    private String education;
    /**
     * 政治面貌
     */
    private String political;
    /**
     * 首次工作时间
     */
    private Date firstWorkDate;
    /**
     * 工作年限
     */
    private int workYears;
    /**
     * 进入现单位日期
     */
    private Date unitWorkDate;
    /**
     * 现单位工作年限
     */
    private int nowWorkYears;
    /**
     * 身份性质
     */
    private String identityNature;
    /**
     * 工作类型
     */
    private String workType;
    /**
     * 区域代码
     */
    private String regionCode;
    /**
     * 用户是从哪个终端注册的
     */
    private String registerBy;
    /**
     * 企业电子证照序列号
     */
    private String entLicenseSn;
    /**
     * 用户类型 person - 个人、enterprise - 企业
     */
    private String userType;
    /**
     * 企业类型，仅当用户为企业时这个字段才存在内容  1-企业，8-个体户
     */
    private String entType;

    private AioEnterpriseUserVO aioEnterpriseUserVO;


    @Getter
    @Setter
    public static class AioEnterpriseUserVO {

        /**
         * 用户id
         */
        private String userId;

        /**
         * 企业名称
         */
        private String enterpriseName;

        /**
         * 统一社会信用代码
         */
        private String enterpriseLicense;

        /**
         * 法人名称
         */
        private String legalPersonName;


        /**
         * 法人身份证
         */
        private String legalPersonPaperCard;

        /**
         * 法人手机号
         */
        private String legalPersonPhone;

        /**
         * 经办人姓名
         */
        private String linkManName;

        /**
         * 经办人手机号
         */
        private String linkManPhone;

        /**
         * 经办人身份证号
         */
        private String linkManPaperCard;

    }

}
