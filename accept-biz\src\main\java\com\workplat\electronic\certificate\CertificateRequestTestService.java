package com.workplat.electronic.certificate;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.common.core.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * todo 电子证照获取
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 10:06
 */
@Service
@Slf4j
public class CertificateRequestTestService {

    public static final String URL = "http://*************42:8084";

    public static void main(String[] args) {
        String body = findCertificateByHolderCodeAndTypeCodes();
        byte[] bytes = downloadCertificate(body);
    }

    public static String findCertificateByHolderCodeAndTypeCodes() {
        String body = HttpRequest.post(URL + "/api/safety/certificate/custom/findCertificateByHolderCodeAndTypeCodes")
                .body(new JSONObject()
                        .fluentPut("platformCode", "allinone")
                        .fluentPut("serviceItemCode", "query")
                        .fluentPut("serviceItemName", "查询事项")
                        .fluentPut("usageIp", "*************")
                        .fluentPut("certificateHolderCode", Sm4Util.encodeStr("320522199208160021", "qvOECaTrKRUgfl4i"))
                        .fluentPut("certificateTypeCodes", ListUtil.of("GA_0121"))
                        .fluentPut("queryPersonName", Sm4Util.encodeStr("我不到啊", "qvOECaTrKRUgfl4i"))
                        .fluentPut("queryPersonCardId", Sm4Util.encodeStr("我不到啊", "qvOECaTrKRUgfl4i"))
                        .fluentPut("holderType", "PERSON")
                        .toJSONString()

                )
                .execute()
                .body();

        return body;
    }

    public static byte[] downloadCertificate(String body) {
        String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("previewCertFileId");
//        String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("certFileId");

        byte[] bytes = HttpRequest.post(URL + "/api/safety/certificate/custom/downloadCertificate")
                .body(new JSONObject()
                        .fluentPut("platformCode", "allinone")
                        .fluentPut("serviceItemCode", "query")
                        .fluentPut("serviceItemName", "查询事项")
                        .fluentPut("usageIp", "*************")
                        .fluentPut("certFileId", certFileId)
                        .toJSONString()
                )
                .execute()
                .bodyBytes();
        FileUtil.writeBytes(bytes, new File("D:\\test.pdf"));
        return bytes;
    }
}
