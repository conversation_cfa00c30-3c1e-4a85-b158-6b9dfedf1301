package com.workplat.push.power.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.push.power.model.MattersDeclareFormRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: MattersDeclareService
 * @Description:事项申报Service
 * @Author: Yang Fan
 * @Date: 2025-03-06 15:32
 * @Version
 **/
@Slf4j
@Service
public class MattersDeclareService {

    private static final String applyApiUrlProd = "/power-api";

    private final SysFileEntityService entityService;

    public MattersDeclareService(SysFileEntityService entityService) {
        this.entityService = entityService;
    }

    public void executed(JSONObject itemInfoJson, BizInstanceInfoDetailVO detailVOById) {
        String initApplyInfoStr = initApplyInfo(itemInfoJson.getString("taskId"));
        if (initApplyInfoStr != null) {
            // 2.保存表单
            MattersDeclareFormRequest formRequest = getMattersDeclareFormRequest(initApplyInfoStr,detailVOById);
            // 保存表单
            saveFormApplyInfo(formRequest);
            // 3.上传文件
            Map<String, List<BizInstanceMaterialFileVO>> aiMaterialFileMap = new HashMap<>();
            detailVOById.getBizInstanceMaterialVOS()
                    .forEach(bizInstanceMaterialVO ->
                            // AI中枢 材料code：材料文件列表
                            aiMaterialFileMap.put(bizInstanceMaterialVO.getMaterialCode(),
                                    bizInstanceMaterialVO.getMaterialFileVOList()));
            JSONArray materialsArr = JSONObject.parseObject(initApplyInfoStr).getJSONArray("materials");
            // 获取当前受理平台的事项材料ID和AI中心事项材料ID的映射关系
            JSONObject materialsMappingJson = itemInfoJson.getJSONObject("materials");
            for (int i = 0; i < materialsArr.size(); i++) {
                JSONObject material = materialsArr.getJSONObject(i);
                // 受理平台的办件生成的材料 ID
                String applyInfoMaterialId = material.getString("id");
                // 受理平台的事项材料ID
                String materialId = material.getString("materialId");
                for (String aiMaterialCode : materialsMappingJson.getString(materialId).split(",")) {
                    List<BizInstanceMaterialFileVO> materialFileVOList = aiMaterialFileMap.get(aiMaterialCode);
                    if (materialFileVOList != null) {
                        for (BizInstanceMaterialFileVO materialFileVO : materialFileVOList) {
                            String fileId = materialFileVO.getFileId();
                            String originalFileName = materialFileVO.getFileName(); // 获取原始文件名
                            try {
                                InputStream inputStream = entityService.getInputStream(fileId);
                                // 创建具有原始文件名的临时文件
                                File tempFile = File.createTempFile(originalFileName.split("\\.")[0], "." + originalFileName.split("\\.")[1]);
                                java.nio.file.Files.copy(inputStream, tempFile.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                                String uploadApplyInfoMaterial = uploadApplyInfoMaterial(applyInfoMaterialId, tempFile);
                                if (uploadApplyInfoMaterial != null) {
                                    log.info("uploadApplyInfoMaterial responseString:{}", uploadApplyInfoMaterial);
                                    // 删除临时文件
                                    tempFile.delete();
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
            // 4.提交申报
            JSONObject initApplyInfoJson = JSON.parseObject(initApplyInfoStr);
            String submitApplyInfo = submitApplyInfo(initApplyInfoJson.getString("id"));
            if (submitApplyInfo != null) {
                log.info("submitApplyInfo responseString:{}", submitApplyInfo);
            }
        }
    }

    @NotNull
    private static MattersDeclareFormRequest getMattersDeclareFormRequest(String initApplyInfoStr, BizInstanceInfoDetailVO detailVOById) {
        MattersDeclareFormRequest formRequest = new MattersDeclareFormRequest();
        JSONObject applyInfoJson = JSONObject.parseObject(initApplyInfoStr);
        formRequest.setId(applyInfoJson.getString("id"));
        formRequest.setApplicantType(StringUtils.isNoneBlank(detailVOById.getSocialCreditCode()) ? "2" : "1");
//            formRequest.setWebUserId(userJson == null ? "01d4226906d6464e8214e925f4954f98" : userJson.getId());
        formRequest.setDataSource("blbb");
        // 申请人信息
        if (StringUtils.isNoneBlank(detailVOById.getSocialCreditCode())) {
            // 企业申请人
            formRequest.setApplicantName(detailVOById.getCompanyName());
            formRequest.setApplicantAddress(detailVOById.getUnitAddress());
            formRequest.setApplicantPaperType("8");
            formRequest.setApplicantPaperCode(detailVOById.getSocialCreditCode());
            formRequest.setOperManName(detailVOById.getLegalPersonName());
            formRequest.setOperManPaperCode(detailVOById.getLegalPersonCertificateCode());
            formRequest.setApplicantMobile(detailVOById.getUnitPhone());
        } else {
            // 个人申请人
            formRequest.setApplicantName(StringUtils.isNotBlank(detailVOById.getApplicationName())?detailVOById.getApplicationName() : "杨帆");
            formRequest.setApplicantPaperType("1");
            formRequest.setApplicantPaperCode(StringUtils.isNotBlank(detailVOById.getApplicationCertificateCode())?detailVOById.getApplicationCertificateCode():"610431199905063016");
            formRequest.setApplicantMobile(StringUtils.isNotBlank(detailVOById.getApplicationPhone()) ? detailVOById.getApplicationPhone() : "15612345678");
        }

        // 经办人信息
        formRequest.setLinkManName(detailVOById.getLinkName());
        formRequest.setLinkManPaperType("1");
        formRequest.setLinkManPaperCode(detailVOById.getLinkCertificateCode());
        formRequest.setLinkManMobile(detailVOById.getLinkCertificateCode());
        formRequest.setLinkManPhone(detailVOById.getLinkPhone());
        formRequest.setLinkManAddress(detailVOById.getApplicationAddress());
        return formRequest;
    }


    public String getProjectArea(String ProjectArea) {
        if (ProjectArea == null) {
            return null;
        }
        String url = applyApiUrlProd + "/api/acceptance/dict/projectArea";
        String executeRequest = GatewayUtils.executeGetRequest(url, null,  null, false, false);
        if (executeRequest != null) {
            JSONObject jsonObject = JSONObject.parseObject(executeRequest);
            if (jsonObject != null) {
                JSONArray content = jsonObject.getJSONArray("data");
                for (int i = 0; i < content.size(); i++) {
                    JSONObject jsonObject1 = content.getJSONObject(i);
                    if (ProjectArea.contains(jsonObject1.getString("dictName"))) {
                        return jsonObject1.getString("dictCode");
                    }
                }
            }
        }
        return null;
    }


    /**
     * 初始化申请信息
     *
     * @param taskId
     * @return String
     */
    public String initApplyInfo(String taskId) {
        String url = applyApiUrlProd + "/api/acceptance/init";
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("taskId", taskId);
        String body = JSONObject.toJSONString(map);
        return GatewayUtils.executePostToBodyRequest(url, null, body, false, false);
    }

    public void saveFormApplyInfo(MattersDeclareFormRequest request) {
        String url = applyApiUrlProd + "/api/acceptance/form/save";
        String body = JSONObject.toJSONString(request);
        log.info("saveFormApplyInfo url:{}", url);
        String responseString = GatewayUtils.executePostToBodyRequest(url, null, body, false, false);
        if (responseString != null) {
            JSONObject jsonObject = JSONObject.parseObject(responseString);
            if (jsonObject != null && jsonObject.getInteger("status") == 200) {
                log.info("saveFormApplyInfo responseString:{}", responseString);
            }
        }
    }

    /**
     * 提交申请信息
     *
     * @param acceptanceId
     * @return
     */
    public String submitApplyInfo(String acceptanceId) {
        String url = applyApiUrlProd + "/api/acceptance/register";
        log.info("submitApplyInfo url:{}", url);
        return GatewayUtils.executePostToFormRequest(url, null, ImmutableMap.of("acceptanceId", acceptanceId), false, false);
    }

    // 上传申报材料
    public String uploadApplyInfoMaterial(String materialId, File file) {
        String url = applyApiUrlProd + "/api/acceptance/fileUpload";
        log.info("uploadApplyInfoMaterial url:{}", url);
        return GatewayUtils.executePostToFormRequest(url, null, ImmutableMap.of("file", file, "materialId", materialId), false, false);
    }
}