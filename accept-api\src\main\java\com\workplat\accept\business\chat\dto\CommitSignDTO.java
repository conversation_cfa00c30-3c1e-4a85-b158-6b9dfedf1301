package com.workplat.accept.business.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CommitSignDTO {

    @Schema(description = "记录ID")
    private String recordId;

    @Schema(description = "签名文件id")
    private String signFileId;

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "签名状态")
    private String signStatus;
}
