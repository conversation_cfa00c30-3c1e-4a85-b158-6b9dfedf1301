
### 各中心开发规范

各中心的模块划分分为三块：

- xxx-biz 启动类所在模块、业务实现层
- xxx-api 对外交互api、dto层
- xxx-dubbo 对内dubbo服务层

其依赖关系为：

```
|-xxx-biz
  |xxx-api
    |-xxx-dubbo
    
biz依赖api、api依赖dubbo
```

### API接口路径规范

```
/api/{中心名称}/{模块名称}/{功能}/{......}

例：配置中心字典模块根据code查询字典功能
/api/configuration/dict/getByCode
```

## 分支说明

- master 主分支
- release 发布分支
- test 测试分支
- dev 开发分支

## 架构说明

### 前端技术架构

### 后端技术架构

| 技术                   | 说明          | 官网                                      |
|----------------------|-------------|-----------------------------------------|
| SpringBoot           | 容器+MVC框架    | https://spring.io/projects/spring-boot  |
| Spring Cloud         | 微服务框架       | https://spring.io/projects/spring-cloud |
| Spring Cloud Alibaba | 阿里微服务       | https://sca.aliyun.com/                 |
| Logback              | 日志框架        | https://logback.qos.ch/                 |
| Dubbo                | 微服务调用框架     | https://cn.dubbo.apache.org/zh-cn/      |
| Knife4j              | 文档生产工具      | https://doc.xiaominfo.com/              |
| Fastjson2            | JSON序列化工具   | https://github.com/alibaba/fastjson2    |
| Redis                | 分布式缓存       | https://redis.io/                       |
| Caffeine             | 内存缓存        | https://github.com/ben-manes/caffeine   |
| MongoDB              | NoSql数据库    | https://www.mongodb.com                 |
| Druid                | 数据库连接池      | https://github.com/alibaba/druid        |
| Lombok               | 简化对象封装工具    | https://github.com/rzwitserloot/lombok  |
| Guava                | google常用工具库 | https://github.com/google/guava         |
| Hutool               | Java工具类库    | https://hutool.cn/                      |
| OkHttp               | Java网络请求框架  | https://square.github.io/okhttp/        |
| Sa-Token             | 权限认证框架      | https://sa-token.cc/                    |

## 运行环境

### 启动前端项目

### 启动后端项目

## 开发规范

### Git提交规范

安装idea插件：`Git Commit Message Helper`  
提交代码时，勾选提交类型、填写相应的功能模块以及简要描述

### 分支开发规范

后台开发人员在功能开发过程中以新建分支的形式进行开发，待完整的功能模块开发完成后合并回dev分支

新建分支的命名规则以分支名称+功能模块名称+功能名称，如：`dev-xxxx-xxxx`，合并回dev分支后及时删除原有开发分支

### 开发环境连接信息
|   类型    |          地址           |    账号    |      密码       |                备注                 |
|:-------:|:---------------------:|:--------:|:-------------:|:---------------------------------:|
|  nacos  | 192.168.124.215:8848  |  nacos   | Risesoft123!@ | http://192.168.124.215:8848/nacos |



### 数据库实体类命名规范

| 功能模块 | 数据库表名前缀 | 实体类名前缀 |        路径前缀        |
|:----:|:-------:|:------:|:------------------:|
| 系统配置 |  sys_   |  Sys   |     /system/*      |
| 业务配置 |  conf_  |  Conf  |      /conf/*       |
| 业务相关 |  biz_   |  Biz   |    /business/*     |
| 日志相关 |  log_   |  Log   |   /system/log/*    |
| 定制相关 |  cus_   |  Cus   | /business/custom/* |

## 踩坑记录
### 1.项目启动时Flowable无法自动建表
数据库连接添加配置:nullCatalogMeansCurrent=true
### 2.查询报错Error attempting to apply AttributeConverter
检查数据库表create_by字段，是否为正确的用户ID，如果为mock测试，可直接设置为`7bdacbd378644992989b44e672c02305`
### 3.项目启动添加vm参数
参考文章:https://zhuanlan.zhihu.com/p/552490625

```--add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED```
