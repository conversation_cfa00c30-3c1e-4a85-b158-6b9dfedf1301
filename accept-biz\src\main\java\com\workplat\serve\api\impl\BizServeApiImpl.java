package com.workplat.serve.api.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.util.ObjectUtils;
import com.workplat.gss.common.core.util.RedisUtil;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterGuide;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterGuideService;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.serve.api.BizServeApi;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.accept.business.serve.vo.BizServeInfoVo;
import com.workplat.gss.common.core.dto.DictModel;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.dict.service.SysDictionarySubService;
import com.workplat.serve.converter.BizServeInfoConverter;
import com.workplat.serve.dto.*;
import com.workplat.serve.importData.TechTalentApartExcel;
import com.workplat.serve.importData.TechTalentApartExcelListener;
import com.workplat.serve.service.BizServeInfoService;
import com.workplat.serve.service.BizServeMethodService;
import jakarta.servlet.http.HttpServletResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
public class BizServeApiImpl implements BizServeApi {

    @Autowired
    private BizServeInfoService bizServeInfoService;

    @Autowired
    private BizServeInfoConverter bizServeInfoConverter;

    @Autowired
    private BizServeMethodService bizServeMethodService;

    @Autowired
    private ConfMatterService confMatterService;

    @Autowired
    private ConfMatterGuideService confMatterGuideService;

    @Autowired
    private SysDictionarySubService sysDictionarySubService;
    @Autowired
    private RedisUtil redisUtil;

    private static String infoType = "app";

    private static String methodType = "XSB";

    //认证等级
    private static String RZDJ = "serve_certification_level";

    //服务类型
    private static String FWLX = "serve_type";

    //办事指南类型
    private static String BSZNLX = "guide_type";

    //渠道类型
    private static String QDLX = "serve_method";
    private static String redisKey = "serve_method_default";


    @Override
    public ResponseData<Page<BizServeInfoVo>> queryListByName(KeyDto keyDto, PageableDTO pageDTO) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("LIKE(name)", "");
        if (keyDto != null) {
            String keyWord = keyDto.getName();
            String enable = keyDto.getEnable();
            String thirdParty = keyDto.getThirdParty();
            if (StringUtil.isNotBlank(keyWord)) {
                map.put("LIKE(name)", keyWord);
            }
            if (StringUtil.isNotBlank(enable)) {
                if ("1".equals(enable)) {
                    map.put("=(enable)", true);
                }
                if ("0".equals(enable)) {
                    map.put("=(enable)", false);
                }
            }
            if (StringUtil.isNotBlank(thirdParty)) {
                if ("1".equals(thirdParty)) {
                    map.put("=(thirdParty)", true);
                }
                if ("0".equals(thirdParty)) {
                    map.put("=(thirdParty)", false);
                }
            }
        }

        Page<BizServeInfo> bizServeInfos = bizServeInfoService.queryForPage(map, pageDTO.convertPageable());

        return ResponseData.success("获取成功", bizServeInfoConverter.convert(bizServeInfos));
    }

    @Override
    public ResponseData<BizServeInfoVo> getById(String id) {
        BizServeInfo bizServeInfo = bizServeInfoService.queryById(id);
        BizServeInfoVo vo = bizServeInfoConverter.convert(bizServeInfo);
        itemConverter(bizServeInfo, vo);
        return ResponseData.success("获取成功", vo);
    }

    @Override
    public ResponseData<BizServeInfoVo> getByIdToChannel(String id, String channel) {
        BizServeInfo bizServeInfo = bizServeInfoService.queryById(id);
        BizServeInfoVo vo = bizServeInfoConverter.convert(bizServeInfo,channel);
        itemConverter(bizServeInfo, vo);
        return ResponseData.success("获取成功", vo);
    }

    @Override
    public ResponseData<List<BizServeInfoVo>> getByIds(String ids) {
        String[] idList = ids.split(",");
        List<BizServeInfo> bizServeInfos = bizServeInfoService.queryByIds(idList);
        return ResponseData.success("获取成功", bizServeInfoConverter.convert(bizServeInfos));
    }

    @Override
    public ResponseData deleteInfo(BizServeInfoDto bizServeInfoDto) {
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                bizServeMethodService.deleteById(methodDto.getId());
            }
        }
        bizServeInfoService.deleteById(bizServeInfoDto.getId());
        return ResponseData.success("删除成功");
    }

    @Override
    public ResponseData updateById(BizServeInfoDto bizServeInfoDto) {
        if (StringUtils.isNotBlank(bizServeInfoDto.getId())) {
            bizServeMethodService.deleteByParams(MapUtil.<String, Object>builder().put("=(serve.id)", bizServeInfoDto.getId()).build());
        }
        BizServeInfo bizServeInfo = bizServeInfoService.queryById(bizServeInfoDto.getId());
        List<BizServeMethod> list = Lists.newArrayList();
        bizServeInfo.setCode(bizServeInfoDto.getCode());
        bizServeInfo.setName(bizServeInfoDto.getName());
        bizServeInfo.setDescription(bizServeInfoDto.getDescription());
        bizServeInfo.setGuideType(bizServeInfoDto.getGuideType());
        bizServeInfo.setType(bizServeInfoDto.getType());

        List<DictModel> guideTypeDict = sysDictionarySubService.getDictCacheListByCode("serve_type");
        for (DictModel dictModel : guideTypeDict) {
            if (dictModel.getValue().equals(bizServeInfoDto.getType())) {
                bizServeInfo.setType(dictModel.getKey());
                break;
            }
        }

        bizServeInfo.setEnable(bizServeInfoDto.getEnable());
        bizServeInfo.setThirdParty(bizServeInfoDto.getThirdParty());
        bizServeInfo.setGuideType(bizServeInfoDto.getGuideType());
        bizServeInfo.setGuideUrl(bizServeInfoDto.getGuideUrl());
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                BizServeMethod method = new BizServeMethod();
                method.setType(methodDto.getType());
                method.setContent(methodDto.getContent());
                method.setCertificationLevel(methodDto.getCertificationLevel());
                method.setIconId(methodDto.getIconId());
                method.setDescription(methodDto.getDescription());
                method.setServe(bizServeInfo);
                method.setSort(methodDto.getSort());
                list.add(method);
                bizServeMethodService.save(method);
            }
        }
        bizServeInfo.setMethodList(list);
        bizServeInfoService.save(bizServeInfo);
        return ResponseData.success("更新成功");
    }

    @Override
    public ResponseData addInfo(BizServeInfoDto bizServeInfoDto) {
        BizServeInfo bizServeInfo = new BizServeInfo();
        BeanUtil.copyProperties(bizServeInfoDto, bizServeInfo, ObjectUtils.getNullPropertyNames(bizServeInfoDto));
        List<BizServeMethod> methodList = Lists.newArrayList();
        if (!bizServeInfoDto.getMethodList().isEmpty()) {
            for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
                BizServeMethod method = new BizServeMethod();
                BeanUtil.copyProperties(methodDto, method);
                method.setServe(bizServeInfo);
                methodList.add(method);
            }
        }
        bizServeInfoService.save(bizServeInfo);
        if (!methodList.isEmpty()) {
            bizServeMethodService.saveList(methodList);
        }
        return ResponseData.success("新增成功");
    }


    @Override
    public ResponseData importData(MultipartFile file) {

        try {
            List<TechTalentApartExcel> techTalentApartExcels = EasyExcel.read(file.getInputStream(), new TechTalentApartExcelListener()).head(TechTalentApartExcel.class).sheet(0).headRowNumber(1).doReadSync();
            if (CollectionUtils.isNotEmpty(techTalentApartExcels)) {

                for (TechTalentApartExcel techTalentApartExcel : techTalentApartExcels) {

                    if (techTalentApartExcel != null) {
                        List<DictModel> dictCacheListRZDJ = sysDictionarySubService.getDictCacheListByCode(RZDJ);

                        // 根据应用名称查找是否已存在 BizServeInfo
                        BizServeInfo existingBizServeInfo = bizServeInfoService.queryForSingle(
                            MapUtil.<String, Object>builder()
                                .put("=(name)", techTalentApartExcel.getYymc())
                                .put("=(type)", infoType)
                                .build()
                        );

                        BizServeInfo savedBizServeInfo;
                        if (existingBizServeInfo != null) {
                            // 如果已存在，使用现有的 BizServeInfo
                            savedBizServeInfo = existingBizServeInfo;
                        } else {
                            // 如果不存在，创建新的 BizServeInfo
                            BizServeInfo bizServeInfo = new BizServeInfo();
                            bizServeInfo.setName(techTalentApartExcel.getYymc());
                            bizServeInfo.setType(infoType);
                            savedBizServeInfo = bizServeInfoService.save(bizServeInfo);
                        }

                        if (savedBizServeInfo != null) {
                            // 创建对应渠道的 BizServeMethod 记录
                            String channel = techTalentApartExcel.getQd().trim();
                            if ("PC".equals(channel) || "WX".equals(channel)) {
                                // 删除已有的渠道记录
                                bizServeMethodService.deleteByParams(MapUtil.<String, Object>builder()
                                    .put("=(serve.id)", savedBizServeInfo.getId())
                                    .put("=(type)", channel)
                                    .build()
                                );
                                // 创建新的渠道记录
                                BizServeMethod bizServeMethod = new BizServeMethod();
                                bizServeMethod.setContent(techTalentApartExcel.getYylj());
                                bizServeMethod.setDescription(techTalentApartExcel.getYysm());
                                bizServeMethod.setServe(savedBizServeInfo);
                                bizServeMethod.setType(channel);

                                // 设置认证等级
                                for (DictModel dictModel : dictCacheListRZDJ) {
                                    if (dictModel.getValue().equals(techTalentApartExcel.getRzdj())) {
                                        bizServeMethod.setCertificationLevel(Integer.parseInt(dictModel.getKey()));
                                        break;
                                    }
                                }

                                // 保存 BizServeMethod 记录
                                bizServeMethodService.save(bizServeMethod);
                            }
                        }

                    }
                }

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseData.success("导入成功");
    }

    @Override
    public ResponseData exportData(IdsDto idsDto, HttpServletResponse response) {
        try {
            List<BizServeInfo> bizServeInfos = Lists.newArrayList();
            if (StringUtils.isNotBlank(idsDto.getIds())) {
                String[] ids = idsDto.getIds().split(",");
                bizServeInfos = bizServeInfoService.queryByIds(ids);
            }else {
                bizServeInfos = bizServeInfoService.queryForAll();
            }
            // 手动转换为 BizServeInfoExportDTO
            List<BizServeInfoExportDTO> exportList = bizServeInfos.stream()
                    .map(info -> {
                        BizServeInfoExportDTO dto = new BizServeInfoExportDTO();
                        dto.setName(info.getName());
                        dto.setDescription(info.getDescription());
                        dto.setId(info.getId());
                        return dto;
                    })
                    .toList();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("服务导出数据", StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BizServeInfoExportDTO.class).sheet("服务导出数据").doWrite(exportList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public ResponseData getRZDJ() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(RZDJ);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getFWLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(FWLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getBSZNLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(BSZNLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData getQDLX() {

        List<DictModel> dictCacheListByCode = sysDictionarySubService.getDictCacheListByCode(QDLX);
        JSONArray list = new JSONArray();
        for (DictModel dictModel : dictCacheListByCode) {
            JSONObject rtn = new JSONObject();
            rtn.put("key", dictModel.getKey());
            rtn.put("value", dictModel.getValue());
            list.add(rtn);
        }
        return ResponseData.success(list);
    }

    @Override
    public ResponseData plqy(IdsDto idsDto) {
        String ids = idsDto.getIds();
        String[] split = ids.split(",");
        for (String s : split) {
            BizServeInfo bizServeInfo = bizServeInfoService.queryById(s);
            bizServeInfo.setEnable(true);
            bizServeInfoService.update(bizServeInfo);
        }
        return ResponseData.success("批量发布成功");
    }

    @Override
    public ResponseData plqxqy(IdsDto idsDto) {
        String ids = idsDto.getIds();
        String[] split = ids.split(",");
        for (String s : split) {
            BizServeInfo bizServeInfo = bizServeInfoService.queryById(s);
            bizServeInfo.setEnable(false);
            bizServeInfoService.update(bizServeInfo);
        }
        return ResponseData.success("批量取消发布成功");
    }

    @Override
    public ResponseData<Void> saveMethodDefault(MethodDefaultDTO dto) {
        JSONObject json = new JSONObject();
        json.put("method", dto.getMethod());
        json.put("iconId", dto.getIconId());
        json.put("description", dto.getDescription());
        List<Object> list = redisUtil.lGet(redisKey, 0, -1);
        List<Object> newList = Lists.newArrayList();
        if (list != null && !list.isEmpty()) {
            boolean found = false;
            for (Object o : list) {
                JSONObject jsonObject = JSONObject.parseObject(o.toString());
                if (jsonObject.getString("method").equals(dto.getMethod())) {
                    // 替换旧值
                    newList.add(json);
                    found = true;
                } else {
                    newList.add(o);
                }
            }
            // 如果未找到匹配项，则添加新元素
            if (!found) {
                newList.add(json);
            }
        } else {
            newList.add(json);
        }
        redisUtil.del(redisKey);
        redisUtil.lSetList(redisKey, newList);
        return ResponseData.success().build();
    }

    @Override
    public ResponseData<List<MethodDefaultDTO>> queryMethodDefault(String method) {
        List<Object> list = redisUtil.lGet(redisKey, 0, -1);
        List<MethodDefaultDTO> dtoList = JSONArray.parseArray(list == null ? "[]" : JSONArray.toJSONString(list), MethodDefaultDTO.class);
        if (StringUtils.isNotBlank(method)) {
            List<MethodDefaultDTO> dto = Lists.newArrayList();
            for (MethodDefaultDTO methodDefaultDTO : dtoList) {
                if (methodDefaultDTO.getMethod().equals(method)) {
                    dto.add(methodDefaultDTO);
                    return ResponseData.success(dto);
                }
            }
        }
        return ResponseData.success(dtoList);
    }

    @Override
    public ResponseData<Void> deleteMethodDefault(String method) {
        List<Object> list = redisUtil.lGet(redisKey, 0, -1);
        List<Object> newList = Lists.newArrayList();
        if (list != null && !list.isEmpty()) {
            for (Object o : list) {
                JSONObject jsonObject = JSONObject.parseObject(o.toString());
                if (!jsonObject.getString("method").equals(method)) {
                    newList.add(o);
                }
            }
        }
        redisUtil.del(redisKey);
        if (!newList.isEmpty()) {
            redisUtil.lSetList(redisKey, newList);
        }
        return ResponseData.success().build();
    }

    private void itemConverter(BizServeInfo bizServeInfo, BizServeInfoVo vo) {
        // 如果该服务是事项，则获取事项材料信息
        if ("item".equals(bizServeInfo.getType())) {
            ConfMatterGuide matterGuide = confMatterGuideService.queryForSingle(ImmutableMap.of("=(matterId)", (bizServeInfo.getCode())));
            if (matterGuide != null) {
                JSONArray guideArray = JSON.parseObject(matterGuide.getGuideJson()).getJSONArray("guideInfo");
                guideArray.forEach(guide -> {
                    JSONObject guideObj = (JSONObject) guide;
                    // 简化材料清单返回字段
                    if ("required_materialsList".equals(guideObj.getString("type"))){
                        JSONArray materialsList = new JSONArray();
                        guideObj.getJSONArray("requiredList").forEach(materials -> {
                            JSONObject materialsObj = (JSONObject) materials;
                            JSONObject materialsObjNew = new JSONObject();
                            materialsObjNew.put("materialName", materialsObj.getString("materialName"));
                            materialsObjNew.put("require", materialsObj.get("require"));
                            materialsObjNew.put("blankTable", materialsObj.getString("blankTable"));
                            materialsObjNew.put("exampleTable", materialsObj.getString("exampleTable"));
                            materialsList.add(materialsObjNew);
                        });
                        guideObj.put("requiredList", materialsList);
                    }
                    // 简化结果物清单返回字段
                    if ("result_materialsList".equals(guideObj.getString("type"))){
                        JSONArray resultList = new JSONArray();
                        guideObj.getJSONArray("resultList").forEach(result -> {
                            JSONObject resultObj = (JSONObject) result;
                            JSONObject resultObjNew = new JSONObject();
                            resultObjNew.put("materialName", resultObj.getString("materialName"));
                            resultObjNew.put("exampleTable", resultObj.getString("exampleTable"));
                            resultList.add(resultObjNew);
                        });
                        guideObj.put("resultList", resultList);
                    }
                });
                Objects.requireNonNull(vo).setGuideJson(String.valueOf(guideArray));
            }
        }
    }
}
