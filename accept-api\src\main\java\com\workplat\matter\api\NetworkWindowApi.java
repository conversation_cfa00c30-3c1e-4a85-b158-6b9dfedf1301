package com.workplat.matter.api;

import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.matter.dto.NetworkWindowDTO;
import com.workplat.matter.dto.NetworkWindowRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description 网点窗口信息模块
 * @date 2025/6/10 13:48
 */
@Validated
@RestController
@RequestMapping("/api/matter/window")
@Tag(name = "网点窗口信息模块")
public interface NetworkWindowApi {

    @PostMapping("/save")
    @Operation(summary = "保存网点窗口")
    ResponseData<Void> save(@Valid @RequestBody NetworkWindowDTO dto);

    @GetMapping("/deleteById")
    @Operation(summary = "删除网点窗口")
    ResponseData<Void> deleteById(String id);

    @GetMapping("/getById")
    @Operation(summary = "通过id查询")
    ResponseData<NetworkWindowDTO> getById(String id);

    @PostMapping("/page")
    @Operation(summary = "分页查询")
    ResponseData<Page<NetworkWindowDTO>> page(@RequestBody NetworkWindowRequest request, PageableDTO pageDTO);

    @GetMapping("/queryByIds")
    @Operation(summary = "通过ids查询")
    ResponseData<List<NetworkWindowDTO>> queryByIds(String ids);

    @GetMapping("/queryByAcceptId")
    @Operation(summary = "通过acceptId获取事项绑定网点窗口")
    ResponseData<List<NetworkWindowDTO>> queryByAcceptId(String acceptId);
}
