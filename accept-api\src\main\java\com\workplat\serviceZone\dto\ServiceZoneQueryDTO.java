package com.workplat.serviceZone.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区查询DTO
 */
@Data
@Schema(description = "服务专区查询DTO")
public class ServiceZoneQueryDTO {

    /**
     * 专区名称（模糊查询）
     */
    @Schema(description = "专区名称（模糊查询）")
    private String name;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enabled;
}
