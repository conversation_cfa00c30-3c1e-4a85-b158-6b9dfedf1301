package com.workplat.accept.business.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class BizChatMessageVO {


    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "发送者")
    private String sender;

    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "客服key")
    private String agentKey;

    @Schema(description = "智能会话ID")
    private String agentChatId;

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "创建时间")
    private Date createTime;
}
