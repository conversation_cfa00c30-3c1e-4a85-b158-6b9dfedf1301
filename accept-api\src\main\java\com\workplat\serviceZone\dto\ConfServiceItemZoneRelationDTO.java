package com.workplat.serviceZone.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @Author: yang<PERSON>
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联关系DTO
 */
@Data
public class ConfServiceItemZoneRelationDTO {

    /**
     * 主键ID（更新时必填）
     */
    private String id;

    /**
     * 服务清单ID
     */
    private String serviceItemId;

    /**
     * 专区ID
     */
    private String serviceZoneId;

    /**
     * 排序
     */
    private Integer sort;
}