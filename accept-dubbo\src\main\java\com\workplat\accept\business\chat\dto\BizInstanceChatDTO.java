package com.workplat.accept.business.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "办件记录DTO")
public class BizInstanceChatDTO {

    @Schema(description = "用户token")
    private String token;

    @Schema(description = "办件id")
    private String id;

    @Schema(description = "办件状态")
    private String status;

    @Schema(description = "渠道 pc/mobile")
    private String channel;
}
