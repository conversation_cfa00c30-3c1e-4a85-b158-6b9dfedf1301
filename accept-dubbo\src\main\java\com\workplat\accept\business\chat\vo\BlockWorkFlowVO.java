package com.workplat.accept.business.chat.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * Dify Workflow 阻塞式调用响应.
 */
@Data
public class BlockWorkFlowVO implements Serializable {

    /**
     * 不同模式下的事件类型.
     */
    @JsonProperty(value = "workflow_run_id")
    private String workflowRunId;

    /**
     * 任务ID.
     */
    @JsonProperty(value = "task_id")
    private String taskId;


    /**
     * 详细内容.
     */
    private Map<String, Object> data;

}
