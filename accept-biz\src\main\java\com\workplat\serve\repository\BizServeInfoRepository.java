package com.workplat.serve.repository;


import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.gss.common.core.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * @author: qian cheng
 * @package: com.workplat.serve.repository
 * @description: 服务信息Repository
 * @date: 2025/5/14 15:38
 */
@Repository
public interface BizServeInfoRepository extends BaseRepository<BizServeInfo> {






}
