package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoVO;
import com.workplat.utils.ChatCacheUtil;
import com.workplat.componentEngine.constant.ComponentEngineCode;
import org.springframework.stereotype.Service;

/**
 * 推荐组件引擎
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Service
public class RecommendedComponentEngine extends AbstractComponentEngine {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final ChatCacheUtil chatCacheUtil;


    public RecommendedComponentEngine(BizInstanceInfoService bizInstanceInfoService, ChatCacheUtil chatCacheUtil) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.chatCacheUtil = chatCacheUtil;
    }

    @Override
    protected ComponentRunVO doExecute(ComponentDataContext componentDataContext) {
        // 暂未使用，直接通过智能体处理的
        return new ComponentRunVO();
    }
    
    @Override
    public boolean canHandle(ComponentDataContext context) {
        return ComponentEngineCode.RECOMMENDED.equals(context.getConfComponent().getEngineCode());
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        Object data = componentDataContext.getSubmitData();
        // 假设给了我们一个数据 {"matterId":"190803ae4c734010af8b757a72ec3e62"}
        BizInstanceInfoVO bizInstanceInfoVO = bizInstanceInfoService.initialize("190803ae4c734010af8b757a72ec3e62");
        // 更新进入缓存
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentDataContext.getRecordId());
        chatProcessDTO.setInstanceId(bizInstanceInfoVO.getId());
        chatCacheUtil.set(componentDataContext.getRecordId(), chatProcessDTO);
    }

}