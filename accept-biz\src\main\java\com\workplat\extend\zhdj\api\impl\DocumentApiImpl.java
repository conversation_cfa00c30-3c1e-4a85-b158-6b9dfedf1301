package com.workplat.extend.zhdj.api.impl;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ResultFileListVO;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.file.constant.SysFileSource;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import com.workplat.zhdj.DocumentApi;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Scanner;

@Slf4j
@Controller
@Transactional(rollbackFor = Exception.class)
public class DocumentApiImpl implements DocumentApi {
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @Autowired
    private SysFileEntityService sysFileEntityService;

    @SneakyThrows
    @Override
    public String documentUpdate(String bizInstanceId, String fileId, HttpServletRequest request, HttpServletResponse response) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(bizInstanceId);
        log.info("智慧登记-保存onlyOffice编辑后的文件，bizInstanceId:{},fileId:{}", bizInstanceId, fileId);
        Scanner scanner = new Scanner(request.getInputStream()).useDelimiter("\\A");
        String body = scanner.hasNext() ? scanner.next() : "";
        JSONObject jsonObj = JSONObject.parseObject(body);
        if (Objects.nonNull(jsonObj) && (Integer) jsonObj.get("status") == 2) {
            String downloadUri = (String) jsonObj.get("url");
            // 获取需要保存的文件
            URL url = new URL(downloadUri);
            log.info("智慧登记-保存onlyOffice编辑后的文件，文件地址", downloadUri);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            InputStream inputStream = connection.getInputStream();
            byte[] data = readInputStream(inputStream);
            // 查询原始文件
            SysFileEntity file = sysFileEntityService.queryById(fileId);
            // 创建新的需要保存的文件
            ByteArrayInputStream dataInputStream = new ByteArrayInputStream(data);
            SysFileUploadModel mongoFileModel = new SysFileUploadModel();
            mongoFileModel.setContent(dataInputStream);
            mongoFileModel.setFileName(file.getFileName());
            mongoFileModel.setContentType(file.getFileType());
            mongoFileModel.setLength((long) data.length);
            mongoFileModel.setFileSource(SysFileSource.SYSTEM_USER_PRIVATE_FILE.name());
            mongoFileModel.setMd5(DigestUtils.md5Hex(dataInputStream.readAllBytes()));
            SysFileEntity upload = sysFileEntityService.upload(mongoFileModel);

            List<ResultFileListVO> fileListVOS = new ArrayList<>();
            ResultFileListVO resultFileListVO = new ResultFileListVO();
            resultFileListVO.setName(upload.getFileName());
            resultFileListVO.setPreviewUrl("https://zwfw.tcsjj.cn/applications/dowhiletalk-pc/#/doc/preview?filedId="+upload.getId()+"&fileName="+upload.getFileName()+"&bizInstanceId="+bizInstanceId);
            resultFileListVO.setDownloadUrl("https://zwfw.tcsjj.cn/gateway-api/zwfw-ai-central/api/configuration/file/download?id=" + upload.getId());
            fileListVOS.add(resultFileListVO);

            bizInstanceInfo.extendJsonAdd(JSONObject.of(BizInstanceExtendJsonEnum.RESULT_FILE_LIST.getCode(), fileListVOS));
            bizInstanceInfoService.update(bizInstanceInfo);
            log.info("智慧登记-保存onlyOffice编辑后的文件，文件地址", downloadUri);
        }
        return "{\"error\":0}";
    }

    private static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        // 创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        // 每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        // 使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        // 关闭输入流
        inStream.close();
        // 把outStream里的数据写入内存
        byte[] bytes = outStream.toByteArray();
        outStream.flush();
        outStream.close();
        return bytes;
    }
}
