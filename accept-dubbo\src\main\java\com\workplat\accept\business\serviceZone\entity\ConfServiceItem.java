package com.workplat.accept.business.serviceZone.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 推荐服务清单配置实体类
 */
@Getter
@Setter
@Entity
@Table(name = "conf_service_item", indexes = {
    @Index(name = "idx_channel", columnList = "channel"),
    @Index(name = "idx_category", columnList = "category"),
    @Index(name = "idx_deleted", columnList = "deleted")
})
public class ConfServiceItem extends BaseEntity {

    @Comment("服务名称")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Comment("发送内容")
    @Column(name = "send_content", length = 1000)
    private String sendContent;

    @Comment("渠道 pc:PC端 wx:微信端")
    @Column(name = "channel", length = 10)
    private String channel;

    @Comment("分类标签")
    @Column(name = "category", length = 50)
    private String category;

    @Comment("是否启用 0:禁用 1:启用")
    @Column(name = "enabled")
    private Boolean enabled;

    @Comment("关联的专区")
    @OneToMany(mappedBy = "serviceItem", cascade = CascadeType.ALL)
    @Where(clause = "deleted = 0")
    private List<ConfServiceItemZoneRelation> serviceZoneRelations;
}
