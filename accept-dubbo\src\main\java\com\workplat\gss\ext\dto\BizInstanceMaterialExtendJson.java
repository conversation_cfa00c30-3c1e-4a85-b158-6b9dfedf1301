package com.workplat.gss.ext.dto;

import com.alibaba.fastjson2.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * BizInstanceMaterial对象扩展字段ExtendJson
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:30
 */
@Data
public class BizInstanceMaterialExtendJson {

    @Schema(description = "材料绑定字段codes")
    private String materialBindFields;

    @Schema(description = "是否最终提交展示")
    private Boolean isFinalSubmitShow;

    @Schema(description = "提示词")
    private String prompt;


    public static BizInstanceMaterialExtendJson convert(String extendJson) {
        return JSON.parseObject(extendJson, BizInstanceMaterialExtendJson.class);
    }
}
