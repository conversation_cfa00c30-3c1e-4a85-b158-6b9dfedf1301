//package com.workplat.electronic.certificate;
package com.workplat.gss.script.biz.loader;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.*;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.doc.pdf.util.PdfConvertUtils;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;
import com.workplat.gss.file.service.SysFileEntityService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 大型活动办件推送服务
 */
public class GaDistributionDriverLoaderImpl implements DistributionDriverLoader {



    // Constants for SQL queries
    private static final String INSERT_INSTANCE_SQL =
            "INSERT INTO biz_ga_instance_info (id, create_time, accepted_num, application_address, " +
            "application_certificate_code, application_name, application_mobile, matter_code, " +
            "matter_name, form_obj, fields_map, is_first_submit) " +
            "VALUES (:uuid, now(), :acceptedNum, :applicationAddress, :applicationCertificateCode, " +
            ":applicationName, :applicationMobile, :matterCode, :matterName, :formObj, :fieldsMap, :isFirstSubmit)";

    private static final String INSERT_MATERIAL_SQL =
            "INSERT INTO biz_ga_instance_material (id, create_time, material_id, material_code, " +
            "material_description, material_name, instance_id) " +
            "VALUES (:uuid, now(), :materialId, :materialCode, :materialDescription, :materialName, :instanceId)";

    private static final String INSERT_FILE_SQL =
            "INSERT INTO biz_ga_instance_material_file (id, create_time, file_name, file_data, material_id) " +
            "VALUES (:uuid, now(), :fileName, :file_data, :materialId)";

    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {

        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);

        DistributionDriverOutput output = new DistributionDriverOutput();
        try {
            String instanceId = input.getInstanceId();
            BizInstanceChat chat = bizInstanceChatService.queryForSingle(ImmutableMap.of("=(instance.id)", instanceId));

            if (chat != null) {
                handleExistingCase(instanceId, chat);
            } else {
                handleNewCase(instanceId);
            }

            output.setSuccess(true);
        } catch (Exception e) {
            output.setSuccess(false);
            output.setMsg(StringUtils.substring(ExceptionUtils.getMessage(e), 0, 2000));
        }
        return output;
    }

    /**
     * 处理已存在的办件案例
     */
    private void handleExistingCase(String instanceId, BizInstanceChat chat) {
        // 1. 解析历史数据
        PushCompareData oldData = parseOldPushData(chat);
        if (oldData == null) return;

        // 2. 获取当前办件详情
        BizInstanceInfoDetailVO currentDetail = getBizInstanceDetail(instanceId);
        String currentFormData = (String) coverFormObj(currentDetail.getBizInstanceFields().getFormObj());

        // 3. 比较表单数据变化
        Map<String, Object> formChanges = compareFormDataChanges(
                currentFormData,
                oldData.getFormData()
        );

        // 4. 比较文件变化
        Map<String, List<String>> currentFileGroups = getCurrentFileGroups(instanceId);
        markChangedFileGroups(formChanges, currentFileGroups, oldData.getGroupFileIds());

        // 5. 更新会话记录
        updateChatRecord(chat, formChanges, currentFileGroups);

        // 6. 推送数据
        pushV2(instanceId, false, JSONObject.toJSONString(formChanges));
    }

    /**
     * 解析历史推送数据
     */
    private PushCompareData parseOldPushData(BizInstanceChat chat) {
        try {
            return JSONObject.parseObject(chat.getExtendJson(), PushCompareData.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取办件详情
     */
    private BizInstanceInfoDetailVO getBizInstanceDetail(String instanceId) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        BizInstanceInfoDetailVO detail = bizInstanceInfoService.getDetailVOById(instanceId);
        if (detail == null) {
            throw new BusinessException("办件详情不存在，instanceId: " + instanceId);
        }
        return detail;
    }

    /**
     * 比较表单数据变化
     */
    private Map<String, Object> compareFormDataChanges(String currentFormData, String oldFormData) {
        Map<String, Object> currentFormMap = JSON.parseObject(currentFormData, Map.class);
        Map<String, Object> oldFormMap = JSON.parseObject(oldFormData, Map.class);
        Map<String, Object> result = new LinkedHashMap<>();

        currentFormMap.forEach((category, value) -> {
            JSONObject currentCategory = (JSONObject) value;
            JSONObject oldCategory = (JSONObject) oldFormMap.get(category);

            // 新增分类
            boolean isChanged = oldCategory == null || !equalsIgnoreUpdateFlag(currentCategory, oldCategory);  // 比较内容变化

            currentCategory.put("isUpdate", isChanged);
            result.put(category, currentCategory);
        });

        return result;
    }

    /**
     * 忽略isUpdate标志比较两个JSON对象
     */
    private boolean equalsIgnoreUpdateFlag(JSONObject obj1, JSONObject obj2) {
        if (obj1 == obj2) return true;
        if (obj1 == null || obj2 == null) return false;

        JSONObject copy1 = new JSONObject(obj1);
        JSONObject copy2 = new JSONObject(obj2);

        copy1.remove("isUpdate");
        copy2.remove("isUpdate");

        return copy1.equals(copy2);
    }

    /**
     * 标记变化的文件分组
     */
    private void markChangedFileGroups(Map<String, Object> formChanges,
                                       Map<String, List<String>> currentFiles,
                                       Map<String, List<String>> oldFiles) {
        currentFiles.forEach((groupName, fileIds) -> {
            // 如果当前文件列表为空，则跳过处理
            if (fileIds == null || fileIds.isEmpty()) {
                return;
            }
            boolean isFileChanged = !fileIds.equals(oldFiles.get(groupName));
            if (isFileChanged && formChanges.containsKey(groupName)) {
                ((JSONObject) formChanges.get(groupName)).put("isUpdate", true);
            }
        });
    }

    /**
     * 更新会话记录
     */
    private void updateChatRecord(BizInstanceChat chat,
                                  Map<String, Object> formChanges,
                                  Map<String, List<String>> fileGroups) {
        PushCompareData newData = new PushCompareData();
        newData.setFormData(JSONObject.toJSONString(formChanges));
        newData.setGroupFileIds(fileGroups);

        chat.setExtendJson(JSONObject.toJSONString(newData));
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        bizInstanceChatService.update(chat);
    }

    private void handleNewCase(String instanceId) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        PushCompareData pushCompareData = pushV2(instanceId, true, null);

        BizInstanceChat bizInstanceChat = new BizInstanceChat();
        bizInstanceChat.setInstance(bizInstanceInfoService.queryById(instanceId));
        bizInstanceChat.setCurrentNode(NodeStatusEnum.FINISH_ACCEPT.name());
        bizInstanceChat.setCurrentNodeName(NodeStatusEnum.FINISH_ACCEPT.getValue());
        bizInstanceChat.setExtendJson(JSONObject.toJSONString(pushCompareData));

        bizInstanceChatService.create(bizInstanceChat);
    }

    private Map<String, List<String>> getCurrentFileGroups(String instanceId) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        List<BizInstanceMaterialGroupVO> materialGroups = bizInstanceMaterialService.getMaterialGroup(instanceId);

        return materialGroups.stream()
                .collect(Collectors.toMap(
                        BizInstanceMaterialGroupVO::getGroupName,
                        group -> group.getInstanceMaterialVOList().stream()
                                .flatMap(material -> material.getMaterialFileVOList().stream()
                                        .map(BizInstanceMaterialFileVO::getFileId))
                                .collect(Collectors.toList())
                ));
    }

    public PushCompareData pushV2(String instanceId, Boolean isFirstSubmit, String formData) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        PushCompareData compareData = new PushCompareData();
        try {
            gaTransactionTemplate.execute(status -> {
                BizInstanceInfoDetailVO detailVOById = bizInstanceInfoService.getDetailVOById(instanceId);
                if (detailVOById == null) {
                    throw new BusinessException("未找到办件信息，instanceId: " + instanceId);
                }

                BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
                List<BizInstanceMaterialGroupVO> materialGroups = bizInstanceMaterialService.getMaterialGroup(instanceId);

                // Process and save instance info
                String processedFormData = processFormData(detailVOById, formData);
                compareData.setFormData(processedFormData);

                String instanceUuid = saveInstanceInfo(detailVOById, bizInstanceInfo, processedFormData, isFirstSubmit);

                // Process and save materials and files
                Map<String, List<String>> groupFileIds = processMaterials(materialGroups, instanceUuid);
                compareData.setGroupFileIds(groupFileIds);

                return null;
            });
        } catch (Exception e) {
            throw new RuntimeException("Error push: " + e.getMessage(), e);
        }


        return compareData;
    }

    private String processFormData(BizInstanceInfoDetailVO detailVO, String formData) {
        return formData == null ?
                (String) coverFormObj(detailVO.getBizInstanceFields().getFormObj()) :
                formData;
    }

    private String saveInstanceInfo(BizInstanceInfoDetailVO detailVO, BizInstanceInfo bizInstanceInfo,
                                    String formData, Boolean isFirstSubmit) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        MapSqlParameterSource params = new MapSqlParameterSource();
        String uuid = UUID.randomUUID().toString().replace("-", "");

        params.addValue("uuid", uuid);
        params.addValue("acceptedNum", detailVO.getAcceptedNum());
        params.addValue("applicationAddress", detailVO.getApplicationAddress());
        params.addValue("applicationCertificateCode", detailVO.getApplicationCertificateCode());
        params.addValue("applicationName", detailVO.getApplicationName());
        params.addValue("applicationMobile", detailVO.getApplicationPhone());
        params.addValue("matterCode", bizInstanceInfo.getMatterCode());
        params.addValue("matterName", bizInstanceInfo.getMatterName());
        params.addValue("formObj", formData);
        params.addValue("fieldsMap", detailVO.getBizInstanceFields().getFieldsMap());
        params.addValue("isFirstSubmit", isFirstSubmit ? 1 : 0);

        int updateCount = gaJdbcTemplate.update(INSERT_INSTANCE_SQL, params);
        if (updateCount <= 0) {
            throw new BusinessException("办件信息插入失败，instanceId: " + detailVO.getId());
        }

        return uuid;
    }

    private Map<String, List<String>> processMaterials(List<BizInstanceMaterialGroupVO> materialGroups, String instanceUuid) {
        Map<String, List<String>> groupFileIds = new HashMap<>();

        for (BizInstanceMaterialGroupVO group : materialGroups) {
            List<String> fileIds = new ArrayList<>();

            for (BizInstanceMaterialVO material : group.getInstanceMaterialVOList()) {
                String materialUuid = saveMaterial(material, group.getGroupName(), instanceUuid);
                saveMaterialFiles(material, materialUuid, fileIds);
            }

            if (!fileIds.isEmpty()) {
                groupFileIds.put(group.getGroupName(), fileIds);
            }
        }

        return groupFileIds;
    }

    private String saveMaterial(BizInstanceMaterialVO material, String groupName, String instanceId) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        MapSqlParameterSource params = new MapSqlParameterSource();
        String uuid = UUID.randomUUID().toString().replace("-", "");

        params.addValue("uuid", uuid);
        params.addValue("materialId", material.getId());
        params.addValue("materialCode", material.getMaterialCode());
        params.addValue("materialDescription", groupName);
        params.addValue("materialName", material.getMaterialName());
        params.addValue("instanceId", instanceId);

        int updateCount = gaJdbcTemplate.update(INSERT_MATERIAL_SQL, params);
        if (updateCount <= 0) {
            throw new BusinessException("材料信息插入失败，materialId: " + material.getId());
        }

        return uuid;
    }

    private void saveMaterialFiles(BizInstanceMaterialVO material, String materialId, List<String> fileIds) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        Map<String, byte[]> fileMap = pdfConvertPicture(material);

        for (BizInstanceMaterialFileVO file : material.getMaterialFileVOList()) {
            MapSqlParameterSource params = new MapSqlParameterSource();
            String uuid = UUID.randomUUID().toString().replace("-", "");

            params.addValue("uuid", uuid);
            params.addValue("fileName", file.getFileName());
            try {
                byte[] fileData = fileMap.containsKey(file.getFileId()) ?
                        fileMap.get(file.getFileId()) :
                        sysFileEntityService.binary(file.getFileId());
                params.addValue("file_data", fileData);
            } catch (Exception e) {
                throw new BusinessException("获取文件数据失败，fileId: " + file.getFileId(), e);
            }
            params.addValue("materialId", materialId);

            int updateCount = gaJdbcTemplate.update(INSERT_FILE_SQL, params);
            if (updateCount <= 0) {
                throw new BusinessException("材料文件信息插入失败，fileId: " + file.getFileId());
            }

            fileIds.add(file.getFileId());
        }
    }

    private Object coverFormObj(String formObj) {
        if (StringUtils.isBlank(formObj)) {
            return null;
        }

        Map<String, Object> formObjMap = JSON.parseObject(formObj, Map.class);
        Map<String, Object> newFormObjMap = new HashMap<>();

//        JSONObject config = JSON.parseObject("""
//                {
//                  "经营相关材料": {
//                    "dwmc": "单位名称",
//                    "zczb": "81X借***********",
//                    "gszcd": "塘栖镇龙船坞村马家坞路30号"
//                  },
//                  "财务相关材料": {
//                    "bgyfxz": "自有房产",
//                    "region": "江苏省苏州市太世市太艺中T八人才落户甲报类型学万证书验AJ1JRXLAW133RSBM",
//                    "fddbrxm": "周亦欣"
//                  },
//                  "申请相关材料": {}
//                }
//                """);

        JSONObject config = JSON.parseObject("""
                {
                  "活动详情": {
                    "isUpdate": true,
                    "sxlb": "事项类别",
                    "yjgmrs": "预计规模",
                    "hdmc": "活动名称",
                    "qsrq": "举办日期",
                    "jzsj": "结束日期",
                    "hdddlx": "举办地点",
                    "hdlx": "活动路线",
                    "hdmjlc": "场地面积（）",
                    "cdlc": "场地里程",
                    "edrl": "场地安全容量",
                    "nfsps": "拟发售票数",
                    "hdnr": "活动概况",
                    "sdzffzrxm": "属地政府负责人姓名",
                    "sdzffzrlxdh": "属地政府负责人联系电话",
                    "zgbmzrr": "主管部门责任人姓名",
                    "zgbmzrrlxdh": "主管部门责任人联系电话",
                    "hdszqy": "活动所在区域"
                  },
                  "活动方案": {
                    "isUpdate": true
                  },
                  "主、承办方情况": {
                    "isUpdate": true,
                    "zbfmc": "主办方名称",
                    "zbffzrxm": "主办方负责人姓名",
                    "zbffzrlxdh": "主办方负责人联系电话",
                    "cbzmc": "承办方名称",
                    "cbzaqzrr": "承办方安全责任人姓名",
                    "cbzzw": "承办方安全责任人职务",
                    "cbzsfz": "承办方安全责任人身份证件类型",
                    "sfzjhm": "承办方安全责任人身份证件号码",
                    "aqzrrlxdh": "承办方安全责任人联系电话",
                    "lxrxm": "承办方联系人姓名",
                    "lxrzw": "承办方联系人职务",
                    "lxrdh": "承办方联系人联系电话"
                  },
                  "场地提供方情况": {
                    "isUpdate": true,
                    "glzmc": "场所管理者名称",
                    "glzyfzr": "主要负责人姓名",
                    "glfzrzw": "主要负责人职务",
                    "glzyfzrdh": "主要负责人联系电话",
                    "gllxr": "联系人姓名",
                    "glzw": "联系人职务",
                    "gldh": "联系人联系电话"
                  },
                  "临时搭建情况": {
                    "isUpdate": true,
                    "dwmc": "单位名称",
                    "qylx": "企业类型",
                    "qyzch": "企业注册号",
                    "qyfzr": "主要负责人姓名",
                    "qyfzrzw": "主要负责人职务",
                    "qyfzrlxdh": "主要负责人联系电话"
                  },
                  "保安提供方情况": {
                    "isUpdate": true,
                    "bagsmc": "保安公司名称",
                    "bagsfzr": "主要负责人姓名",
                    "bagsfzrzw": "主要负责人职务",
                    "bagsfzrdh": "主要负责人联系电话",
                    "bagssgsl": "受雇保安员数量",
                    "bagsbz": "备注"
                  },
                  "活动参与人员情况": {
                    "isUpdate": true
                  },
                  "安保及应急预案": {
                    "isUpdate": true
                  },
                  "证件管理方案": {
                    "isUpdate": true,
                    "gzzjlb": "工作证件类别",
                    "gzzjsl": "工作证件数量"
                  },
                  "票务管理方案": {
                    "isUpdate": true,
                    "pwsl": "票务数量",
                    "smpt": "售卖平台",
                    "smjh": "售卖计划",
                    "pwgsmc": "票务公司名称",
                    "pwgsfzrlxdh": "票务公司负责人联系电话"
                  },
                  "现场检测报告": {
                    "isUpdate": true
                  },
                  "稳评及其他部门许可情况": {
                    "isUpdate": true
                  },
                  "其他材料": {
                    "isUpdate": true
                  }
                }
              """);

        config.forEach((categoryName, value) -> {
            JSONObject categoryFields = (JSONObject) value;
            Map<String, Object> categoryData = new HashMap<>();

            categoryFields.forEach((fieldKey, fieldValue) -> {
                if (formObjMap.containsKey(fieldKey)) {
                    categoryData.put(fieldKey, formObjMap.get(fieldKey));
                }
            });

            // 创建默认isUpdate字段
            categoryData.putIfAbsent("isUpdate", true);

            if (!categoryData.isEmpty()) {
                newFormObjMap.put(categoryName, categoryData);
            }
        });

        return JSONObject.toJSONString(newFormObjMap);
    }

    /**
     * 推送数据比对模型
     */
    public static class PushCompareData {
        private String formData; // 处理后的表单数据
        private Map<String, List<String>> groupFileIds; // 分组文件ID映射（key:分组名称, value:文件ID列表）

        public String toString() {
            return "PushCompareData{" +
                   "formData='" + StringUtils.abbreviate(formData, 50) + '\'' +
                   ", groupFileIds=" + groupFileIds +
                   '}';
        }

        public Map<String, List<String>> getGroupFileIds() {
            return groupFileIds;
        }

        public void setGroupFileIds(Map<String, List<String>> groupFileIds) {
            this.groupFileIds = groupFileIds;
        }

        public String getFormData() {
            return formData;
        }

        public void setFormData(String formData) {
            this.formData = formData;
        }
    }

    /**
     * pdf转换为图片
     *
     * @param materialVO
     */
    private Map<String, byte[]> pdfConvertPicture(BizInstanceMaterialVO materialVO) {
        TransactionTemplate gaTransactionTemplate = (TransactionTemplate) ApplicationContextUtil.getBean("gaTransactionTemplate");
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceMaterialService bizInstanceMaterialService = ApplicationContextUtil.getBean(BizInstanceMaterialService.class);
        SysFileEntityService sysFileEntityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        NamedParameterJdbcTemplate gaJdbcTemplate = (NamedParameterJdbcTemplate) ApplicationContextUtil.getBean("gaJdbcTemplate");
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);
        // 材料配置为纸质的时候才转换成图片
        if (!"PAPER".equals(materialVO.getSubmitFormat())) {
            return MapUtil.newHashMap();
        }
        Map<String, byte[]> fileMap = new HashMap<>();
        List<BizInstanceMaterialFileVO> materialFileVOList = materialVO.getMaterialFileVOList();
        int index = 0;
        for (BizInstanceMaterialFileVO fileVO : materialFileVOList) {
            byte[] binary = null;
            try {
                binary = sysFileEntityService.binary(fileVO.getFileId());
            } catch (Exception e) {
                throw new RuntimeException("获取文件数据失败", e);
            }
            // 转换为图片,目前业务只会有一张图片
            List<byte[]> image = null;
            try {
                image = PdfConvertUtils.pdf2Image(binary, 50);
            } catch (Exception e) {
                continue;
            }
            if (image.isEmpty()) {
                continue;
            }
            fileMap.put(fileVO.getFileId(), image.get(0));

            // 文件后缀变成.png
            fileVO.setFileName(fileVO.getFileName().replace(".pdf", ".jpg"));
        }
        return fileMap;
    }

}