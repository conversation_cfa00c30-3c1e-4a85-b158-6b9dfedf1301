package com.workplat.componentEngine.constant;

/**
 * 组件引擎代码常量类
 * 统一管理所有组件引擎的 engineCode，避免硬编码字符串
 *
 * <AUTHOR>
 * @date 2025/07/13
 */
public final class ComponentEngineCode {

    private ComponentEngineCode() {
        // 私有构造函数，防止实例化
    }

    // ==================== 材料相关组件 ====================

    /**
     * 材料上传/分类组件
     */
    public static final String MATERIAL_UPLOAD = "MaterialUpload";

    /**
     * 材料上传/分类 分组组件
     */
    public static final String MATERIAL_UPLOAD_GROUP = "MaterialUploadGroup";

    /**
     * 材料分类分组组件
     */
    public static final String MATERIAL_GROUP = "MaterialGroup";

    /**
     * 材料清单列表组件
     */
    public static final String MATERIALS_LIST = "MaterialList";

    // ==================== 表单相关组件 ====================

    /**
     * 字段提取展示组件
     */
    public static final String FIELD_SHOW = "FieldExtraction";

    /**
     * 表单分组填写组件
     */
    public static final String FORM_GROUP = "FormFill";

    // ==================== 流程相关组件 ====================

    /**
     * 情境选择组件
     */
    public static final String SITUATION = "ScenarioSelection";

    /**
     * 推荐组件
     */
    public static final String RECOMMENDED = "recommended";

    /**
     * 结果数据组件
     */
    public static final String RESULT = "ResultData";

    /**
     * 验证组件
     */
    public static final String VERIFICATION = "InfoConfirm";

    /**
     * 知情同意表单组件
     */
    public static final String INFORMED_FORM = "InformedForm";

    // ==================== 指令相关组件 ====================

    /**
     * 申报须知（第三方接口获取）组件
     */
    public static final String INSTRUCTION_REMOTE = "InstructionRemote";

    /**
     * 申报须知（配置）组件
     */
    public static final String INSTRUCTION_FIXED = "ProcessConditions";

    // ==================== 其他组件 ====================

    /**
     * 户籍登记组件
     */
    public static final String HOUSEHOLD_REG = "PreConditions";

    /**
     * 社会救助情形组件
     */
    public static final String SOCIAL_SITUATION = "SocialScenarioSelection";
    /**
     * 社会救助结果数据组件
     */
    public static final String SOCIAL_RESULT = "SocialResultData";

    /**
     * 档案组件
     */
    public static final String ARCHIVE = "QueryArchive";

    // ==================== 表单过滤模式相关 ====================

    /**
     * 需要使用保留模式的组件集合
     * 这些组件主要用于展示AI已提取的字段
     */
    public static final String[] KEEP_MODE_COMPONENTS = {
            FIELD_SHOW
    };

    /**
     * 需要使用移除模式的组件集合
     * 这些组件主要用于显示需要用户填写的字段
     */
    public static final String[] REMOVE_MODE_COMPONENTS = {
            FORM_GROUP,
            MATERIAL_UPLOAD  // MaterialsClassifyComponentEngine 根据下一个组件动态决定
    };

    /**
     * 判断组件是否应该使用保留模式
     *
     * @param engineCode 组件引擎代码
     * @return true为保留模式，false为移除模式
     */
    public static boolean shouldUseKeepMode(String engineCode) {
        if (engineCode == null) {
            return false;
        }

        for (String code : KEEP_MODE_COMPONENTS) {
            if (code.equals(engineCode)) {
                return true;
            }
        }

        return false;
    }
}
