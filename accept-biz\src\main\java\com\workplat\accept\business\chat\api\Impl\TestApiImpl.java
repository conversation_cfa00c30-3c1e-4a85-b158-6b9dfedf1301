package com.workplat.accept.business.chat.api.Impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.api.TestApi;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceGuideJsonVO;
import com.workplat.gss.service.item.api.lowCodeForm.LowCodeFormJsonApi;
import com.workplat.gss.service.item.api.matter.api.ConfMatterGuideApi;
import com.workplat.gss.service.item.api.matter.vo.ConfMatterGuideVO;
import com.workplat.gss.service.item.dubbo.lowCodeForm.constant.FormComponentTypeEnum;
import com.workplat.gss.service.item.dubbo.lowCodeForm.dto.LowCodeFormJsonDTO;
import com.workplat.gss.service.item.dubbo.matter.entity.accept.ConfMatterAccept;
import com.workplat.gss.service.item.dubbo.matter.service.accept.ConfMatterAcceptMaterialService;
import com.workplat.gss.service.item.dubbo.matter.service.accept.ConfMatterAcceptService;
import com.workplat.gss.service.item.dubbo.matter.vo.accept.ConfMatterAcceptMaterialVO;
import com.workplat.push.ga.service.GaDeclarePushService;
import com.workplat.push.power.service.MattersDeclareService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;


@RestController
@RequestMapping(value = "/api/test")
public class TestApiImpl  implements TestApi {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final MattersDeclareService mattersDeclareService;
    private final JdbcTemplate jdbcTemplate;
    private final GaDeclarePushService gaDeclarePushService;

    @Autowired
    ConfMatterAcceptService confMatterAcceptService;
    @Autowired
    private ConfMatterAcceptMaterialService matterAcceptMaterialService;
    @Autowired
    LowCodeFormJsonApi lowCodeFormJsonApi;
    @Autowired
    private ConfMatterGuideApi confMatterGuideApi;

    public TestApiImpl(BizInstanceInfoService bizInstanceInfoService,
                       MattersDeclareService mattersDeclareService,
                       JdbcTemplate jdbcTemplate,
                       GaDeclarePushService gaDeclarePushService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.mattersDeclareService = mattersDeclareService;
        this.jdbcTemplate = jdbcTemplate;
        this.gaDeclarePushService = gaDeclarePushService;
    }

    public String test(String matterCode) {
        return "";
    }



    @Override
    public JSONObject test2(String instanceId, Boolean isFirstSubmit) {
        gaDeclarePushService.push(instanceId, true);
        return null;
    }
    
    @Override
    public JSONObject testDistributionDriver(String instanceId) {
        try {
            // 通过反射调用GaDistributionDriverLoaderImpl的distributionDriver方法
            Class<?> driverClass = Class.forName("com.workplat.electronic.certificate.GaDistributionDriverLoaderImpl");
            Object driverInstance = driverClass.newInstance();
            
            // 创建DistributionDriverInput对象
            Class<?> inputClass = Class.forName("com.workplat.gss.common.script.model.declare.DistributionDriverInput");
            Object inputInstance = inputClass.newInstance();
            
            // 设置instanceId
            Method setInstanceIdMethod = inputClass.getMethod("setInstanceId", String.class);
            setInstanceIdMethod.invoke(inputInstance, instanceId);
            
            // 调用distributionDriver方法
            Method distributionDriverMethod = driverClass.getMethod("distributionDriver", inputClass);
            Object result = distributionDriverMethod.invoke(driverInstance, inputInstance);
            
            // 将结果转换为JSONObject返回
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", true);
            jsonObject.put("result", result);
            return jsonObject;
        } catch (Exception e) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", false);
            jsonObject.put("error", e.getMessage());
            return jsonObject;
        }
    }
}
