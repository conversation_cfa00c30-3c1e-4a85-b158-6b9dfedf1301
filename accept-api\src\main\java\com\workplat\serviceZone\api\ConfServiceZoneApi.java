package com.workplat.serviceZone.api;

import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.serviceZone.dto.ConfServiceZoneDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.dto.ServiceZoneQueryDTO;
import com.workplat.serviceZone.vo.ConfServiceZoneVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置API接口
 */
@Tag(name = "服务专区配置", description = "服务专区配置管理接口")
@RequestMapping("/api/serviceZone")
public interface ConfServiceZoneApi {

    @Operation(summary = "分页查询服务专区")
    @GetMapping("/queryPage")
    ResponseData<Page<ConfServiceZoneVO>> queryPage(
            @Parameter ServiceZoneQueryDTO queryDTO,
            @Parameter(description = "分页参数") PageableDTO pageableDTO);

    @Operation(summary = "查询所有启用的服务专区")
    @GetMapping("/getEnabled")
    ResponseData<List<ConfServiceZoneVO>> getEnabled();

    @Operation(summary = "根据ID查询服务专区详情")
    @GetMapping("/getById")
    ResponseData<ConfServiceZoneVO> getById(
            @Parameter(description = "专区ID") String id);

    @Operation(summary = "新增服务专区")
    @PostMapping("/add")
    ResponseData<String> add(@Valid @RequestBody ConfServiceZoneDTO dto);

    @Operation(summary = "修改服务专区")
    @PostMapping("/updateById")
    ResponseData<Void> updateById(@Valid @RequestBody ConfServiceZoneDTO dto);

    @Operation(summary = "删除服务专区")
    @PostMapping("/deleteById")
    ResponseData<Void> deleteById(@RequestBody ConfServiceZoneDTO dto);

    @Operation(summary = "启用/禁用服务专区")
    @PostMapping("/updateEnabled")
    ResponseData<Void> updateEnabled(
            @Parameter(description = "专区ID") String id,
            @Parameter(description = "是否启用") Boolean enabled);

    @Operation(summary = "根据渠道查询服务专区")
    @GetMapping("/getByChannel")
    ResponseData<List<ConfServiceZoneVO>> getByChannel(
            @Parameter(description = "渠道") String channel);
}
