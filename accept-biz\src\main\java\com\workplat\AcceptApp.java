package com.workplat;

import com.workplat.gss.common.core.repository.BaseRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Author: Odin
 * @Date: 2024/9/20 10:35
 * @Description:
 */

@Slf4j
@EnableAsync
@EnableScheduling
@EnableCaching
@EnableTransactionManagement
@EnableDiscoveryClient
@EntityScan(basePackages = {"com.workplat"})
@EnableJpaRepositories(repositoryBaseClass = BaseRepositoryImpl.class, basePackages = {"com.workplat"})
@SpringBootApplication(scanBasePackages = {"com.workplat"})
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableDubbo(scanBasePackages = {"com.workplat"})
public class AcceptApp extends SpringBootServletInitializer {
    public static void main(String[] args) {
        SpringApplication.run(AcceptApp.class, args);
    }
}
