package com.workplat.componentEngine.adapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.workplat.accept.user.util.GatewayUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 默认远程数据适配器实现
 * 处理标准的远程数据获取和转换逻辑
 * 
 * <AUTHOR>
 * @date 2025/06/25
 */
@Slf4j
@Component
public class GatewayRemoteDataAdapter implements RemoteDataAdapter {

    private final DefaultRemoteDataAdapter defaultAdapter;

    public GatewayRemoteDataAdapter(DefaultRemoteDataAdapter defaultAdapter) {
        this.defaultAdapter = defaultAdapter;
    }

    @Override
    public String fetchData(String url, HttpMethod method, HttpHeaders headers, String requestBody, Map<String, Object>  params) {
        try {
            // 将HttpHeaders转换为Map<String, String>，仅保留单值
            Map<String, String> headerMap = new HashMap<>();
            for (String key : headers.keySet()) {
                List<String> values = headers.get(key);
                if (values != null && !values.isEmpty()) {
                    headerMap.put(key, values.getFirst());  // 只取第一个值
                }
            }
            // 判断是GET还是POST请求
            if (method == HttpMethod.GET) {
                return GatewayUtils.executeGetRequest(url, headerMap, params, false, false);
            } else {
                return GatewayUtils.executePostToBodyRequest(url, headerMap, requestBody, false, false);
            }
        } catch (Exception e) {
            log.error("远程数据获取失败: url={}, method={}, error={}", url, method, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Object transformData(String responseBody) {
        //委托给默认适配器处理
        return defaultAdapter.transformData(responseBody);
    }
    
    @Override
    public JSONObject getConfig(String content, String configKey) {
        return defaultAdapter.getConfig(content, configKey);
    }
}
