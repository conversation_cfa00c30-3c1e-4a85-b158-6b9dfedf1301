package com.workplat.matter.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR>
 * @package com.workplat.matter.entity
 * @description 问题推荐
 * @date 2025/5/26 16:35
 */
@Setter
@Getter
@Entity
@Table(name = "biz_question_recommend")
public class BizQuestionRecommend extends BaseEntity {

    @Comment("问题名称")
    @Column(name = "name", length = 50)
    private String name;

    @Comment("问题类型")
    @Column(name = "type", length = 50)
    private String type;
}
