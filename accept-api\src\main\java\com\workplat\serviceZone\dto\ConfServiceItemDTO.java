package com.workplat.serviceZone.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置DTO
 */
@Data
public class ConfServiceItemDTO {

    /**
     * 主键ID（更新时必填）
     */
    private String id;

    /**
     * 服务名称
     */
    @NotBlank(message = "服务名称不能为空")
    @Size(max = 100, message = "服务名称长度不能超过100个字符")
    private String name;

    /**
     * 发送内容
     */
    @Size(max = 1000, message = "发送内容长度不能超过1000个字符")
    private String sendContent;

    /**
     * 渠道
     */
    @Pattern(regexp = "^(pc|wx)$", message = "渠道只能是pc或wx")
    private String channel;

    /**
     * 分类标签
     */
    @Size(max = 50, message = "分类标签长度不能超过50个字符")
    private String category;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 关联的专区ID列表（多对多关系）
     */
    private List<String> serviceZoneIds;
}
