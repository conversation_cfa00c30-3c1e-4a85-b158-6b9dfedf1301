{"tableName": "", "renderList": [{"id": "pepk8Ilwya", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "QNKFTpk-g", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "86d7df3193bd48fc9653d54c3905d817", "key": "loanTypeName", "field": "loanTypeName", "label": "支付方式", "options": [{"label": "全款", "value": "全款", "disabled": false}, {"label": "分期", "value": "分期", "disabled": false}, {"label": "贷款", "value": "贷款", "disabled": false}], "disabled": true, "functions": [{"key": "changezffs", "name": "change事件", "value": "if (form.formObj[\"loanTypeName\"]) {\r\n  if (form.formObj[\"loanTypeName\"] === \"贷款\") {\r\n    if (form.formObj[\"dkqk\"] === \"全款付清\") {\r\n      form.updateFormVisible([\"dkfs\", \"loanBank\"], [false, false]);\r\n    } else if (\r\n      form.formObj[\"dkqk\"] === \"贷款已还清\" ||\r\n      form.formObj[\"dkqk\"] === \"未还清\"\r\n    ) {\r\n      form.updateFormVisible([\"dkfs\", \"loanBank\"], [true, true]);\r\n    } else {\r\n      form.updateFormVisible([\"dkqk\",\"dkfs\", \"loanBank\"], [true,false, false]);\r\n    }\r\n  } else {\r\n    form.updateFormVisible([\"dkqk\", \"dkfs\", \"loanBank\"], [false, false, false]);\r\n  }\r\n} else {\r\n  form.updateFormVisible([\"dkqk\", \"dkfs\", \"loanBank\"], [false, false, false]);\r\n}\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "支付方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "e_qga1IOv", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "a23cadf7ea3a4dad88d40c00fed968da", "key": "dkqk", "field": "dkqk", "label": "贷款情况", "options": [{"label": "全款付清", "value": "全款付清", "disabled": false}, {"label": "贷款已还清", "value": "贷款已还清", "disabled": false}, {"label": "未还清", "value": "未还清", "disabled": false}], "disabled": false, "functions": [{"key": "changedkqk", "name": "change事件", "value": "if (form.formObj[\"loanTypeName\"]) {\r\n  if (form.formObj[\"loanTypeName\"] === \"贷款\") {\r\n    if (form.formObj[\"dkqk\"] === \"全款付清\") {\r\n      form.updateFormVisible([\"dkfs\", \"loanBank\"], [false, false]);\r\n    } else if (\r\n      form.formObj[\"dkqk\"] === \"贷款已还清\" ||\r\n      form.formObj[\"dkqk\"] === \"未还清\"\r\n    ) {\r\n      form.updateFormVisible([\"dkfs\", \"loanBank\"], [true, true]);\r\n    } else {\r\n      form.updateFormVisible([\"dkfs\", \"loanBank\"], [false, false]);\r\n    }\r\n  } else {\r\n    form.updateFormVisible([\"dkqk\", \"dkfs\", \"loanBank\"], [false, false, false]);\r\n  }\r\n} else {\r\n  form.updateFormVisible([\"dkqk\", \"dkfs\", \"loanBank\"], [false, false, false]);\r\n}\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "贷款情况不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "I1whUyumU", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "f02c04c600e7486b8cf2bdbaab583713", "key": "dkfs", "field": "dkfs", "label": "贷款方式", "options": [{"label": "纯商业贷款", "value": " 纯商业贷款", "disabled": false}, {"label": "纯公积金贷款", "value": "纯公积金贷款", "disabled": false}, {"label": "组合贷款", "value": "组合贷款", "disabled": false}], "disabled": false, "functions": [{"key": "changedkfs", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "贷款方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "W3rnNqNOqo", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "loanBank", "field": "loanBank", "label": "贷款银行", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "change1lWQaj7_j", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "\r\nconst dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.LOAN_BANK;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "贷款银行不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}], "props": {"show": true, "field": "2pDir9HHI", "title": "支付方式", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "pLq2WYkxqA", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "606rYgGNp", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "64096661b7ac424595ac9bc80262e189", "key": "name", "field": "name", "label": "姓名", "default": "", "disabled": true, "readonly": false, "clearable": false, "functions": [{"key": "inputmfxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfxm", "name": "foucs事件", "value": ""}, {"key": "changemfxm", "name": "change事件", "value": ""}, {"key": "clickmfxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showAppendBtn": false, "appendBtnColor": "#2c8ef1"}, "rules": [{"message": "姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "Mg4is4EuWM", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "cardtype", "field": "cardtype", "label": "证件类型", "default": "", "options": [{"label": "居民身份证", "value": "居民身份证", "disabled": false}], "disabled": false, "functions": [{"key": "changeqkl5WYuel", "name": "change事件", "value": "// 身份证控制代理人\r\nconst addMflbForm = form.formObj['jyhtYsfSfdsrModel']\r\nfunction isMinor(idCard) {\r\n  // 校验身份证号码是否为 18 位\r\n  if (!/^\\d{17}[\\dXx]$/.test(idCard)) {\r\n    return false;\r\n  }\r\n  // 提取出生年月日\r\n  const birthDateStr = idCard.slice(6, 14);\r\n  const birthDate = new Date(`${birthDateStr.slice(0, 4)}-${birthDateStr.slice(4, 6)}-${birthDateStr.slice(6)}`);\r\n  const currentDate = new Date();\r\n  const age = currentDate.getFullYear() - birthDate.getFullYear();\r\n  const monthDiff = currentDate.getMonth() - birthDate.getMonth();\r\n  if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {\r\n    // 如果生日还未到，年龄减 1\r\n    return age - 1 < 18;\r\n  }\r\n  return age < 18;\r\n}\r\nif (addMflbForm) {\r\n    addMflbForm.forEach((item, index) => {\r\n        //未成年\r\n        if(item.cardtype=='201'&&isMinor(item.cardno)){\r\n            form.updateFormVisible(\r\n            [\r\n              `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n              `jyhtYsfSfdsrModel.${index}.sfzmcl`\r\n            ],[true,true,true,true,true,true,true]\r\n          )\r\n        }else{\r\n            form.updateFormVisible(\r\n            [\r\n              `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n              `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n              `jyhtYsfSfdsrModel.${index}.sfzmcl`\r\n            ],[false,false,false,false,false,false,false]\r\n          )\r\n        }\r\n    })\r\n}\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "居民身份证", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_ZJLX;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "证件类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "Q0dD-t8dK", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "a150c0b374274cec9068c1228265d4ef", "key": "cardno", "field": "cardno", "label": "买方证件号码", "default": "", "disabled": true, "readonly": false, "clearable": false, "functions": [{"key": "inputcardno", "name": "input事件", "value": "console.log(form)"}, {"key": "f<PERSON><PERSON><PERSON><PERSON>", "name": "foucs事件", "value": ""}, {"key": "changecardno", "name": "change事件", "value": "\r\n// 身份证控制代理人\r\nconst buyerForm = form.formObj['jyhtYsfSfdsrModel']\r\nfunction isMinor(idCard) {\r\n  // 校验身份证号码是否为 18 位\r\n  if (!/^\\d{17}[\\dXx]$/.test(idCard)) {\r\n    return false\r\n  }\r\n  // 提取出生年月日\r\n  const birthDateStr = idCard.slice(6, 14)\r\n  const birthDate = new Date(\r\n    `${birthDateStr.slice(0, 4)}-${birthDateStr.slice(4, 6)}-${birthDateStr.slice(6)}`\r\n  )\r\n  const currentDate = new Date()\r\n  const age = currentDate.getFullYear() - birthDate.getFullYear()\r\n  const monthDiff = currentDate.getMonth() - birthDate.getMonth()\r\n  if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {\r\n    // 如果生日还未到，年龄减 1\r\n    return age - 1 < 18\r\n  }\r\n  return age < 18\r\n}\r\nif(buyerForm){\r\nbuyerForm.forEach((item, index) => {\r\n  //  校验未成年，隐藏代理人信息\r\n  if (item.cardtype == '居民身份证' && isMinor(item.cardno)) {\r\n    form.updateFormVisible(\r\n      [\r\n        `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n        `jyhtYsfSfdsrModel.${index}.sfzmcl`\r\n      ],\r\n      [true, true, true, true, true, true, true]\r\n    )\r\n  } else {\r\n    form.updateFormVisible(\r\n      [\r\n        `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n        `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n        `jyhtYsfSfdsrModel.${index}.sfzmcl`\r\n      ],\r\n      [false, false, false, false, false, false, false]\r\n    )\r\n  }\r\n  \r\n})\r\n}"}, {"key": "clickcardno", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "买方证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "zFpo9ecBX", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "8a81dd34a9394fb5b4d0b9fbe882b593", "key": "tel", "field": "tel", "label": "买方手机号", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfsjh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfsjh", "name": "foucs事件", "value": ""}, {"key": "changemfsjh", "name": "change事件", "value": ""}, {"key": "clickmfsjh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "为确保买方能实时查询办件信息，请如实填写买方手机号码。", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "买方手机号不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "O5xT0378V", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"id": "0e29ab8b4e6b45a08d776f6d9ed2c768", "key": "mfsfxytjgyr", "field": "mfsfxytjgyr", "label": "买方是否需要添加共有人", "options": [{"label": "是", "value": "是", "disabled": false}, {"label": "否", "value": "否", "disabled": false}], "disabled": false, "functions": [{"key": "changemfsfxytjgyr", "name": "change事件", "value": "// 婚姻状态\r\nconsole.log(form.formObj,'form.formObj')\r\nconst addMflbForm = form.formObj['jyhtYsfSfdsrModel']\r\nif(addMflbForm){\r\n    addMflbForm.forEach((item,index)=>{\r\n      let arr = []\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.mfhyzk')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.mfpoxm')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.mfpozjlx')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.mfpozjhm')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.mpposjh')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.lyrq')\r\n      arr.push('jyhtYsfSfdsrModel.'+index+'.sorq')\r\n      if(item.mfsfxytjgyr ==='是'){\r\n           form.updateFormVisible(arr,[true, true, true, true, true,false,false])\r\n           const mfhyzkStr = `jyhtYsfSfdsrModel.${index}.mfhyzk`\r\n           form.updateFormObj(mfhyzkStr,'已婚')\r\n      }else{\r\n        const mfhyzkStr = `jyhtYsfSfdsrModel.${index}.mfhyzk`\r\n        form.updateFormObj(mfhyzkStr,'')\r\n       form.updateFormVisible(arr,[false,false,false,false,false,false,false])\r\n      }\r\n    })\r\n}\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "买方是否需要添加共有人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "BTLKbPoDZM", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "nation", "field": "nation", "label": "国籍", "default": "", "options": [{"label": "中华人民共和国", "value": "中华人民共和国", "disabled": false}], "disabled": false, "functions": [{"key": "changeNXOLOkwGw", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "中华人民共和国", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_GJDM;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "国籍不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "qsTDRM-IND", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfssjt", "field": "mfssjt", "label": "所属家庭", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeeAXWhsUbx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.WQ_JT"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": "成年人与其配偶、未成年子女为同一家庭，可以选择“家庭—”；若子女已经成年，则算另一个家庭，选择“家庭二”；若有多个子女已成年，则依次选择“家庭二”、“家庭三”......"}, "rules": [{"message": "所属家庭不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "fPRKqHVhDL", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mftc", "field": "mftc", "label": "买方套次", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "change4d8aQeoe8", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_BFWTC"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": "指本套住房是家庭各成员在苏州市范围内持有的第几套房。其中，家庭成员包括夫妻双方及未成年子女。"}, "rules": [{"message": "买方套次不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "ydzHsMq_e", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "03305279a29144569084d0bd3405e291", "key": "mfhjdz", "field": "mfhjdz", "label": "户籍地址", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfhjdz", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfhjdz", "name": "foucs事件", "value": ""}, {"key": "changemfhjdz", "name": "change事件", "value": ""}, {"key": "clickmfhjdz", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showAppendBtn": false, "appendBtnColor": "#2c8ef1"}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "LGTGtUPpoR", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfhyzk", "field": "mfhyzk", "label": "婚姻状况", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changex0KLtE_mJ", "name": "change事件", "value": "// 婚姻状态\r\nconsole.log(form.formObj, 'form.formObj')\r\nconst addMflbForm = form.formObj['jyhtYsfSfdsrModel']\r\nif (addMflbForm) {\r\n  addMflbForm.forEach((item, index) => {\r\n    let arr = []\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.mfpoxm')\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.mfpozjlx')\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.mfpozjhm')\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.mpposjh')\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.lyrq')\r\n    arr.push('jyhtYsfSfdsrModel.' + index + '.sorq')\r\n    if (item.mfhyzk === '未婚') {\r\n      // 未婚\r\n      form.updateFormVisible(arr, [false, false, false, false, false, false])\r\n    } else if (item.mfhyzk === '已婚') {\r\n      // 已婚\r\n      form.updateFormVisible(arr, [true, true, true, true, false, false])\r\n    } else if (item.mfhyzk === '离异') {\r\n      // 离异\r\n      form.updateFormVisible(arr, [false, false, false, false, true, false])\r\n    } else if (item.mfhyzk === '丧偶') {\r\n      // 丧偶\r\n      form.updateFormVisible(arr, [false, false, false, false, false, true])\r\n    }\r\n  })\r\n}"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_HYZTDM"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "婚姻状况不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "JWXf96cpU", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "57b265f85a1b4f14b9d859e0978a25ca", "key": "mfpoxm", "field": "mfpoxm", "label": "配偶姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfpoxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfpoxm", "name": "foucs事件", "value": ""}, {"key": "changemfpoxm", "name": "change事件", "value": ""}, {"key": "clickmfpoxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "配偶姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "rTCx9HtVTD", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfpozjlx", "field": "mfpozjlx", "label": "配偶证件类型", "default": "", "options": [{"label": "居民身份证", "value": "居民身份证", "disabled": false}], "disabled": false, "functions": [{"key": "changeFRv6_ITSF", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "居民身份证", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_ZJLX;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "配偶证件类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "7rE-CtUOY", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "59ccaad880cf47f484e0ef747a558988", "key": "mfpozjhm", "field": "mfpozjhm", "label": "配偶证件号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfpozjhm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfpozjhm", "name": "foucs事件", "value": ""}, {"key": "changemfpozjhm", "name": "change事件", "value": ""}, {"key": "clickmfpozjhm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "配偶证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "gKCAWVyel", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "babaecced22b472690b8b3ecbc9c5a69", "key": "mpposjh", "field": "mpposjh", "label": "配偶手机号", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmpposjh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmpposjh", "name": "foucs事件", "value": ""}, {"key": "changempposjh", "name": "change事件", "value": ""}, {"key": "clickmpposjh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "wwN0gNRFFT", "icon": "icon-timeSelector", "name": "日期选择器", "child": [], "props": {"key": "", "type": "date", "field": "lyrq", "label": "离异日期（以离婚证或法院判决书上的日期为准）", "format": "YYYY-MM-DD", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "changeyjIND9AJN", "name": "change事件", "value": ""}], "labelWidth": "", "modelValue": null, "tableWidth": "", "isTimeLimit": true, "labelHeight": "", "placeholder": "请选择时间", "valueFormat": "YYYY-MM-DD", "labelDescribe": "", "endPlaceholder": "请选择结束时间", "rangeSeparator": "-", "controlsPosition": "", "startPlaceholder": "请选择开始时间"}, "rules": [{"message": "离异日期不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": true, "specialName": "DataPickerAtribute", "componentName": "a-net-date-picker"}, {"id": "HZjmmLjzQy", "icon": "icon-timeSelector", "name": "日期选择器", "child": [], "props": {"key": "", "type": "date", "field": "sorq", "label": "丧偶日期", "format": "YYYY-MM-DD", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "changejdtO4H8NP", "name": "change事件", "value": ""}], "labelWidth": "", "modelValue": null, "tableWidth": "", "isTimeLimit": false, "labelHeight": "", "placeholder": "请选择时间", "valueFormat": "YYYY-MM-DD", "labelDescribe": "", "endPlaceholder": "请选择结束时间", "rangeSeparator": "-", "controlsPosition": "", "startPlaceholder": "请选择开始时间"}, "rules": [{"message": "丧偶日期不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": true, "specialName": "DataPickerAtribute", "componentName": "a-net-date-picker"}, {"id": "zEyooX5APR", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"field": "sfxydlr", "label": "是否需要代理人", "options": [{"label": "是", "value": "是", "disabled": false}, {"label": "否", "value": "否", "disabled": false}], "disabled": true, "functions": [{"key": "changefbd8NTqms", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "是", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "是否需要代理人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "0mGOBh-KF", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "a0978b47979548d59c59a598a2862723", "key": "dlrxm", "field": "dlrxm", "label": "代理人姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputdlrxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsdlrxm", "name": "foucs事件", "value": ""}, {"key": "changedlrxm", "name": "change事件", "value": ""}, {"key": "clickdlrxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "代理人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "LsK0k3PimF", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "dlrzjlx", "field": "dlrzjlx", "label": "代理人证件类型", "default": "", "options": [{"label": "居民身份证", "value": "居民身份证", "disabled": false}], "disabled": false, "functions": [{"key": "change7DWnaswTe", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "居民身份证", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_ZJLX;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "代理人证件类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "c_F2o7AX_", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "c77be39df37244b2a949698532460672", "key": "dlrz<PERSON><PERSON>", "field": "dlrz<PERSON><PERSON>", "label": "代理人证件号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputdlrzjhm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsdlrzjhm", "name": "foucs事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}, {"key": "clickdlrzjhm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "代理人证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "CKyT5-KFT", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "70286690cfa64a8c995016e31c49075f", "key": "dlrsjh", "field": "dlrsjh", "label": "代理人手机号", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputdlrsjh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsdlrsjh", "name": "foucs事件", "value": ""}, {"key": "changedlrsjh", "name": "change事件", "value": ""}, {"key": "clickdlrsjh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "代理人手机号不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "UtX0rib6OB", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "dlrlx", "field": "dlrlx", "label": "代理人类型", "default": "", "options": [{"label": "法定代理人", "value": "法定代理人", "disabled": false}], "disabled": false, "functions": [{"key": "changeVcr7r9ew2", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "法定代理人", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.PRINCIPAL_TYPE;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "代理人类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "jyhtYsfSfdsrModel", "title": "买方信息", "addText": "", "functions": [{"key": "deletewrAGRBnzn", "name": "delete事件", "value": ""}, {"key": "addwrAGRBnzn", "name": "add事件", "value": ""}], "isNeedMax": true, "isNeedMin": false, "deleteText": "", "innerWidth": 1, "outerWidth": 1, "defaultLine": 0, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}, {"id": "gQ4H9FDzxi", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "bIw9OLoMdb", "icon": "icon-ziti", "name": "文字", "child": [], "props": {"key": "buyerTip", "color": "#000000", "field": "buyerTip", "label": "", "isTips": true, "isTitle": false, "fontSize": "16px", "functions": [{"key": "clickE9MtQXsrd", "name": "click事件", "value": ""}], "tipsTitle": "", "tipsWidth": "", "fontWeight": 500, "labelWidth": "", "modelValue": "若买方是成年人且有未成年子女，买方协查人添加其未成年子女；若买方是未成年人，则添加其父母。", "tableWidth": "", "tipsContent": "", "tipsPosition": "right", "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needRules": false, "needSpecial": false, "componentName": "a-net-text"}, {"id": "FlfBpxDqU", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "fe0af26f0d964f27aaf9601f8f8478db", "key": "mfxcrxm", "field": "mfxcrxm", "label": "买方协查人姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfxcrxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfxcrxm", "name": "foucs事件", "value": ""}, {"key": "changemfxcrxm", "name": "change事件", "value": ""}, {"key": "clickmfxcrxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "买方协查人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "FKTlibDkmi", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfxcrcygx", "field": "mfxcrcygx", "label": "成员关系", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "change4Dr2SB5vE", "name": "change事件", "value": "// 买方协查人\r\ntry {\r\n  const buyerInvestsForm = form.formObj[\"buyerInvests\"];\r\n  if (buyerInvestsForm) {\r\n    buyerInvestsForm.forEach((item, index) => {\r\n      let arr = [];\r\n      arr.push(\"buyerInvests.\" + index + \".mfxcrsjh\");\r\n      if (item.mfxcrcygx === \"子女\") {\r\n        form.updateFormVisible(arr, [false]);\r\n      } else {\r\n        form.updateFormVisible(arr, [true]);\r\n      }\r\n    });\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"error\");\r\n}\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.WQ_GX"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "成员关系不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "7Oe5Efhbt5", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfxcrssjt", "field": "mfxcrssjt", "label": "买方协查人所属家庭", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeWwHynz47W", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": ""}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "买方协查人所属家庭不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "KZdil5nxaQ", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "mfxcrzjlx", "field": "mfxcrzjlx", "label": "买方协查人证件类型", "default": "", "options": [{"label": "居民身份证", "value": "居民身份证", "disabled": false}], "disabled": false, "functions": [{"key": "changelOq_M0Hzb", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "居民身份证", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_ZJLX;"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "买方协查人证件类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "Rr3Z6dNEH", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "01ae9feb10fb44f381db7b738e744b25", "key": "mfxcrzjhm", "field": "mfxcrzjhm", "label": "买方协查人证件号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfxcrzjhm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfxcrzjhm", "name": "foucs事件", "value": ""}, {"key": "changemfxcrzjhm", "name": "change事件", "value": ""}, {"key": "clickmfxcrzjhm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"trigger": "blur", "validatorStr": "function isMinor(idCard) {\r\n  // 校验身份证号码是否为 18 位\r\n  if (!/^\\d{17}[\\dXx]$/.test(idCard)) {\r\n    return false;\r\n  }\r\n  // 提取出生年月日\r\n  const birthDateStr = idCard.slice(6, 14);\r\n  const birthDate = new Date(\r\n    `${birthDateStr.slice(0, 4)}-${birthDateStr.slice(\r\n      4,\r\n      6\r\n    )}-${birthDateStr.slice(6)}`\r\n  );\r\n  const currentDate = new Date();\r\n  const age = currentDate.getFullYear() - birthDate.getFullYear();\r\n  const monthDiff = currentDate.getMonth() - birthDate.getMonth();\r\n  if (\r\n    monthDiff < 0 ||\r\n    (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())\r\n  ) {\r\n    // 如果生日还未到，年龄减 1\r\n    return age - 1 < 18;\r\n  }\r\n  return age < 18;\r\n}\r\n\r\nif (!value) {\r\n  callback(new Error('买方协查人证件号码不能为空'))\r\n} else {\r\n  // 开始校验\r\n  const buyerInvestsList = form.formObj[\"buyerInvests\"];\r\n  if (buyerInvestsList) {\r\n    //  是否有重复证件号码\r\n    const idCardCount = buyerInvestsList.reduce((count, item) => {\r\n      if (item.mfxcrzjhm === value) {\r\n        count++;\r\n      }\r\n      return count;\r\n    }, 0);\r\n\r\n    if (idCardCount > 1) {\r\n      callback(new Error('买方协查人证件号码不能重复'))\r\n    }\r\n\r\n    const arr = buyerInvestsList.find((item) => item.mfxcrzjhm === value);\r\n    if (arr) {\r\n      const idCardRegex = /(^\\d{15}$)|(^\\d{17}([0-9]|X|x)$)/;\r\n      if (!idCardRegex.test(arr.mfxcrzjhm)) {\r\n        callback(new Error('买方协查人身份证号码格式不正确'))\r\n      }\r\n      if (arr.mfxcrcygx === \"子女\" && !isMinor(arr.mfxcrzjhm)) {\r\n        callback(new Error('年满18周岁的子女无需协查，请检查身份证号码是否正确！'))\r\n      }\r\n      if (arr.mfxcrcygx === \"父母\" && isMinor(arr.mfxcrzjhm)) {\r\n        callback(new Error('请检查身份证号码是否正确！'))\r\n      }\r\n    }\r\n    callback();\r\n  }\r\n  callback();\r\n}\r\n"}, {"message": "买方协查人证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "C9f6o69yZ", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "d7ef8c85bf204dfdafd6d2866850da24", "key": "mfxcrsjh", "field": "mfxcrsjh", "label": "买方协查人手机号", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputmfxcrsjh", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsmfxcrsjh", "name": "foucs事件", "value": ""}, {"key": "changemfxcrsjh", "name": "change事件", "value": ""}, {"key": "clickmfxcrsjh", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "买方协查人手机号不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "buyerInvests", "title": "买方协查人信息", "addText": "新增", "functions": [{"key": "delete1bquR9dKF", "name": "delete事件", "value": ""}, {"key": "add1bquR9dKF", "name": "add事件", "value": ""}], "isNeedMax": false, "isNeedMin": false, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": 0, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}, {"id": "zYylb3SeHE", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "wKQOCBjje", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "ac621d0cbd534531935510c7506e8a4a", "key": "jsfs", "field": "jsfs", "label": "交税方式", "default": "", "options": [{"label": "线上缴税", "value": "线上缴税", "disabled": false}, {"label": "线下缴税", "value": "线下缴税", "disabled": false}], "disabled": false, "functions": [{"key": "changejsfs", "name": "change事件", "value": "// 缴税方式\r\n\r\nconst jffsForm = form.formObj['jsfs']\r\nif (jffsForm === '线上缴税') {\r\n  form.updateFormVisible(\r\n    ['xzqh', 'fwcx', 'sfcqyh', 'qszydx', 'qszyyt', 'qszyfs', 'area'],\r\n    [true, true, true, true, true, true, true]\r\n  )\r\n} else {\r\n  form.updateFormVisible(\r\n    ['xzqh', 'fwcx', 'sfcqyh', 'qszydx', 'qszyyt', 'qszyfs', 'area'],\r\n    [false, false, false, false, false, false, false]\r\n  )\r\n}"}], "optionKey": "", "labelWidth": "", "modelValue": "线上缴税", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "交税方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "UPpjtcdXH", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "6c660c4bb6704373af0eb0c0321c6c7d", "key": "xzqh", "field": "xzqh", "label": "行政区划", "default": "", "options": [{"label": "太仓市", "value": "太仓市", "disabled": false}], "disabled": false, "functions": [{"key": "changexzqh", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "太仓市", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "行政区划不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "3UatnWGNEw", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "fwcx", "field": "fwcx", "label": "房屋朝向", "default": "", "options": [{"label": "南", "value": "南", "disabled": false}], "disabled": false, "functions": [{"key": "changepf26frxcl", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "南", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const dict = JSON.parse(sessionStorage.getItem(\"oneHouseDict\"));\r\nconsole.log(dict,'dict');\r\nreturn dict.TAX_FWCX"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "房屋朝向不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "XITXrtAcc", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "5b126ee279964e6d8463477c6b953d6f", "key": "sfcqyh", "field": "sfcqyh", "label": "是否享受拆迁减免", "default": "", "options": [{"label": "否", "value": "否", "disabled": false}], "disabled": false, "functions": [{"key": "changesfxscqjm", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "否", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "是否享受拆迁减免不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "xV9QgBPys", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "d157a6cf39984d60b32796ff4c38b0f9", "key": "qszydx", "field": "qszydx", "label": "转移对象", "default": "", "options": [{"label": "非住房", "value": "非住房", "disabled": false}, {"label": "商品住房", "value": "商品住房", "disabled": false}, {"label": "保障性住房", "value": "保障性住房", "disabled": false}, {"label": "其他住房", "value": "其他住房", "disabled": false}], "disabled": false, "functions": [{"key": "changezydx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "转移对象不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "m6_jDQTEu", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "54b70a1e2917402ab0fafdbc2a4b9b6a", "key": "qszyyt", "field": "qszyyt", "label": "转移用途", "default": "", "options": [{"label": "居住", "value": "居住", "disabled": false}, {"label": "商业", "value": "商业", "disabled": false}, {"label": "办公", "value": "办公", "disabled": false}, {"label": "商住", "value": "商住", "disabled": false}, {"label": "附属建筑", "value": "附属建筑", "disabled": false}, {"label": "工业", "value": "工业", "disabled": false}, {"label": "其他", "value": "其他", "disabled": false}], "disabled": false, "functions": [{"key": "<PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "转移用途不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "w1sINNmEvb", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "qszyfs", "field": "qszyfs", "label": "转移方式", "default": "", "options": [{"label": "买卖", "value": "买卖", "disabled": false}], "disabled": false, "functions": [{"key": "change_n-xYgGXS", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "买卖", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": ""}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "转移方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "4KuFOSqoP", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "48307c3e667c437ebfc0668c34c39016", "key": "area", "field": "area", "label": "所属区域", "default": "", "options": [{"label": "城厢镇", "value": "城厢镇", "disabled": false}, {"label": "浏河镇", "value": "浏河镇", "disabled": false}, {"label": "沙溪镇", "value": "沙溪镇", "disabled": false}, {"label": "璜泾镇", "value": "璜泾镇", "disabled": false}, {"label": "双凤镇", "value": "双凤镇", "disabled": false}, {"label": "浮桥镇", "value": "浮桥镇", "disabled": false}, {"label": "科教新城", "value": "科教新城", "disabled": false}, {"label": "陆渡街道", "value": "陆渡街道", "disabled": false}, {"label": "娄东街道", "value": "娄东街道", "disabled": false}, {"label": "港区", "value": "港区", "disabled": false}], "disabled": false, "functions": [{"key": "changeswqy", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "所属区域不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}], "props": {"show": true, "field": "taxInfo", "title": "税务申报信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "ba2fNgeL5p", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "BRYHpJw_ID", "icon": "icon-ziti", "name": "文字", "child": [], "props": {"key": "", "color": "#000000", "field": "houseTip", "label": "", "isTips": true, "isTitle": false, "fontSize": "16px", "functions": [{"key": "clickE9MtQXsrd", "name": "click事件", "value": ""}], "tipsTitle": "", "tipsWidth": "", "fontWeight": 500, "labelWidth": "", "modelValue": "成年人与其配偶、未成年子女为同一家庭，可以选择“家庭—”；若子女已经成年，则算另一个家庭，选择“家庭二”；若有多个子女已成年，则依次选择“家庭二”、“家庭三”......", "tableWidth": "", "tipsContent": "", "tipsPosition": "right", "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needRules": false, "needSpecial": false, "componentName": "a-net-text"}, {"id": "gQsb70g01h", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "jtcy1", "field": "jtcy1", "label": "“家庭一”成员（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeSg2xEFKhF", "name": "change事件", "value": "const map = {\r\n  1: \"家庭一\",\r\n  2: \"家庭二\",\r\n  3: \"家庭三\",\r\n  4: \"家庭四\",\r\n  5: \"家庭五\",\r\n};\r\nfunction getJtcy(arr, isArray) {\r\n  //如果item为空，且后边存在非空项，将后续的值向前赋值，并清空后续值，确保item的值从数组头部排列\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if ((arr[i] === \"\" && !isArray) || (isArray && arr[i].length == 0)) {\r\n      for (let j = i + 1; j < arr.length; j++) {\r\n        if (arr[j]) {\r\n          arr[i] = arr[j];\r\n          const key = \"jtcy\" + (i + 1);\r\n          const value = arr[j];\r\n          form.updateFormObj(key, value);\r\n          arr[j] = isArray ? [] : \"\";\r\n\r\n          const key2 = \"jtcy\" + (j + 1);\r\n          form.updateFormObj(key2, isArray ? [] : \"\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  console.log(arr, \"arr\");\r\n  // 清空buyerList的mfssjt\r\n  buyerList.forEach((item) => {\r\n    item.mfssjt = \"\";\r\n  });\r\n  // 清空buyerInvestsList的mfxcrssjt\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item) => {\r\n      item.mfxcrssjt = \"\";\r\n    });\r\n  }\r\n\r\n  arr.forEach((item, index) => {\r\n    if ((item !== \"\" && !isArray) || (isArray && item.length > 0)) {\r\n      const jtcyItem = isArray ? item : item.split(\"、\");\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [true]);\r\n      // 遍历 buyerList 查找匹配项\r\n      buyerList.forEach((buyerItem) => {\r\n        if (jtcyItem.includes(buyerItem.cardno)) {\r\n          buyerItem.mfssjt = map[(index + 1).toString()];\r\n        }\r\n      });\r\n      // 遍历 buyerInvestsList 查找匹配项\r\n      if (buyerInvestsList) {\r\n        buyerInvestsList.forEach((buyerInvestsItem) => {\r\n          if (\r\n            jtcyItem.includes(buyerInvestsItem.mfxcrzjhm) &&\r\n            buyerInvestsItem.mfxcrzjhm\r\n          ) {\r\n            buyerInvestsItem.mfxcrssjt = map[(index + 1).toString()];\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [false]);\r\n    }\r\n  });\r\n  //如果buyerList中存在mfssjt为空或者buyerInvestsList存在mfxcrssjt为空\r\n  if (\r\n    buyerList.some((item) => !item.mfssjt) ||\r\n    (buyerInvestsList &&\r\n      buyerInvestsList.some((item) => !item.mfxcrssjt && item.mfxcrzjhm))\r\n  ) {\r\n    //   找出arr中第一个为空的Index\r\n    const firstEmptyIndex = arr.findIndex(\r\n      (item) => (item === \"\" && !isArray) || (isArray && item.length == 0)\r\n    );\r\n\r\n    const filed = \"jtcy\" + (firstEmptyIndex + 1);\r\n    form.updateFormVisible([filed], [true]);\r\n  }\r\n}\r\nconst buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nconst jtcy1Value = form.formObj[\"jtcy1\"];\r\nconst jtcy2Value = form.formObj[\"jtcy2\"];\r\nconst jtcy3Value = form.formObj[\"jtcy3\"];\r\nconst jtcy4Value = form.formObj[\"jtcy4\"];\r\nconst jtcy5Value = form.formObj[\"jtcy5\"];\r\nconst jtcyValue = [jtcy1Value, jtcy2Value, jtcy3Value, jtcy4Value, jtcy5Value];\r\nconst isArray =\r\n  Array.isArray(jtcy1Value) ||\r\n  Array.isArray(jtcy2Value) ||\r\n  Array.isArray(jtcy3Value) ||\r\n  Array.isArray(jtcy4Value) ||\r\n  Array.isArray(jtcy5Value);\r\ngetJtcy(jtcyValue, isArray);\r\n\r\nconsole.log(\"所属家庭end\", form, form.formObj);\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst formArr = Array.isArray(form.formObj[\"jtcy1\"])\r\n  ? form.formObj[\"jtcy1\"]\r\n  : form.formObj[\"jtcy1\"].split(\"、\");\r\nconst jtcy1Value = formArr;\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  if (!item.mfssjt) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n  if (jtcy1Value.includes(item.cardno)) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n});\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nif (buyerInvestsList) {\r\n  buyerInvestsList.forEach((item) => {\r\n    if (!item.mfxcrssjt && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n    if (jtcy1Value.includes(item.mfxcrzjhm) && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n  });\r\n}\r\nreturn list;\r\n"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "家庭一成员不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "c-PWDfDq2E", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "jtcy2", "field": "jtcy2", "label": "“家庭二”成员（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "change74odoccsg", "name": "change事件", "value": "const map = {\r\n  1: \"家庭一\",\r\n  2: \"家庭二\",\r\n  3: \"家庭三\",\r\n  4: \"家庭四\",\r\n  5: \"家庭五\",\r\n};\r\nfunction getJtcy(arr, isArray) {\r\n  //如果item为空，且后边存在非空项，将后续的值向前赋值，并清空后续值，确保item的值从数组头部排列\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if ((arr[i] === \"\" && !isArray) || (isArray && arr[i].length == 0)) {\r\n      for (let j = i + 1; j < arr.length; j++) {\r\n        if (arr[j]) {\r\n          arr[i] = arr[j];\r\n          const key = \"jtcy\" + (i + 1);\r\n          const value = arr[j];\r\n          form.updateFormObj(key, value);\r\n          arr[j] = isArray ? [] : \"\";\r\n\r\n          const key2 = \"jtcy\" + (j + 1);\r\n          form.updateFormObj(key2, isArray ? [] : \"\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  console.log(arr, \"arr\");\r\n  // 清空buyerList的mfssjt\r\n  buyerList.forEach((item) => {\r\n    item.mfssjt = \"\";\r\n  });\r\n  // 清空buyerInvestsList的mfxcrssjt\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item) => {\r\n      item.mfxcrssjt = \"\";\r\n    });\r\n  }\r\n\r\n  arr.forEach((item, index) => {\r\n    if ((item !== \"\" && !isArray) || (isArray && item.length > 0)) {\r\n      const jtcyItem = isArray ? item : item.split(\"、\");\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [true]);\r\n      // 遍历 buyerList 查找匹配项\r\n      buyerList.forEach((buyerItem) => {\r\n        if (jtcyItem.includes(buyerItem.cardno)) {\r\n          buyerItem.mfssjt = map[(index + 1).toString()];\r\n        }\r\n      });\r\n      // 遍历 buyerInvestsList 查找匹配项\r\n      if (buyerInvestsList) {\r\n        buyerInvestsList.forEach((buyerInvestsItem) => {\r\n          if (\r\n            jtcyItem.includes(buyerInvestsItem.mfxcrzjhm) &&\r\n            buyerInvestsItem.mfxcrzjhm\r\n          ) {\r\n            buyerInvestsItem.mfxcrssjt = map[(index + 1).toString()];\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [false]);\r\n    }\r\n  });\r\n  //如果buyerList中存在mfssjt为空或者buyerInvestsList存在mfxcrssjt为空\r\n  if (\r\n    buyerList.some((item) => !item.mfssjt) ||\r\n    (buyerInvestsList &&\r\n      buyerInvestsList.some((item) => !item.mfxcrssjt && item.mfxcrzjhm))\r\n  ) {\r\n    //   找出arr中第一个为空的Index\r\n    const firstEmptyIndex = arr.findIndex(\r\n      (item) => (item === \"\" && !isArray) || (isArray && item.length == 0)\r\n    );\r\n\r\n    const filed = \"jtcy\" + (firstEmptyIndex + 1);\r\n    form.updateFormVisible([filed], [true]);\r\n  }\r\n}\r\nconst buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nconst jtcy1Value = form.formObj[\"jtcy1\"];\r\nconst jtcy2Value = form.formObj[\"jtcy2\"];\r\nconst jtcy3Value = form.formObj[\"jtcy3\"];\r\nconst jtcy4Value = form.formObj[\"jtcy4\"];\r\nconst jtcy5Value = form.formObj[\"jtcy5\"];\r\nconst jtcyValue = [jtcy1Value, jtcy2Value, jtcy3Value, jtcy4Value, jtcy5Value];\r\nconst isArray =\r\n  Array.isArray(jtcy1Value) ||\r\n  Array.isArray(jtcy2Value) ||\r\n  Array.isArray(jtcy3Value) ||\r\n  Array.isArray(jtcy4Value) ||\r\n  Array.isArray(jtcy5Value);\r\ngetJtcy(jtcyValue, isArray);\r\n\r\nconsole.log(\"所属家庭end\", form, form.formObj);\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst formArr = Array.isArray(form.formObj[\"jtcy2\"])\r\n  ? form.formObj[\"jtcy2\"]\r\n  : form.formObj[\"jtcy2\"].split(\"、\");\r\nconst jtcy1Value = formArr\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  if (!item.mfssjt) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n  if (jtcy1Value.includes(item.cardno)) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n});\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nif (buyerInvestsList) {\r\n  buyerInvestsList.forEach((item) => {\r\n    if (!item.mfxcrssjt && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n    if (jtcy1Value.includes(item.mfxcrzjhm)&& item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n  });\r\n}\r\nreturn list;"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "家庭二成员不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "jZTUGfj-9a", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "jtcy3", "field": "jtcy3", "label": "“家庭三”成员（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeuyvqKWgiS", "name": "change事件", "value": "const map = {\r\n  1: \"家庭一\",\r\n  2: \"家庭二\",\r\n  3: \"家庭三\",\r\n  4: \"家庭四\",\r\n  5: \"家庭五\",\r\n};\r\nfunction getJtcy(arr, isArray) {\r\n  //如果item为空，且后边存在非空项，将后续的值向前赋值，并清空后续值，确保item的值从数组头部排列\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if ((arr[i] === \"\" && !isArray) || (isArray && arr[i].length == 0)) {\r\n      for (let j = i + 1; j < arr.length; j++) {\r\n        if (arr[j]) {\r\n          arr[i] = arr[j];\r\n          const key = \"jtcy\" + (i + 1);\r\n          const value = arr[j];\r\n          form.updateFormObj(key, value);\r\n          arr[j] = isArray ? [] : \"\";\r\n\r\n          const key2 = \"jtcy\" + (j + 1);\r\n          form.updateFormObj(key2, isArray ? [] : \"\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  console.log(arr, \"arr\");\r\n  // 清空buyerList的mfssjt\r\n  buyerList.forEach((item) => {\r\n    item.mfssjt = \"\";\r\n  });\r\n  // 清空buyerInvestsList的mfxcrssjt\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item) => {\r\n      item.mfxcrssjt = \"\";\r\n    });\r\n  }\r\n\r\n  arr.forEach((item, index) => {\r\n    if ((item !== \"\" && !isArray) || (isArray && item.length > 0)) {\r\n      const jtcyItem = isArray ? item : item.split(\"、\");\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [true]);\r\n      // 遍历 buyerList 查找匹配项\r\n      buyerList.forEach((buyerItem) => {\r\n        if (jtcyItem.includes(buyerItem.cardno)) {\r\n          buyerItem.mfssjt = map[(index + 1).toString()];\r\n        }\r\n      });\r\n      // 遍历 buyerInvestsList 查找匹配项\r\n      if (buyerInvestsList) {\r\n        buyerInvestsList.forEach((buyerInvestsItem) => {\r\n          if (\r\n            jtcyItem.includes(buyerInvestsItem.mfxcrzjhm) &&\r\n            buyerInvestsItem.mfxcrzjhm\r\n          ) {\r\n            buyerInvestsItem.mfxcrssjt = map[(index + 1).toString()];\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [false]);\r\n    }\r\n  });\r\n  //如果buyerList中存在mfssjt为空或者buyerInvestsList存在mfxcrssjt为空\r\n  if (\r\n    buyerList.some((item) => !item.mfssjt) ||\r\n    (buyerInvestsList &&\r\n      buyerInvestsList.some((item) => !item.mfxcrssjt && item.mfxcrzjhm))\r\n  ) {\r\n    //   找出arr中第一个为空的Index\r\n    const firstEmptyIndex = arr.findIndex(\r\n      (item) => (item === \"\" && !isArray) || (isArray && item.length == 0)\r\n    );\r\n\r\n    const filed = \"jtcy\" + (firstEmptyIndex + 1);\r\n    form.updateFormVisible([filed], [true]);\r\n  }\r\n}\r\nconst buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nconst jtcy1Value = form.formObj[\"jtcy1\"];\r\nconst jtcy2Value = form.formObj[\"jtcy2\"];\r\nconst jtcy3Value = form.formObj[\"jtcy3\"];\r\nconst jtcy4Value = form.formObj[\"jtcy4\"];\r\nconst jtcy5Value = form.formObj[\"jtcy5\"];\r\nconst jtcyValue = [jtcy1Value, jtcy2Value, jtcy3Value, jtcy4Value, jtcy5Value];\r\nconst isArray =\r\n  Array.isArray(jtcy1Value) ||\r\n  Array.isArray(jtcy2Value) ||\r\n  Array.isArray(jtcy3Value) ||\r\n  Array.isArray(jtcy4Value) ||\r\n  Array.isArray(jtcy5Value);\r\ngetJtcy(jtcyValue, isArray);\r\n\r\nconsole.log(\"所属家庭end\", form, form.formObj);\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst formArr = Array.isArray(form.formObj[\"jtcy3\"])\r\n  ? form.formObj[\"jtcy3\"]\r\n  : form.formObj[\"jtcy3\"].split(\"、\");\r\nconst jtcy1Value = formArr\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  if (!item.mfssjt) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n  if (jtcy1Value.includes(item.cardno)) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n});\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nif (buyerInvestsList) {\r\n  buyerInvestsList.forEach((item) => {\r\n    if (!item.mfxcrssjt && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n    if (jtcy1Value.includes(item.mfxcrzjhm)&& item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n  });\r\n}\r\nreturn list;"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "家庭三成员不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "Q3dVlJ-_Qt", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "jtcy4", "field": "jtcy4", "label": "“家庭四”成员（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeqnAoGAVaK", "name": "change事件", "value": "const map = {\r\n  1: \"家庭一\",\r\n  2: \"家庭二\",\r\n  3: \"家庭三\",\r\n  4: \"家庭四\",\r\n  5: \"家庭五\",\r\n};\r\nfunction getJtcy(arr, isArray) {\r\n  //如果item为空，且后边存在非空项，将后续的值向前赋值，并清空后续值，确保item的值从数组头部排列\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if ((arr[i] === \"\" && !isArray) || (isArray && arr[i].length == 0)) {\r\n      for (let j = i + 1; j < arr.length; j++) {\r\n        if (arr[j]) {\r\n          arr[i] = arr[j];\r\n          const key = \"jtcy\" + (i + 1);\r\n          const value = arr[j];\r\n          form.updateFormObj(key, value);\r\n          arr[j] = isArray ? [] : \"\";\r\n\r\n          const key2 = \"jtcy\" + (j + 1);\r\n          form.updateFormObj(key2, isArray ? [] : \"\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  console.log(arr, \"arr\");\r\n  // 清空buyerList的mfssjt\r\n  buyerList.forEach((item) => {\r\n    item.mfssjt = \"\";\r\n  });\r\n  // 清空buyerInvestsList的mfxcrssjt\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item) => {\r\n      item.mfxcrssjt = \"\";\r\n    });\r\n  }\r\n\r\n  arr.forEach((item, index) => {\r\n    if ((item !== \"\" && !isArray) || (isArray && item.length > 0)) {\r\n      const jtcyItem = isArray ? item : item.split(\"、\");\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [true]);\r\n      // 遍历 buyerList 查找匹配项\r\n      buyerList.forEach((buyerItem) => {\r\n        if (jtcyItem.includes(buyerItem.cardno)) {\r\n          buyerItem.mfssjt = map[(index + 1).toString()];\r\n        }\r\n      });\r\n      // 遍历 buyerInvestsList 查找匹配项\r\n      if (buyerInvestsList) {\r\n        buyerInvestsList.forEach((buyerInvestsItem) => {\r\n          if (\r\n            jtcyItem.includes(buyerInvestsItem.mfxcrzjhm) &&\r\n            buyerInvestsItem.mfxcrzjhm\r\n          ) {\r\n            buyerInvestsItem.mfxcrssjt = map[(index + 1).toString()];\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [false]);\r\n    }\r\n  });\r\n  //如果buyerList中存在mfssjt为空或者buyerInvestsList存在mfxcrssjt为空\r\n  if (\r\n    buyerList.some((item) => !item.mfssjt) ||\r\n    (buyerInvestsList &&\r\n      buyerInvestsList.some((item) => !item.mfxcrssjt && item.mfxcrzjhm))\r\n  ) {\r\n    //   找出arr中第一个为空的Index\r\n    const firstEmptyIndex = arr.findIndex(\r\n      (item) => (item === \"\" && !isArray) || (isArray && item.length == 0)\r\n    );\r\n\r\n    const filed = \"jtcy\" + (firstEmptyIndex + 1);\r\n    form.updateFormVisible([filed], [true]);\r\n  }\r\n}\r\nconst buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nconst jtcy1Value = form.formObj[\"jtcy1\"];\r\nconst jtcy2Value = form.formObj[\"jtcy2\"];\r\nconst jtcy3Value = form.formObj[\"jtcy3\"];\r\nconst jtcy4Value = form.formObj[\"jtcy4\"];\r\nconst jtcy5Value = form.formObj[\"jtcy5\"];\r\nconst jtcyValue = [jtcy1Value, jtcy2Value, jtcy3Value, jtcy4Value, jtcy5Value];\r\nconst isArray =\r\n  Array.isArray(jtcy1Value) ||\r\n  Array.isArray(jtcy2Value) ||\r\n  Array.isArray(jtcy3Value) ||\r\n  Array.isArray(jtcy4Value) ||\r\n  Array.isArray(jtcy5Value);\r\ngetJtcy(jtcyValue, isArray);\r\n\r\nconsole.log(\"所属家庭end\", form, form.formObj);\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst formArr = Array.isArray(form.formObj[\"jtcy4\"])\r\n  ? form.formObj[\"jtcy4\"]\r\n  : form.formObj[\"jtcy4\"].split(\"、\");\r\nconst jtcy1Value = formArr\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  if (!item.mfssjt) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n  if (jtcy1Value.includes(item.cardno)) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n});\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nif (buyerInvestsList) {\r\n  buyerInvestsList.forEach((item) => {\r\n    if (!item.mfxcrssjt && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n    if (jtcy1Value.includes(item.mfxcrzjhm)&& item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n  });\r\n}\r\nreturn list;"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "家庭四成员不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "koETAxIpXi", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "jtcy5", "field": "jtcy5", "label": "“家庭五”成员（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeL_6w7FKDi", "name": "change事件", "value": "const map = {\r\n  1: \"家庭一\",\r\n  2: \"家庭二\",\r\n  3: \"家庭三\",\r\n  4: \"家庭四\",\r\n  5: \"家庭五\",\r\n};\r\nfunction getJtcy(arr, isArray) {\r\n  //如果item为空，且后边存在非空项，将后续的值向前赋值，并清空后续值，确保item的值从数组头部排列\r\n  for (let i = 0; i < arr.length; i++) {\r\n    if ((arr[i] === \"\" && !isArray) || (isArray && arr[i].length == 0)) {\r\n      for (let j = i + 1; j < arr.length; j++) {\r\n        if (arr[j]) {\r\n          arr[i] = arr[j];\r\n          const key = \"jtcy\" + (i + 1);\r\n          const value = arr[j];\r\n          form.updateFormObj(key, value);\r\n          arr[j] = isArray ? [] : \"\";\r\n\r\n          const key2 = \"jtcy\" + (j + 1);\r\n          form.updateFormObj(key2, isArray ? [] : \"\");\r\n        }\r\n      }\r\n    }\r\n  }\r\n  console.log(arr, \"arr\");\r\n  // 清空buyerList的mfssjt\r\n  buyerList.forEach((item) => {\r\n    item.mfssjt = \"\";\r\n  });\r\n  // 清空buyerInvestsList的mfxcrssjt\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item) => {\r\n      item.mfxcrssjt = \"\";\r\n    });\r\n  }\r\n\r\n  arr.forEach((item, index) => {\r\n    if ((item !== \"\" && !isArray) || (isArray && item.length > 0)) {\r\n      const jtcyItem = isArray ? item : item.split(\"、\");\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [true]);\r\n      // 遍历 buyerList 查找匹配项\r\n      buyerList.forEach((buyerItem) => {\r\n        if (jtcyItem.includes(buyerItem.cardno)) {\r\n          buyerItem.mfssjt = map[(index + 1).toString()];\r\n        }\r\n      });\r\n      // 遍历 buyerInvestsList 查找匹配项\r\n      if (buyerInvestsList) {\r\n        buyerInvestsList.forEach((buyerInvestsItem) => {\r\n          if (\r\n            jtcyItem.includes(buyerInvestsItem.mfxcrzjhm) &&\r\n            buyerInvestsItem.mfxcrzjhm\r\n          ) {\r\n            buyerInvestsItem.mfxcrssjt = map[(index + 1).toString()];\r\n          }\r\n        });\r\n      }\r\n    } else {\r\n      const filed = \"jtcy\" + (index + 1);\r\n      form.updateFormVisible([filed], [false]);\r\n    }\r\n  });\r\n  //如果buyerList中存在mfssjt为空或者buyerInvestsList存在mfxcrssjt为空\r\n  if (\r\n    buyerList.some((item) => !item.mfssjt) ||\r\n    (buyerInvestsList &&\r\n      buyerInvestsList.some((item) => !item.mfxcrssjt && item.mfxcrzjhm))\r\n  ) {\r\n    //   找出arr中第一个为空的Index\r\n    const firstEmptyIndex = arr.findIndex(\r\n      (item) => (item === \"\" && !isArray) || (isArray && item.length == 0)\r\n    );\r\n\r\n    const filed = \"jtcy\" + (firstEmptyIndex + 1);\r\n    form.updateFormVisible([filed], [true]);\r\n  }\r\n}\r\nconst buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nconst jtcy1Value = form.formObj[\"jtcy1\"];\r\nconst jtcy2Value = form.formObj[\"jtcy2\"];\r\nconst jtcy3Value = form.formObj[\"jtcy3\"];\r\nconst jtcy4Value = form.formObj[\"jtcy4\"];\r\nconst jtcy5Value = form.formObj[\"jtcy5\"];\r\nconst jtcyValue = [jtcy1Value, jtcy2Value, jtcy3Value, jtcy4Value, jtcy5Value];\r\nconst isArray =\r\n  Array.isArray(jtcy1Value) ||\r\n  Array.isArray(jtcy2Value) ||\r\n  Array.isArray(jtcy3Value) ||\r\n  Array.isArray(jtcy4Value) ||\r\n  Array.isArray(jtcy5Value);\r\ngetJtcy(jtcyValue, isArray);\r\n\r\nconsole.log(\"所属家庭end\", form, form.formObj);\r\n"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst formArr = Array.isArray(form.formObj[\"jtcy5\"])\r\n  ? form.formObj[\"jtcy5\"]\r\n  : form.formObj[\"jtcy5\"].split(\"、\");\r\nconst jtcy1Value = formArr\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  if (!item.mfssjt) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n  if (jtcy1Value.includes(item.cardno)) {\r\n    list.push({\r\n      label: item.name,\r\n      value: item.cardno,\r\n    });\r\n  }\r\n});\r\nconst buyerInvestsList = form.formObj[\"buyerInvests\"];\r\nif (buyerInvestsList) {\r\n  buyerInvestsList.forEach((item) => {\r\n    if (!item.mfxcrssjt && item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n    if (jtcy1Value.includes(item.mfxcrzjhm)&& item.mfxcrzjhm) {\r\n      list.push({\r\n        label: item.mfxcrxm,\r\n        value: item.mfxcrzjhm,\r\n      });\r\n    }\r\n  });\r\n}\r\nreturn list;"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "家庭五成员不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}], "props": {"show": true, "field": "SSJTXX", "title": "所属家庭", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "3N1EzOVzND", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "6Ll7WEsbu", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"id": "8794900ef8494272a5e32bfaeb24615c", "key": "sslx", "field": "sslx", "label": "设施类型", "default": "", "options": [{"label": "自行车库", "value": "1", "disabled": false}, {"label": "汽车库", "value": "2", "disabled": false}, {"label": "阁楼", "value": "3", "disabled": false}, {"label": "储藏室", "value": "4", "disabled": false}, {"label": "地下室", "value": "5", "disabled": false}], "disabled": false, "functions": [{"key": "changesslx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "labelHeight": "", "placeholder": "请选择"}, "rules": [{"message": "设施类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "W2w00LEge", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "419b5c0f6eae42239126565a3a3f4c67", "key": "ssmj", "field": "ssmj", "label": "设施面积(㎡)", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "<PERSON>smj", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsssmj", "name": "foucs事件", "value": ""}, {"key": "<PERSON>smj", "name": "change事件", "value": ""}, {"key": "clickssmj", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showAppendBtn": false, "appendBtnColor": "#2c8ef1"}, "rules": [{"message": "设施面积不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}], "props": {"key": "", "max": 1, "min": 1, "field": "adjunctInfo", "title": "附属设施", "addText": "新增", "functions": [{"key": "delete8-jXaTuTV", "name": "delete事件", "value": ""}, {"key": "add8-jXaTuTV", "name": "add事件", "value": ""}], "isNeedMax": false, "isNeedMin": false, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": 0, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}, {"id": "PWCxo9VrVI", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "1PdtEr3D-", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "df0fe3437a0d4e6094d332a35f15be00", "key": "fpdm", "field": "fpdm", "label": "发票代码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfpdm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfpdm", "name": "foucs事件", "value": ""}, {"key": "changefpdm", "name": "change事件", "value": ""}, {"key": "clickfpdm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "20位发票号码可拆分为12位发票代码+8位发票号码", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "发票代码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "GV8f9624A", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "4835b58dea0d4114a558216bf790be9c", "key": "fphm", "field": "fphm", "label": "发票号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputfphm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsfphm", "name": "foucs事件", "value": ""}, {"key": "changefphm", "name": "change事件", "value": ""}, {"key": "clickfphm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "20位发票号码可拆分为12位发票代码+8位发票号码", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "发票号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}], "props": {"show": true, "field": "invoices", "title": "购房发票信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "U-bIv122c4", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "gnppGxUhX-", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "cqsyfs", "field": "cqsyfs", "label": "产权所有方式", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeLsTiJSawL", "name": "change事件", "value": "const cqsyfsValue = form.formObj[\"cqsyfs\"]\r\nif (cqsyfsValue == '单独所有') {\r\n    form.updateFormVisible(\r\n      ['cqsyqxm', 'cqsyrzb', 'cqsyqxmFS', 'ownerInfoBFSSelf'],\r\n      [true, true, false, false]\r\n    )\r\n  } else if (cqsyfsValue == '共同共有') {\r\n    form.updateFormVisible(\r\n      ['cqsyqxm', 'cqsyrzb', 'cqsyqxmFS', 'ownerInfoBFSSelf'],\r\n      [false, false, true, false]\r\n    )\r\n  } else if (cqsyfsValue == '按份共有') {\r\n    form.updateFormVisible(\r\n      ['cqsyqxm', 'cqsyrzb', 'cqsyqxmFS', 'ownerInfoBFSSelf'],\r\n      [false, false, false, true]\r\n    )\r\n  }"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nlet list = [\r\n  {\r\n    label: \"单独所有\",\r\n    value: \"单独所有\",\r\n  },\r\n  {\r\n    label: \"共同共有\",\r\n    value: \"共同共有\",\r\n  },\r\n  {\r\n    label: \"按份共有\",\r\n    value: \"按份共有\",\r\n  },\r\n];\r\n\r\nif (buyerList.length === 1 && buyerList[0].mfhyzk !== \"已婚\") {\r\n  list = [\r\n    {\r\n      label: \"单独所有\",\r\n      value: \"单独所有\",\r\n    },\r\n  ];\r\n} else {\r\n  list = [\r\n    {\r\n      label: \"单独所有\",\r\n      value: \"单独所有\",\r\n    },\r\n    {\r\n      label: \"共同共有\",\r\n      value: \"共同共有\",\r\n    },\r\n    {\r\n      label: \"按份共有\",\r\n      value: \"按份共有\",\r\n    },\r\n  ];\r\n}\r\n\r\nreturn list;\r\n"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": "若有抵押，登簿信息应与贷款合同中的抵押人一致。"}, "rules": [{"message": "产权所有方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "f74SU52xcq", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "cqsyqxm", "field": "cqsyqxm", "label": "所有人姓名", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeKAfa2PE7a", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno,\r\n  });\r\n  if (item.mfhyzk === \"已婚\") {\r\n    list.push({\r\n      label: item.mfpoxm,\r\n      value: item.mfpozjhm,\r\n    });\r\n  }\r\n});\r\n\r\n// 去重\r\nconst uniqueList = [...new Set(list.map((item) => item.value))].map(\r\n  (value) => ({\r\n    label: list.find((item) => item.value === value).label,\r\n    value,\r\n  })\r\n);\r\n\r\nreturn uniqueList;\r\n"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "所有人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "CYFO-Q8t_t", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "cqsyrzb", "field": "cqsyrzb", "label": "所有权占比", "default": "100%", "disabled": true, "readonly": false, "clearable": false, "functions": [{"key": "inputYnHYM7XvG", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsYnHYM7XvG", "name": "foucs事件", "value": ""}, {"key": "changeYnHYM7XvG", "name": "change事件", "value": ""}, {"key": "clickYnHYM7XvG", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "100%", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "所有权占比不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "rI-RxVitCe", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"key": "cqsyqxmFS", "field": "cqsyqxmFS", "label": "选择共有人（可多选）", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "change2fQtEQgRt", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno,\r\n  });\r\n  if (item.mfhyzk === \"已婚\") {\r\n    list.push({\r\n      label: item.mfpoxm,\r\n      value: item.mfpozjhm,\r\n    });\r\n  }\r\n});\r\n\r\n// 去重\r\nconst uniqueList = [...new Set(list.map((item) => item.value))].map(\r\n  (value) => ({\r\n    label: list.find((item) => item.value === value).label,\r\n    value,\r\n  })\r\n);\r\n\r\nreturn uniqueList;\r\n"}], "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "共有人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "7tqMrRg_bF", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "可新增表格", "type": "addTable", "child": [{"child": [{"id": "BLkJ5786rB", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "cqsyqxm", "field": "cqsyqxm", "label": "所有人姓名", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeKAfa2PE7a", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst ownerInfoBFSList = form.formObj[\"ownerInfoBFS\"];\r\n\r\nlet list = [];\r\n\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno,\r\n  });\r\n  if (item.mfhyzk === \"已婚\") {\r\n    list.push({\r\n      label: item.mfpoxm,\r\n      value: item.mfpozjhm,\r\n    });\r\n  }\r\n});\r\n\r\ntry {\r\n  //   找出list中value等于ownerInfoBFSList[addIndex].cqsyqxm的值，返回符合的对象\r\n  let obj = list.find(\r\n    (item) => item.value === ownerInfoBFSList[addIndex].cqsyqxm\r\n  );\r\n  // 使用 filter 方法过滤掉满足条件的元素\r\n  list = list.filter(\r\n    (item) => !ownerInfoBFSList.some((i) => i.cqsyqxm === item.value)\r\n  );\r\n  if (obj) {\r\n    list.push(obj);\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"使用 filter 方法过滤掉满足条件的元素\");\r\n}\r\n\r\n// 去重\r\nconst uniqueList = [...new Set(list.map((item) => item.value))].map(\r\n  (value) => ({\r\n    label: list.find((item) => item.value === value).label,\r\n    value,\r\n  })\r\n);\r\n\r\nreturn uniqueList;\r\n"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "所有人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "RNiCDTXD_F", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "cqsyrzb", "field": "cqsyrzb", "label": "所有权占比", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputFNcgyqyaH", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsFNcgyqyaH", "name": "foucs事件", "value": ""}, {"key": "changeFNcgyqyaH", "name": "change事件", "value": "// 是否是百分数\r\nfunction isPercentage(str) {\r\n    // 定义百分数的正则表达式\r\n    const percentageRegex = /^\\d+(\\.\\d+)?%$/;\r\n\r\n    // 使用 test 方法检查字符串是否匹配正则表达式\r\n    return percentageRegex.test(str);\r\n}\r\n\r\nconst ownerInfoValue = form.formObj[\"ownerInfoBFS\"];\r\nconst cqsyfsValue = form.formObj[\"cqsyfs\"];\r\nownerInfoValue.forEach((item) => {\r\n   if (!isPercentage(item.cqsyrzb)) {\r\n          item.cqsyrzb = \"\";\r\n        }\r\n})\r\n"}, {"key": "clickFNcgyqyaH", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "产权方式选择按份共有，所有权占比只能输入百分数；", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"trigger": "blur", "validatorStr": "console.log(form.formObj[\"ownerInfoBFS\"])\r\nif (!value) {\r\n  callback(new Error('所有权占比不能为空'))\r\n} else {\r\n  const ownerInfoValue = form.formObj[\"ownerInfoBFS\"]\r\n  const arr = ownerInfoValue.find((item) => {\r\n    return item.cqsyrzb == \"\" || item.cqsyqxm === \"\"\r\n  })\r\n  if (arr) {\r\n    callback()\r\n  }\r\n  let sum = 0\r\n  ownerInfoValue.forEach((item) => {\r\n    const data = item.cqsyrzb.replace(\"%\", \"\")\r\n    sum += Number(data)\r\n  })\r\n  if (sum !== 100) {\r\n    callback(new Error('按份共有比例总和必须为100%'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n"}, {"message": "所有权占比不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}, {"child": [{"id": "xcWq0Ezxn", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "cqsyqxm", "field": "cqsyqxm", "label": "所有人姓名", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changeKAfa2PE7a", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nconst ownerInfoBFSList = form.formObj[\"ownerInfoBFS\"];\r\n\r\nlet list = [];\r\n\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno,\r\n  });\r\n  if (item.mfhyzk === \"已婚\") {\r\n    list.push({\r\n      label: item.mfpoxm,\r\n      value: item.mfpozjhm,\r\n    });\r\n  }\r\n});\r\n\r\ntry {\r\n  //   找出list中value等于ownerInfoBFSList[addIndex].cqsyqxm的值，返回符合的对象\r\n  let obj = list.find(\r\n    (item) => item.value === ownerInfoBFSList[addIndex].cqsyqxm\r\n  );\r\n  // 使用 filter 方法过滤掉满足条件的元素\r\n  list = list.filter(\r\n    (item) => !ownerInfoBFSList.some((i) => i.cqsyqxm === item.value)\r\n  );\r\n  if (obj) {\r\n    list.push(obj);\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"使用 filter 方法过滤掉满足条件的元素\");\r\n}\r\n\r\n// 去重\r\nconst uniqueList = [...new Set(list.map((item) => item.value))].map(\r\n  (value) => ({\r\n    label: list.find((item) => item.value === value).label,\r\n    value,\r\n  })\r\n);\r\n\r\nreturn uniqueList;\r\n"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "所有人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "WrXDMjJf-5", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "cqsyrzb", "field": "cqsyrzb", "label": "所有权占比", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputFNcgyqyaH", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsFNcgyqyaH", "name": "foucs事件", "value": ""}, {"key": "changeFNcgyqyaH", "name": "change事件", "value": "// 是否是百分数\r\nfunction isPercentage(str) {\r\n    // 定义百分数的正则表达式\r\n    const percentageRegex = /^\\d+(\\.\\d+)?%$/;\r\n\r\n    // 使用 test 方法检查字符串是否匹配正则表达式\r\n    return percentageRegex.test(str);\r\n}\r\n\r\nconst ownerInfoValue = form.formObj[\"ownerInfoBFS\"];\r\nconst cqsyfsValue = form.formObj[\"cqsyfs\"];\r\nownerInfoValue.forEach((item) => {\r\n   if (!isPercentage(item.cqsyrzb)) {\r\n          item.cqsyrzb = \"\";\r\n        }\r\n})\r\n"}, {"key": "clickFNcgyqyaH", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请输入", "labelDescribe": "产权方式选择按份共有，所有权占比只能输入百分数；", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"trigger": "blur", "validatorStr": "console.log(form.formObj[\"ownerInfoBFS\"])\r\nif (!value) {\r\n  callback(new Error('所有权占比不能为空'))\r\n} else {\r\n  const ownerInfoValue = form.formObj[\"ownerInfoBFS\"]\r\n  const arr = ownerInfoValue.find((item) => {\r\n    return item.cqsyrzb == \"\" || item.cqsyqxm === \"\"\r\n  })\r\n  if (arr) {\r\n    callback()\r\n  }\r\n  let sum = 0\r\n  ownerInfoValue.forEach((item) => {\r\n    const data = item.cqsyrzb.replace(\"%\", \"\")\r\n    sum += Number(data)\r\n  })\r\n  if (sum !== 100) {\r\n    callback(new Error('按份共有比例总和必须为100%'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\n"}, {"message": "所有权占比不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}]}], "props": {"key": "ownerInfoBFS", "max": 1, "min": 2, "field": "ownerInfoBFS", "title": "所有权信息", "addText": "新增", "functions": [{"key": "deleteXXRnZI6Gd", "name": "delete事件", "value": "const ownerInfoBFSForm = form.formObj[\"ownerInfoBFS\"];\r\n\r\nfunction calculateRemainingPercentage(arr) {\r\n  // 如果数组长度小于等于 1，直接返回 100%\r\n  if (arr.length <= 1) {\r\n    return \"100%\";\r\n  }\r\n\r\n  // 使用 reduce 方法计算除最后一项外的所有百分比之和\r\n  const sum = arr.slice(0, -1).reduce((acc, current) => {\r\n    const percentage = parseFloat(current.cqsyrzb.replace(\"%\", \"\"));\r\n    return acc + (isNaN(percentage) ? 0 : percentage);\r\n  }, 0);\r\n\r\n  // 计算剩余百分比并确保结果不小于 0\r\n  const remaining = Math.max(0, 100 - sum);\r\n  return `${remaining}%`;\r\n}\r\n\r\n// 打印 ownerInfoBFSForm \r\nconsole.log(ownerInfoBFSForm, \"ownerInfoBFSForm\");\r\n\r\n// 检查 ownerInfoBFSForm 是否为数组\r\nif (Array.isArray(ownerInfoBFSForm) && ownerInfoBFSForm.length > 0) {\r\n  // 更新最后一项的 cqsyrzb 属性\r\n  const lastItem = ownerInfoBFSForm[ownerInfoBFSForm.length - 1];\r\n  lastItem.cqsyrzb = calculateRemainingPercentage(ownerInfoBFSForm);\r\n}\r\n"}, {"key": "addXXRnZI6Gd", "name": "add事件", "value": "const ownerInfoBFSForm = form.formObj[\"ownerInfoBFS\"];\r\n\r\nfunction calculateRemainingPercentage(arr) {\r\n  // 如果数组长度小于等于 1，直接返回 100%\r\n  if (arr.length <= 1) {\r\n    return \"100%\";\r\n  }\r\n\r\n  // 使用 reduce 方法计算除最后一项外的所有百分比之和\r\n  const sum = arr.slice(0, -1).reduce((acc, current) => {\r\n    const percentage = parseFloat(current.cqsyrzb.replace(\"%\", \"\"));\r\n    return acc + (isNaN(percentage) ? 0 : percentage);\r\n  }, 0);\r\n\r\n  // 计算剩余百分比并确保结果不小于 0\r\n  const remaining = Math.max(0, 100 - sum);\r\n  return `${remaining}%`;\r\n}\r\n\r\n// 打印 ownerInfoBFSForm \r\nconsole.log(ownerInfoBFSForm, \"ownerInfoBFSForm\");\r\n\r\n// 检查 ownerInfoBFSForm 是否为数组\r\nif (Array.isArray(ownerInfoBFSForm) && ownerInfoBFSForm.length > 0) {\r\n  // 更新最后一项的 cqsyrzb 属性\r\n  const lastItem = ownerInfoBFSForm[ownerInfoBFSForm.length - 1];\r\n  lastItem.cqsyrzb = calculateRemainingPercentage(ownerInfoBFSForm);\r\n}\r\n"}, {"key": "limitXXRnZI6Gd", "name": "限制事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"];\r\nlet list = [];\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno,\r\n  });\r\n  if (item.mfhyzk === \"已婚\") {\r\n    list.push({\r\n      label: item.mfpoxm,\r\n      value: item.mfpozjhm,\r\n    });\r\n  }\r\n});\r\n\r\nreturn  list.length;"}], "isNeedMax": false, "isNeedMin": true, "deleteText": "删除", "innerWidth": 1, "outerWidth": 1, "defaultLine": 2, "isSelection": false, "isShowIndex": true, "showColumns": "", "isAddByDialog": false, "isShowAsTable": false, "labelDescribe": "", "isShowOutBorder": true, "innerBorderColor": "#000", "outerBorderColor": "#000", "isShowInnerBorder": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetCanAddTable"}], "props": {"show": true, "field": "estateInfo", "title": "不动产登簿信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "cRTKBHRAp4", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "xMs-AMAgS", "icon": "icon-select", "name": "多选下拉框", "child": [], "props": {"id": "5e5731936c914191917dd35c85a47ace", "key": "khlx", "field": "khlx", "label": "选择开户类型（可多选）", "default": "", "options": [{"label": "用水开户", "value": "用水开户", "disabled": false}, {"label": "用电开户", "value": "用电开户", "disabled": false}, {"label": "用气开户", "value": " 用气开户", "disabled": false}, {"label": "有线电视开户", "value": "有线电视开户", "disabled": false}], "disabled": false, "functions": [{"key": "changekhlx", "name": "change事件", "value": ""}], "optionKey": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "allowCreate": false, "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "选择开户类型不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select-multiple"}, {"id": "g1k57Tg1vk", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "xzkhr", "field": "xzkhr", "label": "选择开户人", "default": "", "options": [{"label": "选项一", "value": "1", "disabled": false}, {"label": "选项二", "value": "2", "disabled": false}], "disabled": false, "functions": [{"key": "changen42Fp_3FU", "name": "change事件", "value": "const buyer = form.formObj[\"jyhtYsfSfdsrModel\"].find((item) => item.cardno === form.formObj[\"xzkhr\"]);\r\nform.updateFormObj(\"khrxm\", buyer.name);\r\nform.updateFormObj(\"khrzjhm\", buyer.cardno);\r\nform.updateFormObj(\"khrlxfs\", buyer.tel);"}], "optionKey": "", "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"]\r\nlet list = []\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno\r\n  });\r\n})\r\nreturn list"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "labelDescribe": ""}, "rules": [{"message": "开户人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "sp2YF88A8", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "936edb196e544081b3ff68c9c061bd46", "key": "khrxm", "field": "khrxm", "label": "开户人姓名", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputkhrxm", "name": "input事件", "value": "console.log(form)"}, {"key": "foucskhrxm", "name": "foucs事件", "value": ""}, {"key": "changekhrxm", "name": "change事件", "value": ""}, {"key": "clickkhrxm", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "开户人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "nujYG0Rlq", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "dcc259aa17ce42a2b8e8034b7c042ec1", "key": "khrzjhm", "field": "khrzjhm", "label": "开户人证件号码", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "input事件", "value": "console.log(form)"}, {"key": "fou<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "foucs事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "开户人证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "TNwtVpIVA", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "572911ed0a36430ebb3ed4889fa19dfe", "key": "khrlxfs", "field": "khrlxfs", "label": "开户人联系方式", "default": "", "disabled": false, "readonly": false, "clearable": false, "functions": [{"key": "inputkhrlxfs", "name": "input事件", "value": "console.log(form)"}, {"key": "foucskhrlxfs", "name": "foucs事件", "value": ""}, {"key": "changekhrlxfs", "name": "change事件", "value": ""}, {"key": "clickkhrlxfs", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "开户人联系方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}], "props": {"show": true, "field": "passInfo", "title": "水电气视开户信息", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}, {"id": "x173opVoyN", "icon": "icon-<PERSON><PERSON><PERSON>", "name": "表单域", "type": "formArea", "child": [{"id": "qxHaIztFpt", "icon": "icon-kaiguan3", "name": "邮寄地址", "type": "addressArea", "child": [{"id": "0oqpXe0IdC", "icon": "icon-ziti", "name": "文字", "child": [], "props": {"key": "", "color": "#000000", "field": "helpTip", "label": "", "isTips": true, "isTitle": false, "fontSize": "16px", "showById": "LZFSpayment", "functions": [{"key": "click3STTKKdtR", "name": "click事件", "value": ""}], "tipsTitle": "", "tipsWidth": "", "fontWeight": 500, "isNeedHide": true, "labelWidth": "", "modelValue": "自助打印地点：太仓市政务服务中心3楼、港区行政审批局2楼、浮桥行政中心2楼、太仓市农商行上海东路198号（金融大厦）1楼大厅不动产自助服务终端", "tableWidth": "", "showByValue": "自助打印", "tipsContent": "", "tipsPosition": "right", "labelDescribe": ""}, "rules": [], "events": {}, "platform": "all", "needRules": false, "needSpecial": false, "componentName": "a-net-text"}, {"id": "ZCjgwXdtyu", "key": "", "icon": "icon-da<PERSON><PERSON><PERSON><PERSON>", "name": "单选框", "child": [], "props": {"field": "LZFSpayment", "label": "请选择不动产权证领证方式", "options": [{"label": "服务网点自取", "value": "服务网点自取", "disabled": false}, {"label": "自助打印", "value": "自助打印", "disabled": false}, {"label": "EMS邮寄", "value": "EMS邮寄", "disabled": false}], "disabled": false, "showById": "", "functions": [{"key": "changeU-HQO138H", "name": "change事件", "value": ""}], "optionKey": "", "isNeedHide": false, "labelWidth": "", "modelValue": "服务网点自取", "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "showByValue": "", "labelDescribe": ""}, "rules": [{"message": "请选择不动产权证领证方式", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-radio"}, {"id": "BaGsVKAKTz", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "xzlzr", "field": "xzlzr", "label": "选择领证人", "default": "", "options": [], "disabled": false, "showById": "LZFSpayment", "functions": [{"key": "changeMx5Ur49pE", "name": "change事件", "value": "\r\nconst buyer = form.formObj[\"jyhtYsfSfdsrModel\"].find(\r\n  (item) => item.cardno === form.formObj[\"xzlzr\"]\r\n);\r\nform.updateFormObj(\"lzrzjhm\", buyer.cardno);\r\nform.updateFormObj(\"lzrlxfs\", buyer.tel);"}], "optionKey": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"]\r\nlet list = []\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno\r\n  });\r\n})\r\nreturn list"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "showByValue": "服务网点自取", "labelDescribe": ""}, "rules": [{"message": "领证人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "MYaVASB_al", "icon": "icon-select", "name": "下拉框", "child": [], "props": {"key": "xzlzr", "field": "xzlzr", "label": "选择领证人", "default": "", "options": [], "disabled": false, "showById": "LZFSpayment", "functions": [{"key": "changeMx5Ur49pE", "name": "change事件", "value": "\r\nconst buyer = form.formObj[\"jyhtYsfSfdsrModel\"].find(\r\n  (item) => item.cardno === form.formObj[\"xzlzr\"]\r\n);\r\nform.updateFormObj(\"lzrzjhm\", buyer.cardno);\r\nform.updateFormObj(\"lzrlxfs\", buyer.tel);"}], "optionKey": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "optionFunc": [{"key": "getOption", "name": "getOption事件", "value": "const buyerList = form.formObj[\"jyhtYsfSfdsrModel\"]\r\nlet list = []\r\nbuyerList.forEach((item) => {\r\n  list.push({\r\n    label: item.name,\r\n    value: item.cardno\r\n  });\r\n})\r\nreturn list"}], "tableWidth": "", "labelHeight": "", "placeholder": "请选择", "showByValue": "自助打印", "labelDescribe": ""}, "rules": [{"message": "领证人不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-select"}, {"id": "4u2pwj2qp", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "046f60e3e94948f6b50bf79ed3b597e2", "key": "lzrlxfs", "field": "lzrlxfs", "label": "领证人联系方式", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "inputlzrlxfs", "name": "input事件", "value": "console.log(form)"}, {"key": "foucslzrlxfs", "name": "foucs事件", "value": ""}, {"key": "changelzrlxfs", "name": "change事件", "value": ""}, {"key": "clicklzrlxfs", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "服务网点自取", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "领证人联系方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "1nKqPCmP1", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "add9611f96024c328210683b0b8623a9", "key": "lzrzjhm", "field": "lzrzjhm", "label": "领证人证件号码", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "input事件", "value": "console.log(form)"}, {"key": "foucslzrz<PERSON>hm", "name": "foucs事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "服务网点自取", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "领证人证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "Bat4j_LqBn", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "046f60e3e94948f6b50bf79ed3b597e2", "key": "lzrlxfs", "field": "lzrlxfs", "label": "领证人联系方式", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "inputlzrlxfs", "name": "input事件", "value": "console.log(form)"}, {"key": "foucslzrlxfs", "name": "foucs事件", "value": ""}, {"key": "changelzrlxfs", "name": "change事件", "value": ""}, {"key": "clicklzrlxfs", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "自助打印", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "领证人联系方式不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "yXKsBSRD9E", "icon": "icon-input", "name": "输入框", "child": [], "props": {"id": "add9611f96024c328210683b0b8623a9", "key": "lzrzjhm", "field": "lzrzjhm", "label": "领证人证件号码", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "input事件", "value": "console.log(form)"}, {"key": "foucslzrz<PERSON>hm", "name": "foucs事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "change事件", "value": ""}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "自助打印", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "2,2"}, "rules": [{"message": "领证人证件号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input"}, {"id": "hOeWArtsB1", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "getCertificateTypeTname", "field": "getCertificateTypeTname", "label": "收件人姓名", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "inputd36ZfeMrTname", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsd36ZfeMrTname", "name": "foucs事件", "value": ""}, {"key": "changed36ZfeMrTname", "name": "change事件", "value": ""}, {"key": "clickd36ZfeMrTname", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "EMS邮寄", "labelDescribe": "", "listInterface": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "titleButtonText": "从地址簿导入", "isDesensitization": ""}, "rules": [{"message": "收件人姓名不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-button-title"}, {"id": "FBfSVyjLnd", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "getCertificateTypeTmobile", "field": "getCertificateTypeTmobile", "label": "收件人手机号码", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "inputd36ZfeMrTmobile", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsd36ZfeMrTmobile", "name": "foucs事件", "value": ""}, {"key": "changed36ZfeMrTmobile", "name": "change事件", "value": ""}, {"key": "clickd36ZfeMrTmobile", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "EMS邮寄", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": "1,2"}, "rules": [{"message": "收件人手机号码不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-input-number"}, {"id": "ubkLDzD3dW", "icon": "icon-input", "name": "输入框", "child": [], "props": {"key": "getCertificateTypeTaddress", "field": "getCertificateTypeTaddress", "label": "收件地址", "default": "", "disabled": false, "readonly": false, "showById": "LZFSpayment", "clearable": false, "functions": [{"key": "inputd36ZfeMrTaddress", "name": "input事件", "value": "console.log(form)"}, {"key": "foucsd36ZfeMrTaddress", "name": "foucs事件", "value": ""}, {"key": "changed36ZfeMrTaddress", "name": "change事件", "value": ""}, {"key": "clickd36ZfeMrTaddress", "name": "click事件", "value": ""}], "maxlength": 50, "buttonText": "", "isNeedHide": true, "labelWidth": "", "modelValue": "", "tableWidth": "", "isShowLimit": false, "labelHeight": "", "placeholder": "请在此处输入内容", "showByValue": "EMS邮寄", "labelDescribe": "", "showAppendBtn": false, "appendBtnColor": "#2c8ef1", "isDesensitization": ""}, "rules": [{"message": "收件地址不能为空", "trigger": "blur", "required": true}], "events": {}, "platform": "all", "needRules": true, "needSpecial": false, "componentName": "a-net-detail-address"}], "props": {"show": true, "field": "d36ZfeMrT", "isHidden": false, "functions": [], "listInterface": "", "saveInterface": ""}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetMailingAddress"}], "props": {"show": true, "field": "getCertificateType", "title": "领证方式", "bgColor": "#dd4b39", "barColor": "", "isHidden": false, "functions": [], "titleSize": 22, "arrowColor": "#000000", "titleColor": "#0E0D0D", "isShowButton": true}, "rules": [], "events": {}, "platform": "all", "needSpecial": false, "componentName": "ANetFormArea"}], "formAttribute": {"inline": false, "disabled": false, "tableName": "SSJTXXName", "labelWidth": "100px", "labelPosition": "right"}, "beforeFunction": "try {\r\n  if (!sessionStorage.getItem(\"oneHouseDict\")) {\r\n    axios\r\n      .get(\r\n        \"https://zwfw.taicang.gov.cn/gateway-api/bdc-ai/app/incremental/business/operate/getAllSelectData\",\r\n        {}\r\n      )\r\n      .then(function (response) {\r\n        console.log(response);\r\n        const dictData = response.data.data;\r\n        //   遍历dictData\r\n        for (const key in dictData) {\r\n          const ele = dictData[key];\r\n          ele.forEach((item) => {\r\n            item.label = item.value;\r\n          });\r\n        }\r\n        sessionStorage.setItem(\"oneHouseDict\", JSON.stringify(dictData));\r\n      })\r\n      .catch(function (error) {\r\n        console.log(error);\r\n      });\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"获取字典\");\r\n}\r\n\r\n// 自定义匹配字符串数组\r\nconst matchStrings = [\"镇\", \"街道\", \"港区\", \"科教新城\"];\r\n\r\nfunction extractBeforeMatch(strs, matches) {\r\n  if (!strs) return \"\";\r\n  const str = strs.slice(0, 6);\r\n  for (const match of matches) {\r\n    const nums = match.length;\r\n    const index = str.indexOf(match);\r\n    if (index !== -1) {\r\n      return str.slice(0, index + nums);\r\n    }\r\n  }\r\n  return str;\r\n}\r\ntry {\r\n  const addressValue = form?.formObj?.[\"address\"];\r\n  const addressStr = extractBeforeMatch(addressValue, matchStrings);\r\n  const houseUse = form?.formObj?.[\"houseUse\"];\r\n  const loanTypeName1Value = form?.formObj?.[\"loanTypeName1\"];\r\n\r\n  if (form.formObj.hasOwnProperty(\"loanTypeName\")) {\r\n    form.updateFormObj(\"loanTypeName\", loanTypeName1Value);\r\n  }\r\n  if (form.formObj.hasOwnProperty(\"area\")) {\r\n    form.updateFormObj(\"area\", addressStr);\r\n  }\r\n\r\n  if (\r\n    houseUse === \"成套住宅\" &&\r\n    form.formObj.hasOwnProperty(\"qszydx\") &&\r\n    form.formObj.hasOwnProperty(\"qszyyt\")\r\n  ) {\r\n    form.updateFormObj(\"qszydx\", \"商品住房\");\r\n    form.updateFormObj(\"qszyyt\", \"居住\");\r\n  } else if (\r\n    houseUse !== \"成套住宅\" &&\r\n    form.formObj.hasOwnProperty(\"qszydx\") &&\r\n    form.formObj.hasOwnProperty(\"qszyyt\")\r\n  ) {\r\n    form.updateFormObj(\"qszydx\", \"非住房\");\r\n    form.updateFormObj(\"qszyyt\", \"其他\");\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"初始赋值\");\r\n}\r\n\r\nfunction myUpdateFormVisible(arr, visible) {\r\n  form.updateFormVisible(arr, visible);\r\n  return;\r\n  console.log(arr, \"arr\");\r\n  let keyArr = [];\r\n  let valueArr = [];\r\n  arr.forEach((item, index) => {\r\n    const myArr = item.split(\".\");\r\n    // 单层字段\r\n    if (myArr.length === 1 && form.formObj[myArr[0]]) {\r\n      keyArr.push(myArr[0]);\r\n      valueArr.push(visible[index]);\r\n    } else {\r\n      // 多层\r\n      if (form.formObj[myArr[0]] && form.formObj[myArr[0]][myArr[1]]) {\r\n        keyArr.push(item);\r\n        valueArr.push(visible[index]);\r\n      }\r\n    }\r\n  });\r\n  if (keyArr.length > 0) {\r\n    form.updateFormVisible(keyArr, valueArr);\r\n  }\r\n}\r\n\r\ntry {\r\n  // 所属家庭\r\n  const state2 = form?.formObj?.[\"jtcy2\"];\r\n  const state3 = form?.formObj?.[\"jtcy3\"];\r\n  const state4 = form?.formObj?.[\"jtcy4\"];\r\n  const state5 = form?.formObj?.[\"jtcy5\"];\r\n\r\n  myUpdateFormVisible(\r\n    [\"jtcy1\", \"jtcy2\", \"jtcy3\", \"jtcy4\", \"jtcy5\"],\r\n    [\r\n      true,\r\n      // 检查 state2 既不为空字符串，数组也不为空\r\n      state2 !== \"\" && (!Array.isArray(state2) || state2.length > 0),\r\n      // 检查 state3 既不为空字符串，数组也不为空\r\n      state3 !== \"\" && (!Array.isArray(state3) || state3.length > 0),\r\n      // 检查 state4 既不为空字符串，数组也不为空\r\n      state4 !== \"\" && (!Array.isArray(state4) || state4.length > 0),\r\n      // 检查 state5 既不为空字符串，数组也不为空\r\n      state5 !== \"\" && (!Array.isArray(state5) || state5.length > 0)\r\n    ]\r\n  );\r\n} catch (error) {\r\n  console.log(error, \"所属家庭\");\r\n}\r\ntry {\r\n  if (form.formObj[\"loanTypeName\"]) {\r\n    if (form.formObj[\"loanTypeName\"] === \"贷款\") {\r\n      if (form.formObj[\"dkqk\"] === \"全款付清\") {\r\n        form.updateFormVisible([\"dkfs\", \"loanBank\"], [false, false]);\r\n      } else if (\r\n        form.formObj[\"dkqk\"] === \"贷款已还清\" ||\r\n        form.formObj[\"dkqk\"] === \"未还清\"\r\n      ) {\r\n        form.updateFormVisible([\"dkfs\", \"loanBank\"], [true, true]);\r\n      } else {\r\n        form.updateFormVisible(\r\n          [\"dkqk\", \"dkfs\", \"loanBank\"],\r\n          [true, false, false]\r\n        );\r\n      }\r\n    } else {\r\n      form.updateFormVisible(\r\n        [\"dkqk\", \"dkfs\", \"loanBank\"],\r\n        [false, false, false]\r\n      );\r\n    }\r\n  } else {\r\n    form.updateFormVisible([\"dkqk\", \"dkfs\", \"loanBank\"], [false, false, false]);\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"贷款\");\r\n}\r\n// 买方协查人\r\ntry {\r\n  const buyerInvestsForm = form.formObj[\"buyerInvests\"];\r\n  if (buyerInvestsForm) {\r\n    buyerInvestsForm.forEach((item, index) => {\r\n      let arr = [];\r\n      arr.push(\"buyerInvests.\" + index + \".mfxcrsjh\");\r\n      if (item.mfxcrcygx === \"子女\") {\r\n        form.updateFormVisible(arr, [false]);\r\n      } else {\r\n        form.updateFormVisible(arr, [true]);\r\n      }\r\n    });\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"error\");\r\n}\r\n\r\n// 身份证控制代理人\r\nconst buyerForm = form?.formObj?.[\"jyhtYsfSfdsrModel\"];\r\nfunction isMinor(idCard) {\r\n  // 校验身份证号码是否为 18 位\r\n  if (!/^\\d{17}[\\dXx]$/.test(idCard)) {\r\n    return false;\r\n  }\r\n  // 提取出生年月日\r\n  const birthDateStr = idCard.slice(6, 14);\r\n  const birthDate = new Date(\r\n    `${birthDateStr.slice(0, 4)}-${birthDateStr.slice(\r\n      4,\r\n      6\r\n    )}-${birthDateStr.slice(6)}`\r\n  );\r\n  const currentDate = new Date();\r\n  const age = currentDate.getFullYear() - birthDate.getFullYear();\r\n  const monthDiff = currentDate.getMonth() - birthDate.getMonth();\r\n  if (\r\n    monthDiff < 0 ||\r\n    (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())\r\n  ) {\r\n    // 如果生日还未到，年龄减 1\r\n    return age - 1 < 18;\r\n  }\r\n  return age < 18;\r\n}\r\nif (buyerForm) {\r\n  buyerForm.forEach((item, index) => {\r\n    // 隐藏全部的买方所属家庭\r\n    try {\r\n      const shjtStr = `jyhtYsfSfdsrModel.${index}.mfssjt`;\r\n      myUpdateFormVisible([shjtStr], [false]);\r\n    } catch (error) {\r\n      console.log(error, \"隐藏所属家庭\");\r\n    }\r\n\r\n    //  校验未成年，隐藏代理人信息\r\n    if (item.cardtype === \"居民身份证\" && isMinor(item.cardno)) {\r\n      try {\r\n        myUpdateFormVisible(\r\n          [\r\n            `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n            `jyhtYsfSfdsrModel.${index}.sfzmcl`,\r\n          ],\r\n          [true, true, true, true, true, true, true]\r\n        );\r\n      } catch (error) {\r\n        console.log(error, \"隐藏代理人信息\");\r\n      }\r\n    } else {\r\n      try {\r\n        myUpdateFormVisible(\r\n          [\r\n            `jyhtYsfSfdsrModel.${index}.sfxydlr`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrxm`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrzjlx`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrzjhm`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrsjh`,\r\n            `jyhtYsfSfdsrModel.${index}.dlrlx`,\r\n            `jyhtYsfSfdsrModel.${index}.sfzmcl`,\r\n          ],\r\n          [false, false, false, false, false, false, false]\r\n        );\r\n      } catch (error) {\r\n        console.log(error, \"隐藏代理人信息\");\r\n      }\r\n    }\r\n    // 婚姻状态初始化\r\n    const arr = [\r\n      `jyhtYsfSfdsrModel.${index}.mfhyzk`,\r\n      `jyhtYsfSfdsrModel.${index}.mfpoxm`,\r\n      `jyhtYsfSfdsrModel.${index}.mfpozjlx`,\r\n      `jyhtYsfSfdsrModel.${index}.mfpozjhm`,\r\n      `jyhtYsfSfdsrModel.${index}.mpposjh`,\r\n      `jyhtYsfSfdsrModel.${index}.lyrq`,\r\n      `jyhtYsfSfdsrModel.${index}.sorq`,\r\n    ];\r\n    try {\r\n      if (item.mfhyzk === \"未婚\") {\r\n        // 未婚\r\n        form.updateFormVisible(arr, [\r\n          true,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n        ]);\r\n      } else if (item.mfhyzk === \"已婚\") {\r\n        // 已婚\r\n        form.updateFormVisible(arr, [\r\n          true,\r\n          true,\r\n          true,\r\n          true,\r\n          true,\r\n          false,\r\n          false,\r\n        ]);\r\n      } else if (item.mfhyzk === \"离异\") {\r\n        // 离异\r\n        form.updateFormVisible(arr, [\r\n          true,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          true,\r\n          false,\r\n        ]);\r\n      } else if (item.mfhyzk === \"丧偶\") {\r\n        // 丧偶\r\n        form.updateFormVisible(arr, [\r\n          true,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          true,\r\n        ]);\r\n      } else {\r\n        form.updateFormVisible(arr, [\r\n          true,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n        ]);\r\n      }\r\n    } catch (error) {\r\n      console.log(error, \"隐藏婚姻状态\");\r\n    }\r\n    try {\r\n      if (item.mfsfxytjgyr && item.mfsfxytjgyr === \"是\") {\r\n        myUpdateFormVisible(arr, [true, true, true, true, true, false, false]);\r\n        const mfhyzkStr = `jyhtYsfSfdsrModel.${index}.mfhyzk`;\r\n        if (form.updateFormObj) {\r\n          form.updateFormObj(mfhyzkStr, \"已婚\");\r\n        }\r\n      } else if (item.mfsfxytjgyr && item.mfsfxytjgyr === \"否\") {\r\n        const mfhyzkStr = `jyhtYsfSfdsrModel.${index}.mfhyzk`;\r\n        if (form.updateFormObj) {\r\n          form.updateFormObj(mfhyzkStr, \"\");\r\n        }\r\n        myUpdateFormVisible(arr, [\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n          false,\r\n        ]);\r\n      }\r\n    } catch (error) {\r\n      console.log(error, \"隐藏婚姻状态\");\r\n    }\r\n  });\r\n}\r\n// 协查人隐藏所属家庭\r\nconst buyerInvestsList = form?.formObj?.[\"buyerInvests\"];\r\ntry {\r\n  if (buyerInvestsList) {\r\n    buyerInvestsList.forEach((item, index) => {\r\n      const field = \"buyerInvests.\" + index + \".mfxcrssjt\";\r\n      myUpdateFormVisible([field], [false]);\r\n    });\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"协查人隐藏所属家庭\");\r\n}\r\n\r\n// 缴税方式\r\nconst jffsForm = form?.formObj?.[\"jsfs\"];\r\ntry {\r\n  if (jffsForm) {\r\n    if (jffsForm === \"线上缴税\") {\r\n      myUpdateFormVisible(\r\n        [\"xzqh\", \"fwcx\", \"sfcqyh\", \"qszydx\", \"qszyyt\", \"qszyfs\", \"area\"],\r\n        [true, true, true, true, true, true, true]\r\n      );\r\n    } else {\r\n      myUpdateFormVisible(\r\n        [\"xzqh\", \"fwcx\", \"sfcqyh\", \"qszydx\", \"qszyyt\", \"qszyfs\", \"area\"],\r\n        [false, false, false, false, false, false, false]\r\n      );\r\n    }\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"隐藏缴税方式\");\r\n}\r\n\r\n// 产权方式\r\ntry {\r\n  const cqsyfsValue = form?.formObj?.[\"cqsyfs\"];\r\n  const ownerInfoFSForm = form?.formObj?.[\"ownerInfoFS\"];\r\n  const ownerInfoBFSForm = form?.formObj?.[\"ownerInfoBFS\"];\r\n  if (form && form.updateFormVisible) {\r\n    if (cqsyfsValue) {\r\n      if (cqsyfsValue === \"单独所有\") {\r\n        myUpdateFormVisible(\r\n          [\"cqsyqxm\", \"cqsyrzb\", \"cqsyqxmFS\", \"ownerInfoBFSSelf\"],\r\n          [true, true, false, false]\r\n        );\r\n      } else if (cqsyfsValue === \"共同共有\") {\r\n        myUpdateFormVisible(\r\n          [\"cqsyqxm\", \"cqsyrzb\", \"cqsyqxmFS\", \"ownerInfoBFSSelf\"],\r\n          [false, false, true, false]\r\n        );\r\n      } else if (cqsyfsValue === \"按份共有\") {\r\n        myUpdateFormVisible(\r\n          [\"cqsyqxm\", \"cqsyrzb\", \"cqsyqxmFS\", \"ownerInfoBFSSelf\"],\r\n          [false, false, false, true]\r\n        );\r\n      }\r\n    } else {\r\n      myUpdateFormVisible(\r\n        [\"cqsyqxm\", \"cqsyrzb\", \"cqsyqxmFS\", \"ownerInfoBFSSelf\"],\r\n        [false, false, false, false]\r\n      );\r\n    }\r\n  }\r\n} catch (error) {\r\n  console.log(error, \"隐藏产权方式\");\r\n}\r\n", "submitFunction": "{}"}