package com.workplat.push.service;

import com.workplat.accept.business.chat.dto.InstancePushLogDTO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.gss.common.core.service.BaseService;
import com.workplat.push.model.InstancePushLogQuery;
import com.workplat.push.vo.InstancePushLogVO;
import org.springframework.data.domain.Page;


public interface InstancePushLogService extends BaseService<InstancePushLog> {

    Page<InstancePushLogVO> queryForPage(InstancePushLogDTO dto, PageableDTO pageable);
}
