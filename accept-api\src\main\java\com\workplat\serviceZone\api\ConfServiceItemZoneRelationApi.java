package com.workplat.serviceZone.api;

import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.serviceZone.dto.ConfServiceItemZoneRelationDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.vo.ConfServiceItemZoneRelationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单与专区关联关系API接口
 */
@Tag(name = "服务清单专区关联关系", description = "服务清单与专区多对多关联关系管理接口")
@RequestMapping("/api/serviceItemZoneRelation")
public interface ConfServiceItemZoneRelationApi {

    @Operation(summary = "根据专区ID查询关联的服务清单")
    @GetMapping("/getByZoneId")
    ResponseData<List<ConfServiceItemZoneRelationVO>> getByServiceZoneId(
            @Parameter(description = "专区ID") String serviceZoneId);

    @Operation(summary = "根据服务清单ID查询关联的专区")
    @GetMapping("/getByItemId")
    ResponseData<List<ConfServiceItemZoneRelationVO>> getByServiceItemId(
            @Parameter(description = "服务清单ID") String serviceItemId);

    @Operation(summary = "批量保存服务清单与专区的关联关系")
    @PostMapping("/saveItemRelations")
    ResponseData<Void> saveServiceItemZoneRelations(@RequestBody ConfZoneServiceItemRelationsDTO relationsDTO);

    @Operation(summary = "批量保存专区与服务清单的关联关系")
    @PostMapping("/saveZoneRelations")
    ResponseData<Void> saveZoneServiceItemRelations(@RequestBody ConfZoneServiceItemRelationsDTO relationsDTO);

    @Operation(summary = "更新关联关系排序")
    @PostMapping("/updateSort")
    ResponseData<Void> updateSort(
            @Parameter(description = "关联关系ID") @RequestParam String id,
            @Parameter(description = "排序值") @RequestParam Integer sort);

    @Operation(summary = "批量更新关联关系排序")
    @PostMapping("/batchUpdateSort")
    ResponseData<Void> batchUpdateSort(@RequestBody List<ConfServiceItemZoneRelationDTO> relations);

    @Operation(summary = "删除关联关系")
    @PostMapping("/deleteById")
    ResponseData<Void> deleteById(@Parameter(description = "关联关系ID") @RequestParam String id);

    @Operation(summary = "删除专区下的所有关联关系")
    @PostMapping("/deleteByZoneId")
    ResponseData<Void> deleteByServiceZoneId(@Parameter(description = "专区ID") @RequestParam String serviceZoneId);

    @Operation(summary = "删除服务清单的所有关联关系")
    @PostMapping("/deleteByItemId")
    ResponseData<Void> deleteByServiceItemId(@Parameter(description = "服务清单ID") @RequestParam String serviceItemId);
}