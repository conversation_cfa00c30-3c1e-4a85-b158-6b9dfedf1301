package com.workplat.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.*;
import java.util.function.Consumer;

/**
 * 表单字段筛选工具类
 * 提供根据字段映射筛选和移除表单项的功能
 *
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class FormFieldFilterUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 从字段映射中提取所有字段名
     *
     * @param fieldMapNode 字段映射节点
     * @return 字段名集合
     */
    public static Set<String> getFieldNamesFromMap(JsonNode fieldMapNode) {
        Set<String> fieldNames = new HashSet<>();

        Iterator<Map.Entry<String, JsonNode>> fields = fieldMapNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            // 如果field是对象数组，则递归处理
            if (field.getValue().isArray()) {
                for (JsonNode element : field.getValue()) {
                    fieldNames.addAll(getFieldNamesFromMap(element));
                }
            } else if (field.getValue().isObject()) {
                fieldNames.addAll(getFieldNamesFromMap(field.getValue()));
            } else if (field.getValue().isTextual()) {
                fieldNames.add(field.getKey());
            }
        }

        return fieldNames;
    }

    /**
     * 递归筛选表单项
     *
     * @param item       表单项节点
     * @param fieldNames 字段名集合
     * @param isKeepMode true为保留模式，false为移除模式
     * @return 筛选后的表单项，如果应该被过滤掉则返回null
     */
    private static JsonNode filterFormItem(JsonNode item, Set<String> fieldNames, boolean isKeepMode) {
        if (item == null || !item.isObject()) {
            return item;
        }

        ObjectNode itemCopy = item.deepCopy();

        // 检查组件类型
        JsonNode typeNode = item.get("type");
        // 可新增表单
        boolean isAddTable = typeNode != null && "addTable".equals(typeNode.asText());
        // 表单域
        boolean isFormArea = typeNode != null && "formArea".equals(typeNode.asText());
        // 人脸识别
        boolean isFaceRecognition = typeNode != null && "faceRecognition".equals(typeNode.asText());

        if (isFaceRecognition){
            // 直接返回，不做处理了
            return itemCopy;
        }

        if (isAddTable) {
            // 可新增表单的处理逻辑
            if (isKeepMode) {
                // 保留模式：可新增表单不保留
                return null;
            } else {
                // 移除模式：可新增表单不筛选，返回整个
                return itemCopy;
            }
        }

        if (isFormArea) {
            // 表单域始终保留，不管其field属性
            // 继续处理子项
        } else {
            // 获取当前项的field（仅对非表单域组件）
            JsonNode fieldNode = item.get("props");
            String fieldName = null;
            if (fieldNode != null && fieldNode.get("field") != null) {
                fieldName = fieldNode.get("field").asText();
            }

            // 根据模式判断是否保留当前项
            boolean shouldKeep = isShouldKeep(fieldNames, isKeepMode, fieldName);

            if (!shouldKeep) {
                return null;
            }
        }

        // 递归处理子项
        JsonNode childNode = item.get("child");
        if (childNode != null && childNode.isArray()) {
            ArrayNode filteredChildren = objectMapper.createArrayNode();

            for (JsonNode child : childNode) {
                JsonNode filteredChild = filterFormItem(child, fieldNames, isKeepMode);
                if (filteredChild != null) {
                    filteredChildren.add(filteredChild);
                }
            }

            itemCopy.set("child", filteredChildren);
        }

        return itemCopy;
    }

    /**
     * 判断当前项是否应该被保留
     *
     * @param fieldNames 字段名集合
     * @param isKeepMode true为保留模式，false为移除模式
     * @param fieldName  当前项的字段名
     * @return 是否应该被保留
     */
    private static boolean isShouldKeep(Set<String> fieldNames, boolean isKeepMode, String fieldName) {
        boolean shouldKeep;
        if (isKeepMode) {
            // 保留模式：如果没有字段名，保留；如果有字段名，只保留在fieldNames中的字段
            if (fieldName == null) {
                shouldKeep = true; // 没有字段名的组件保留
            } else {
                shouldKeep = fieldNames.contains(fieldName); // 只保留映射中存在的字段
            }
        } else {
            // 移除模式：如果没有字段名，保留；如果有字段名，移除在fieldNames中的字段
            if (fieldName == null) {
                shouldKeep = true; // 没有字段名的组件保留
            } else {
                shouldKeep = !fieldNames.contains(fieldName); // 移除映射中存在的字段
            }
        }
        return shouldKeep;
    }

    /**
     * 表单处理结果类
     */
    public static class FormFilterResult {
        private final String filteredFormJson;
        private final List<Integer> removedFormAreaIndexes;
        private final List<String> removedFormAreaFields;

        public FormFilterResult(String filteredFormJson, List<Integer> removedFormAreaIndexes, List<String> removedFormAreaFields) {
            this.filteredFormJson = filteredFormJson;
            this.removedFormAreaIndexes = removedFormAreaIndexes;
            this.removedFormAreaFields = removedFormAreaFields;
        }

        public String getFilteredFormJson() {
            return filteredFormJson;
        }

        public List<Integer> getRemovedFormAreaIndexes() {
            return removedFormAreaIndexes;
        }

        public List<String> getRemovedFormAreaFields() {
            return removedFormAreaFields;
        }

        @Override
        public String toString() {
            return "FormFilterResult{" +
                    "removedFormAreaIndexes=" + removedFormAreaIndexes +
                    ", removedFormAreaFields=" + removedFormAreaFields +
                    '}';
        }
    }

    /**
     * 增强版表单字段筛选：自动移除空表单域并提供索引信息
     *
     * @param formMetadata 表单元数据JSON字符串
     * @param fieldMapData 字段映射数据JSON字符串
     * @param isKeepMode true为保留模式，false为移除模式
     * @return FormFilterResult 包含处理结果和被移除的表单域信息
     * @throws Exception 处理异常
     */
    public static FormFilterResult filterFormWithIndexTracking(String formMetadata, String fieldMapData, boolean isKeepMode) throws Exception {
        JsonNode formNode = objectMapper.readTree(formMetadata);
        JsonNode fieldMapNode = objectMapper.readTree(fieldMapData);

        // 获取字段映射中的所有字段名
        Set<String> fieldNames = getFieldNamesFromMap(fieldMapNode);

        // 创建新的表单节点
        ObjectNode filteredForm = formNode.deepCopy();

        // 用于记录被移除的表单域信息
        List<Integer> removedFormAreaIndexes = new ArrayList<>();
        List<String> removedFormAreaFields = new ArrayList<>();

        // 处理renderList中的表单项
        JsonNode renderList = filteredForm.get("renderList");
        if (renderList != null && renderList.isArray()) {
            ArrayNode filteredRenderList = objectMapper.createArrayNode();

            for (int i = 0; i < renderList.size(); i++) {
                JsonNode item = renderList.get(i);
                JsonNode filteredItem = filterFormItem(item, fieldNames, isKeepMode);

                // 检查是否为表单域且是否为空
                if (filteredItem != null) {
                    boolean isFormArea = isFormAreaType(filteredItem);
                    boolean isEmpty = isFormArea && isFormAreaEmpty(filteredItem);

                    if (isEmpty) {
                        // 记录被移除的空表单域信息
                        removedFormAreaIndexes.add(i);
                        String formAreaField = getFormAreaField(item);
                        if (formAreaField != null) {
                            removedFormAreaFields.add(formAreaField);
                        }
                    } else {
                        filteredRenderList.add(filteredItem);
                    }
                } else {
                    // 记录被完全过滤掉的项
                    if (isFormAreaType(item)) {
                        removedFormAreaIndexes.add(i);
                        String formAreaField = getFormAreaField(item);
                        if (formAreaField != null) {
                            removedFormAreaFields.add(formAreaField);
                        }
                    }
                }
            }

            ((ObjectNode) filteredForm).set("renderList", filteredRenderList);
        }

        String resultJson = objectMapper.writeValueAsString(filteredForm);
        return new FormFilterResult(resultJson, removedFormAreaIndexes, removedFormAreaFields);
    }

    /**
     * 检查是否为表单域类型
     */
    private static boolean isFormAreaType(JsonNode item) {
        if (item == null || !item.isObject()) {
            return false;
        }
        JsonNode typeNode = item.get("type");
        return typeNode != null && "formArea".equals(typeNode.asText());
    }

    /**
     * 检查表单域是否为空（没有子项或子项为空数组）
     */
    private static boolean isFormAreaEmpty(JsonNode formArea) {
        JsonNode childNode = formArea.get("child");
        return childNode == null || !childNode.isArray() || childNode.isEmpty();
    }

    /**
     * 获取表单域的field值
     */
    private static String getFormAreaField(JsonNode formArea) {
        JsonNode propsNode = formArea.get("props");
        if (propsNode != null && propsNode.has("field")) {
            return propsNode.get("field").asText();
        }
        return null;
    }

    /**
     * 检查组件及其子组件中是否有任何字段在映射数据中
     * @param item 组件节点
     * @param fieldNames 字段名集合
     * @return 是否有匹配的字段
     */
    private static boolean hasAnyFieldInMapping(JsonNode item, Set<String> fieldNames) {
        if (item == null || !item.isObject()) {
            return false;
        }

        // 检查当前组件的field
        JsonNode propsNode = item.get("props");
        if (propsNode != null && propsNode.has("field")) {
            String fieldName = propsNode.get("field").asText();
            if (fieldName != null && fieldNames.contains(fieldName)) {
                return true;
            }
        }

        // 递归检查子组件
//        JsonNode childNode = item.get("child");
//        if (childNode != null && childNode.isArray()) {
//            for (JsonNode child : childNode) {
//                if (hasAnyFieldInMapping(child, fieldNames)) {
//                    return true;
//                }
//            }
//        }

        return false;
    }
}
