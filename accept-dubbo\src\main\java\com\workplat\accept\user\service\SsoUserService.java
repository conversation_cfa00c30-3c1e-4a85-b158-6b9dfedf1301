package com.workplat.accept.user.service;

import com.workplat.accept.user.entity.SsoUser;

/**
 * @Author: Odin
 * @Date: 2024/9/23 11:14
 * @Description:
 */
public interface SsoUserService {

    // 调用RMC 根据userId获取user
    SsoUser getUserInfoByUserId(String userId);

    // 查询系统内UserId
    SsoUser loadByUserId(String userId);

    // 保存
    void saveUser(SsoUser ssoUser);

    String getANetToken(String name, String idCard);
}
