package com.workplat.serviceZone.service;

import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.gss.common.core.service.BaseService;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置Service接口
 */
public interface ConfServiceZoneService extends BaseService<ConfServiceZone> {

    /**
     * 根据名称查询专区
     * @param name 专区名称
     * @return 专区信息
     */
    ConfServiceZone getByName(String name);

    /**
     * 启用/禁用专区
     * @param id 专区ID
     * @param enabled 是否启用
     */
    void updateEnabled(String id, Boolean enabled);

    /**
     * 批量保存专区与服务清单的关联关系
     * @param serviceZoneId 专区ID
     * @param serviceItemIds 服务清单ID列表
     */
    void saveZoneServiceItemRelations(String serviceZoneId, List<String> serviceItemIds);
}
