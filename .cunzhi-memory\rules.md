# 开发规范和规则

- 全局事务问题修复：1. 修复GaDeclarePushService中SQL语法错误(now1()改为now())；2. 为GA数据源配置独立的事务管理器gaTransactionManager；3. 在GaDeclarePushService.push()方法中指定使用gaTransactionManager确保事务正确回滚
- Spring定时任务重复执行问题：使用@Scheduled注解时必须在启动类添加@EnableScheduling注解，否则可能导致定时任务重复执行。缺少@EnableScheduling会导致调度器初始化不完整，造成执行上下文不稳定和事务边界不清晰，最终引起同一任务被重复调用。
- 组件引擎优先级策略：使用@Order注解控制组件选择优先级，特定业务组件(1-20)优先于默认实现组件(80-99)优先于抽象基类(100+)。已修复充电桩情形选择、申报须知、材料分类组件的选择冲突问题。
- AI政务智能中枢平台开发规范文档已创建，包含：1.项目三层架构(biz/api/dubbo)模块划分和依赖关系；2.参数传输规范(Controller层@RequestBody/@RequestParam、Service层参数限制、Repository层MapUtil查询)；3.DTO规范(命名XxxDTO、@Schema/@NotBlank注解、BeanUtil转换)；4.VO规范(命名XxxVO、@Dict字典转换、数据展示用途)；5.模块职责划分(biz业务实现、api接口定义、dubbo服务层)；6.代码组织结构(包结构标准化、类命名规则、配置文件组织)；7.Service/Repository/Controller层实现规范；8.异常处理和事务管理(@Transactional、@Idempotent)；9.常量枚举定义；10.数据转换映射；11.数据库表命名规范(biz_/sys_/conf_/cus_前缀)。文档位置：docs/开发规范文档.md
- 开发规范文档已补充BaseQuery查询条件构建规范：1.推荐使用BaseQuery替代MapUtil构建复杂查询条件；2.BaseQuery继承方式，使用@QueryMapBuild注解定义查询字段；3.支持QueryOperator操作符(EQ/LIKE/IN/GT/LT等)；4.支持fieldName指定关联查询字段；5.BaseQuery提供类型安全、代码可读性、重用性、维护性优势；6.简单查询可继续使用MapUtil，复杂查询推荐BaseQuery；7.实际项目示例：InstancePushLogQuery类的实现和使用方式。
