package com.workplat.serviceZone.converter;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.serviceZone.dto.ConfServiceItemDTO;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import com.workplat.serviceZone.vo.ConfServiceItemVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置转换器
 */
@Component
public class ConfServiceItemConverter {

    @Autowired
    private ConfServiceItemZoneRelationService relationService;

    /**
     * Entity转VO
     */
    public ConfServiceItemVO convert(ConfServiceItem entity) {
        if (entity == null) {
            return null;
        }
        ConfServiceItemVO vo = new ConfServiceItemVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 通过关联服务查询专区信息，避免懒加载问题
        try {
            List<ConfServiceItemZoneRelation> relations = relationService.getByServiceItemIdWithDetails(entity.getId());
            if (relations != null && !relations.isEmpty()) {
                List<String> serviceZoneIds = relations.stream()
                        .map(relation -> relation.getServiceZone().getId())
                        .collect(Collectors.toList());
                vo.setServiceZoneIds(serviceZoneIds);
            }
        } catch (Exception e) {
            // 如果查询关联关系失败，不影响主要数据的返回
            // 可以记录日志，但不抛出异常
        }
        
        return vo;
    }

    /**
     * Entity转VO（带已知的关联关系，避免额外查询）
     */
    public ConfServiceItemVO convert(ConfServiceItem entity, List<ConfServiceItemZoneRelation> relations) {
        if (entity == null) {
            return null;
        }
        ConfServiceItemVO vo = new ConfServiceItemVO();
        BeanUtils.copyProperties(entity, vo);
        
        if (relations != null && !relations.isEmpty()) {
            // 过滤当前服务清单的关联关系
            List<ConfServiceItemZoneRelation> itemRelations = relations.stream()
                    .filter(relation -> entity.getId().equals(relation.getServiceItem().getId()))
                    .toList();
                    
            if (!itemRelations.isEmpty()) {
                List<String> serviceZoneIds = itemRelations.stream()
                        .map(relation -> relation.getServiceZone().getId())
                        .collect(Collectors.toList());
                vo.setServiceZoneIds(serviceZoneIds);
            }
        }
        
        return vo;
    }

    /**
     * Entity列表转VO列表
     */
    public List<ConfServiceItemVO> convert(List<ConfServiceItem> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * DTO转Entity
     */
    public ConfServiceItem convert(ConfServiceItemDTO dto) {
        if (dto == null) {
            return null;
        }
        ConfServiceItem entity = new ConfServiceItem();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * DTO列表转Entity列表
     */
    public List<ConfServiceItem> convertFromDTO(List<ConfServiceItemDTO> dtos) {
        if (dtos == null) {
            return null;
        }
        return dtos.stream()
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * DTO转Entity（更新）
     */
    public void updateEntity(ConfServiceItemDTO dto, ConfServiceItem entity) {
        if (dto == null || entity == null) {
            return;
        }
        // 不更新ID和时间字段
        if (dto.getName() != null) {
            entity.setName(dto.getName());
        }
        if (dto.getSendContent() != null) {
            entity.setSendContent(dto.getSendContent());
        }
        if (dto.getChannel() != null) {
            entity.setChannel(dto.getChannel());
        }
        if (dto.getCategory() != null) {
            entity.setCategory(dto.getCategory());
        }
        if (dto.getSort() != null) {
            entity.setSort(dto.getSort());
        }
        if (dto.getEnabled() != null) {
            entity.setEnabled(dto.getEnabled());
        }
        // 移除旧的专区关联设置逻辑，多对多关系通过关联表单独管理
    }
}
