package com.workplat.electronic.certificate;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class Test1 {
    public static void main(String[] args) {
        // 创建假的旧推送数据 (oldPushCompareData)
        PushCompareData oldPushCompareData = new PushCompareData();
        
        // 创建假的表单数据
        Map<String, Object> oldFormData = new HashMap<>();
        
        // 添加一些字段数据
        JSONObject nameField = new JSONObject();
        nameField.put("value", "张三");
        nameField.put("isUpdate", false);
        oldFormData.put("name", nameField);
        
        JSONObject ageField = new JSONObject();
        ageField.put("value", "30");
        ageField.put("isUpdate", false);
        oldFormData.put("age", ageField);
        
        JSONObject addressField = new JSONObject();
        addressField.put("value", "北京市朝阳区");
        addressField.put("isUpdate", false);
        oldFormData.put("address", addressField);
        
        oldPushCompareData.setFormData(JSONObject.toJSONString(oldFormData));
        
        // 创建假的文件分组数据
        Map<String, List<String>> oldFileGroups = new HashMap<>();
        oldFileGroups.put("name", Arrays.asList("file001", "file002"));
        oldFileGroups.put("age", Arrays.asList("file003"));
        oldFileGroups.put("address", Arrays.asList("file004", "file005"));
        oldPushCompareData.setGroupFileIds(oldFileGroups);
        
        System.out.println("旧推送数据: " + JSONObject.toJSONString(oldPushCompareData));
        
        // 模拟新的表单数据
        Map<String, Object> newFormData = new HashMap<>();
        
        // 保持姓名不变
        JSONObject newNameField = new JSONObject();
        newNameField.put("value", "张三");
        newNameField.put("isUpdate", false);
        newFormData.put("name", newNameField);
        
        // 修改年龄
        JSONObject newAgeField = new JSONObject();
        newAgeField.put("value", "31"); // 年龄从30改为31
        newAgeField.put("isUpdate", false);
        newFormData.put("age", newAgeField);
        
        // 保持地址不变
        JSONObject newAddressField = new JSONObject();
        newAddressField.put("value", "北京市朝阳区");
        newAddressField.put("isUpdate", false);
        newFormData.put("address", newAddressField);
        
        // 添加新的字段
        JSONObject phoneField = new JSONObject();
        phoneField.put("value", "13800138000");
        phoneField.put("isUpdate", false);
        newFormData.put("phone", phoneField);
        
        // 模拟新的文件分组数据
        Map<String, List<String>> newFileGroups = new HashMap<>();
        newFileGroups.put("name", Arrays.asList("file001", "file002", "file006")); // 添加新文件
        newFileGroups.put("age", Arrays.asList("file003"));
        newFileGroups.put("address", Arrays.asList("file004", "file005")); // 保持不变

        // 模拟handleExistingCase方法中的数据对比逻辑
        Map<String, Object> resultFormMap = new HashMap<>();
        
        // 对比表单数据
        newFormData.forEach((key, value) -> {
            JSONObject oldValue = (JSONObject) ((Map<?, ?>) JSON.parseObject(oldPushCompareData.getFormData(), Map.class)).get(key);
            if (oldValue != null) {
                // 移除 isUpdate
                oldValue.remove("isUpdate");
                JSONObject newValue = (JSONObject) value;
                newValue.remove("isUpdate");
                // 对比两个对象是否相同
                if (!oldValue.equals(newValue)) {
                    ((JSONObject) value).put("isUpdate", true);
                    resultFormMap.put(key, value);
                } else {
                    ((JSONObject) value).put("isUpdate", false);
                    resultFormMap.put(key, value);
                }
            } else {
                // 新增字段
                ((JSONObject) value).put("isUpdate", true);
                resultFormMap.put(key, value);
            }
        });
        
        // 对比文件分组数据
        newFileGroups.forEach((key, value) -> {
            if (!oldFileGroups.containsKey(key) || !oldFileGroups.get(key).equals(value)) {
                if (resultFormMap.containsKey(key)) {
                    JSONObject jsonObject = (JSONObject) resultFormMap.get(key);
                    jsonObject.put("isUpdate", true);
                    resultFormMap.put(key, jsonObject);
                }
            }
        });
        
        // 创建新的推送数据
        PushCompareData newPushCompareData = new PushCompareData();
        newPushCompareData.setFormData(JSONObject.toJSONString(resultFormMap));
        newPushCompareData.setGroupFileIds(newFileGroups);
        
        System.out.println("新推送数据: " + JSONObject.toJSONString(newPushCompareData));
        
        // 输出对比结果
        System.out.println("\n=== 数据对比结果 ===");
        resultFormMap.forEach((key, value) -> {
            JSONObject field = (JSONObject) value;
            if (field.getBoolean("isUpdate")) {
                System.out.println(key + ": 有更新 - " + field.getString("value"));
            } else {
                System.out.println(key + ": 无变化 - " + field.getString("value"));
            }
        });
        
        System.out.println("\n=== 文件分组对比结果 ===");
        newFileGroups.forEach((key, value) -> {
            if (!oldFileGroups.containsKey(key)) {
                System.out.println(key + ": 新增分组 - " + value);
            } else if (!oldFileGroups.get(key).equals(value)) {
                System.out.println(key + ": 文件有变化");
                System.out.println("  旧文件: " + oldFileGroups.get(key));
                System.out.println("  新文件: " + value);
            } else {
                System.out.println(key + ": 无变化");
            }
        });
    }

    /**
     * 推送数据比对模型
     */
    @Getter
    @Setter
    public static class PushCompareData {
        private String formData; // 处理后的表单数据
        private Map<String, List<String>> groupFileIds; // 分组文件ID映射（key:分组名称, value:文件ID列表）

        @Override
        public String toString() {
            return "PushCompareData{" +
                   "formData='" + StringUtils.abbreviate(formData, 50) + '\'' +
                   ", groupFileIds=" + groupFileIds +
                   '}';
        }
    }
}
