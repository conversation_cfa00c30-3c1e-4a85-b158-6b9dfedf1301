package com.workplat.extend.socialAssistance.api;


import cn.hutool.core.io.resource.BytesResource;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.digest.SM3;
import cn.hutool.http.HttpInterceptor;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.hutool.http.body.MultipartBody;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.ext.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;


@Tag(name = "社会救助回调认证网关接口测试")
@RestController
@RequestMapping("/api/social/assistance/gateway")
public class SocialAssistanceGateWayTestApi {

    static String gatewayUrl = "https://servers.workplat.com/gateway";
    static String projectUrl = "https://servers.workplat.com/gateway/cdz-onething/api/dify/social/assistance/authentication";
    static String clientId = "100BC4DEF3544C5788753BA1E0C904DC";
    static String publicKey = "0497594e0c702241b2a31fb22c4a64f36fec005e78e38310f01085e4a3336b058a239393321ad76472edd77bcfc5609236a277567ad4ab1fde7d5abf267ccb1901";


    @Operation(summary = "社会救助认证网关接口测试")
    @GetMapping(value = "/authentication")
    public ResponseData authentication(String token) {
        JSONObject userInfo = getUserInfo(token);
        return ResponseData.success(userInfo);
    }

    /**
     * 获取用户信息
     */
    private JSONObject getUserInfo(String token) {
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("token", token);
        HttpRequest request = HttpRequest.post(projectUrl);
        request.body(bodyJson.toJSONString());
        request.addInterceptor(getSignInterceptor());
        String result = request.execute().body();
        return JSON.parseObject(result);
    }

    /**
     * 接口鉴权
     */
    private HttpInterceptor getSignInterceptor() {
        return (HttpInterceptor<HttpRequest>) httpObj -> {
            try {
                //sm3 - params摘要信息
                String sign;
                String paramType;

                //对参数进行摘要
                if (httpObj.getMethod() == Method.GET) {
                    //直接param，处理成sm3摘要
                    sign = new SM3().digestHex(StrUtil.bytes(UrlQuery.of(httpObj.form(), false).build(StandardCharsets.UTF_8), StandardCharsets.UTF_8));
                    paramType = "QUERY";
                } else if (httpObj.getMethod() == Method.POST) {

                    //处理 multipart/form-data
                    boolean isMultipart = false;

                    isMultipart = (boolean) FieldUtils.readDeclaredField(httpObj, "isMultiPart", true);
                    if (isMultipart) {
                        ByteArrayOutputStream bodyOutputStream = new ByteArrayOutputStream();
                        MultipartBody.create(httpObj.form(), StandardCharsets.UTF_8).write(bodyOutputStream);
                        sign = new SM3().digestHex(bodyOutputStream.toByteArray());
                        paramType = "BODY";
                    } else {
                        byte[] bodyContent;
                        //兼容低版本hutool没有bodyBytes方法
                        BytesResource bytesResource = (BytesResource) FieldUtils.readField(httpObj, "body", true);
                        byte[] bodyBytes = bytesResource.readBytes();
                        if (null != bodyBytes) {
                            bodyContent = bodyBytes;
                            paramType = "BODY";
                        } else {
                            //FormUrlEncodedBody.create(httpObj.form(), StandardCharsets.UTF_8)
                            bodyContent = StrUtil.bytes(UrlQuery.of(httpObj.form(), true).build(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
                            paramType = "QUERY";
                        }
                        sign = new SM3().digestHex(bodyContent);
                    }
                } else {
                    //直接param，处理成sm3摘要
                    sign = new SM3().digestHex(StrUtil.bytes(UrlQuery.of(httpObj.form(), true).build(StandardCharsets.UTF_8), StandardCharsets.UTF_8));
                    paramType = "QUERY";
                }
                //构建请求
                JSONObject ticketPayload = new JSONObject().fluentPut("clientId", clientId).fluentPut("sign", new SM2(null, publicKey).encryptHex(StrBuilder.create().append(clientId).append(",").append(sign).append(",").append(paramType).toString(), KeyType.PublicKey));
                String ticket = JSON.parseObject(HttpRequest.post(gatewayUrl + "/api/ticket/getTicketBySign").body(ticketPayload.toJSONString()).execute().body()).getString("ticket");
                httpObj.header("ticket", ticket);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        };
    }
}
