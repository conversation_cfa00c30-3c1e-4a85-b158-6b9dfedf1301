package com.workplat.matter.converter;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.vo.ConfMatterExtendVO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @package com.workplat.matter.converter
 * @description
 * @date 2025/7/8
 */
@Component
public class ConfMatterExtendConverter implements BaseConverter<ConfMatterExtend, ConfMatterExtendVO> {

    @Override
    public ConfMatterExtendVO convert(ConfMatterExtend source) {
        ConfMatterExtendVO convert = BaseConverter.super.convert(source);
        convert.setMatterId(source.getMatter().getId());
        convert.setMatterCode(source.getMatter().getMatterCode());
        convert.setMatterName(source.getMatter().getMatterName());
        return convert;
    }
}
