package com.workplat.serviceZone.api;

import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.serviceZone.dto.ConfServiceItemDTO;
import com.workplat.serviceZone.dto.ConfZoneServiceItemRelationsDTO;
import com.workplat.serviceZone.dto.ServiceItemQueryDTO;
import com.workplat.serviceZone.vo.ConfServiceItemVO;
import com.workplat.serviceZone.vo.ServiceItemGroupVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置API接口
 */
@Tag(name = "服务清单配置", description = "服务清单配置管理接口")
@RequestMapping("/api/serviceItem")
public interface ConfServiceItemApi {

    @Operation(summary = "分页查询服务清单")
    @GetMapping("/queryPage")
    ResponseData<Page<ConfServiceItemVO>> queryPage(
            @Parameter ServiceItemQueryDTO queryDTO,
            @Parameter(description = "分页参数") PageableDTO pageableDTO);

    @Operation(summary = "根据专区ID查询服务清单")
    @GetMapping("/getByZoneId")
    ResponseData<List<ConfServiceItemVO>> getByZoneId(
            @Parameter(description = "专区ID") String serviceZoneId);

    @Operation(summary = "根据ID查询服务清单详情")
    @GetMapping("/getById")
    ResponseData<ConfServiceItemVO> getById(
            @Parameter(description = "服务清单ID") String id);

    @Operation(summary = "新增服务清单")
    @PostMapping("/add")
    ResponseData<String> add(@Valid @RequestBody ConfServiceItemDTO dto);

    @Operation(summary = "修改服务清单")
    @PostMapping("/updateById")
    ResponseData<Void> updateById(@Valid @RequestBody ConfServiceItemDTO dto);

    @Operation(summary = "删除服务清单")
    @PostMapping("/deleteById")
    ResponseData<Void> deleteById(@RequestBody ConfServiceItemDTO dto);

    @Operation(summary = "启用/禁用服务清单")
    @PostMapping("/updateEnabled")
    ResponseData<Void> updateEnabled(
            @Parameter(description = "服务清单ID") String id,
            @Parameter(description = "是否启用") Boolean enabled);

    @Operation(summary = "根据专区ID和渠道查询服务清单，按分类分组")
    @GetMapping("/getGroupedByCategory")
    ResponseData<List<ServiceItemGroupVO>> getGroupedByCategory(
            @Parameter(description = "专区ID") String serviceZoneId,
            @Parameter(description = "渠道，如：pc,wx") String channel);

}
