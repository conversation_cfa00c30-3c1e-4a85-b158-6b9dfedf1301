package com.workplat.accept.business.third.api;

import com.workplat.accept.business.third.dto.ThirdDataQueryDto;
import com.workplat.accept.business.third.vo.ThirdDataVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: Odin
 * @Date: 2024/9/26 09:56
 * @Description:
 */
@Tag(name = "三方数据展示接口")
@RestController
@RequestMapping(value = "/api/third-data")
public interface ThirdDataApi {

    @PostMapping("/list")
    @Operation(summary = "个人中心查询")
    public ResponseData<ThirdDataVO> queryThirdDataList(@RequestBody ThirdDataQueryDto dto);

}
