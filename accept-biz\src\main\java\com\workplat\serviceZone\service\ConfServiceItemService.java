package com.workplat.serviceZone.service;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.gss.common.core.service.BaseService;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单配置Service接口
 */
public interface ConfServiceItemService extends BaseService<ConfServiceItem> {

    /**
     * 根据专区ID查询服务清单
     * @param serviceZoneId 专区ID
     * @return 服务清单列表
     */
    List<ConfServiceItem> getByServiceZoneId(String serviceZoneId);

    /**
     * 启用/禁用服务清单
     * @param id 服务清单ID
     * @param enabled 是否启用
     */
    void updateEnabled(String id, Boolean enabled);

    /**
     * 批量保存服务清单与专区的关联关系
     * @param serviceItemId 服务清单ID
     * @param serviceZoneIds 专区ID列表
     */
    void saveServiceItemZoneRelations(String serviceItemId, List<String> serviceZoneIds);

    /**
     * 根据ID列表批量查询服务清单
     * @param ids 服务清单ID列表
     * @return 服务清单列表
     */
    List<ConfServiceItem> findByIds(List<String> ids);

}
