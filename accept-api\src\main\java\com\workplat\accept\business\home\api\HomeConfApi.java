package com.workplat.accept.business.home.api;


import com.workplat.accept.business.home.dto.ConfMatterCatalogDto;
import com.workplat.accept.business.home.dto.HomeConfDto;
import com.workplat.accept.business.home.vo.ConfMatterCatalogVO;
import com.workplat.accept.business.home.vo.HomeConfVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.workplat.gss.log.constant.OperationType;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Author: Odin
 * @Date: 2024/9/24 16:28
 * @Description:
 */

@Validated
@Tag(name = "首页配置")
@RestController
@RequestMapping("/api/home-conf")
public interface HomeConfApi {

    @GetMapping("/list")
    @Operation(summary = "获取首页图标配置")
    public ResponseData<LinkedHashMap<String, List<HomeConfVO>>> getHomeConfList();

    @PostMapping("/saveHomeConf")
    @Operation(summary = "保存事项的菜单配置")
    public ResponseData<Void> save(@Valid @RequestBody HomeConfDto homeConfDto);

    @GetMapping("/getByConfMatterId")
    @Operation(summary = "通过事项id获取事项的菜单配置")
    public ResponseData<HomeConfVO> getByRelatedConfMatterId(@RequestParam("confMatterId") @NotBlank(message = "事项编号不能为空") String confMatterId);

    @GetMapping("/deleteMatter")
    @Operation(summary = "删除事项")
    public ResponseData<Void> deleteMatter(@RequestParam String id);

    @PostMapping("/save")
    @Operation(summary = "新增事项")
    ResponseData<ConfMatterCatalogVO> save(@Valid @RequestBody ConfMatterCatalogDto confMatterCatalogDto);

}
