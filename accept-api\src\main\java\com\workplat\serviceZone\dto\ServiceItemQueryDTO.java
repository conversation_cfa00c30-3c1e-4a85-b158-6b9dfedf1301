package com.workplat.serviceZone.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务清单查询DTO
 */
@Data
@Schema(description = "服务清单查询DTO")
public class ServiceItemQueryDTO {

    /**
     * 服务名称（模糊查询）
     */
    @Schema(description = "服务名称（模糊查询）")
    private String name;

    /**
     * 专区ID
     */
    @Schema(description = "专区ID")
    private String serviceZoneId;

    /**
     * 渠道过滤
     */
    @Schema(description = "渠道过滤")
    private String channel;

    /**
     * 分类过滤
     */
    @Schema(description = "分类过滤")
    private String category;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean enabled;

    /**
     * 是否热门推荐
     */
    @Schema(description = "是否热门推荐")
    private Boolean isHotRecommend;
}
