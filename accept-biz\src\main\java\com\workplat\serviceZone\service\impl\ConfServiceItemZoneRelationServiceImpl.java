package com.workplat.serviceZone.service.impl;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItem;
import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.accept.business.serviceZone.entity.ConfServiceZone;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.serviceZone.repository.ConfServiceItemZoneRelationRepository;
import com.workplat.serviceZone.service.ConfServiceItemZoneRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联Service实现类
 */
@Service
public class ConfServiceItemZoneRelationServiceImpl extends BaseServiceImpl<ConfServiceItemZoneRelation> implements ConfServiceItemZoneRelationService {

    @Autowired
    private ConfServiceItemZoneRelationRepository relationRepository;

    @Override
    public List<ConfServiceItemZoneRelation> getByServiceZoneId(String serviceZoneId) {
        return relationRepository.findByServiceZoneIdAndDeletedFalseOrderBySortAsc(serviceZoneId);
    }

    @Override
    public List<ConfServiceItemZoneRelation> getByServiceItemId(String serviceItemId) {
        return relationRepository.findByServiceItemIdAndDeletedFalse(serviceItemId);
    }

    @Override
    public List<ConfServiceItemZoneRelation> getByServiceZoneIdWithDetails(String serviceZoneId) {
        return relationRepository.findByServiceZoneIdWithDetails(serviceZoneId);
    }

    @Override
    public List<ConfServiceItemZoneRelation> getByServiceItemIdWithDetails(String serviceItemId) {
        return relationRepository.findByServiceItemIdWithDetails(serviceItemId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelations(String serviceItemId, List<String> serviceZoneIds) {
        if (serviceZoneIds == null || serviceZoneIds.isEmpty()) {
            return;
        }
        
        // 逻辑删除现有关联关系
        deleteByServiceItemId(serviceItemId);
        
        // 创建新的关联关系
        List<ConfServiceItemZoneRelation> relations = buildRelations(serviceZoneIds, serviceItemId, true);
        saveList(relations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRelationsByZone(String serviceZoneId, List<String> serviceItemIds) {
        if (serviceItemIds == null || serviceItemIds.isEmpty()) {
            return;
        }
        
        // 逻辑删除现有关联关系
        deleteByServiceZoneId(serviceZoneId);
        
        // 创建新的关联关系
        List<ConfServiceItemZoneRelation> relations = buildRelations(serviceItemIds, serviceZoneId, false);
        saveList(relations);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByServiceZoneId(String serviceZoneId) {
        relationRepository.deleteByServiceZoneId(serviceZoneId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByServiceItemId(String serviceItemId) {
        relationRepository.deleteByServiceItemId(serviceItemId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(String id, Integer sort) {
        ConfServiceItemZoneRelation relation = queryById(id);
        if (relation != null) {
            relation.setSort(sort);
            update(relation);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateSort(List<ConfServiceItemZoneRelation> relations) {
        if (relations != null && !relations.isEmpty()) {
            relations.forEach(relation -> {
                if (relation.getId() != null && relation.getSort() != null) {
                    updateSort(relation.getId(), relation.getSort());
                }
            });
        }
    }


    private List<ConfServiceItemZoneRelation> buildRelations(List<String> ids, String fixedId, boolean isServiceItemIdFixed) {
        AtomicInteger sort = new AtomicInteger(1);
        return ids.stream()
                .map(id -> {
                    ConfServiceItemZoneRelation relation = new ConfServiceItemZoneRelation();

                    ConfServiceItem serviceItem = new ConfServiceItem();
                    ConfServiceZone serviceZone = new ConfServiceZone();

                    if (isServiceItemIdFixed) {
                        serviceItem.setId(fixedId);
                        serviceZone.setId(id);
                    } else {
                        serviceItem.setId(id);
                        serviceZone.setId(fixedId);
                    }

                    relation.setServiceItem(serviceItem);
                    relation.setServiceZone(serviceZone);
                    relation.setSort(sort.getAndIncrement());
                    return relation;
                })
                .toList();
    }

}