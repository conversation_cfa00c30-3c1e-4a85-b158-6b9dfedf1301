package com.workplat.matter.service.impl;

import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.matter.entity.BizNetworkWindow;
import com.workplat.matter.service.BizNetworkWindowService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.service.impl
 * @description
 * @date 2025/6/10 13:39
 */
@Service
public class BizNetworkWindowServiceImpl extends BaseServiceImpl<BizNetworkWindow> implements BizNetworkWindowService {
}
