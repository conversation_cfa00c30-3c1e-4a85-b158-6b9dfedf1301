package com.workplat.flow.repository;

import com.workplat.flow.entity.ConfFlowComponent;
import com.workplat.gss.common.core.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * @author: qian cheng
 * @package: com.workplat.flow.repository
 * @description: 流程组件关联Repository
 * @date: 2025/5/20 9:56
 */
@Repository
public interface ConfFlowComponentRepository extends BaseRepository<ConfFlowComponent> {

    @Query(value = "select ent.id as id,ent.code as code,ent.name as name,ent.description as description, ent.create_time createTime,ent.update_time updateTime, COALESCE(count(distinct extend.id), 0) as itemCount\n" +
            "from conf_component ent\n" +
            "inner join conf_flow_component rel on ent.id = rel.conf_component_id\n" +
            "inner join conf_matter_extend extend on rel.conf_flow_id = extend.conf_flow_id\n" +
            "where ent.deleted = false and rel.deleted = false and extend.deleted = false\n" +
            "and (:componentName is null or ent.name like %:componentName%)\n" +
            "group by ent.id,ent.code,ent.name,ent.description,ent.create_time,ent.update_time\n" +
            "order by ent.create_time desc, ent.id",
            countQuery = "select count(*) from conf_component ent " +
                    "where ent.deleted = false " +
                    "and (:componentName is null or ent.name = :componentName)"
            ,nativeQuery = true)
    Page<Map<String, Object>> queryComponentUsage(@Param("componentName") String componentName, Pageable pageable);
}
