package com.workplat.componentEngine.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(description = "组件属性DTO")
public class ComponentPropsDTO {

    @Schema(description = "组件名称")
    private String componentName;

    @Schema(description = "组件编码")
    private String componentCode;

    @Schema(description = "排序")
    private int sequenceOrder;

    @Schema(description = "属性")
    private List<PropsVO> props;

    @Getter
    @Setter
    public static class PropsVO {

        @Schema(description = "属性标题")
        private String label;

        @Schema(description = "属性名")
        private String name;

        @Schema(description = "属性值")
        private String value;
    }

}
