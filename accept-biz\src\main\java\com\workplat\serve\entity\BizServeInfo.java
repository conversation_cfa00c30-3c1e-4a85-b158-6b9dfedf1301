//package com.workplat.serve.entity;
//
//import com.workplat.gss.common.core.entity.BaseEntity;
//import jakarta.persistence.Column;
//import jakarta.persistence.Entity;
//import jakarta.persistence.OneToMany;
//import jakarta.persistence.Table;
//import lombok.Getter;
//import lombok.Setter;
//import org.hibernate.annotations.Comment;
//
//import java.util.List;
//
///**
// * <AUTHOR> cheng
// * @className: com.workplat.serve.entity
// * @description: 服务信息
// * @date 2025/5/14 13:53
// */
//@Setter
//@Getter
//@Entity
//@Table(name = "biz_serve_info")
//public class BizServeInfo extends BaseEntity {
//
//    @Comment("服务名称")
//    @Column(name = "name", length = 50)
//    private String name;
//
//    @Comment("服务编码")
//    @Column(name = "code", length = 32)
//    private String code;
//
//    /**
//     * 数据字典:serve_type
//     */
//    @Comment("服务类型")
//    @Column(name = "type", length = 32)
//    private String type;
//
//    @Comment("服务描述")
//    @Column(name = "description")
//    private String description;
//
//    /**
//     * 数据字典:guide_type
//     */
//    @Comment("办事指南类型")
//    @Column(name = "guide_type", length = 32)
//    private String guideType;
//
//    @Comment("办事指南地址")
//    @Column(name = "guide_url")
//    private String guideUrl;
//
//    @Comment("是否启用")
//    @Column(name = "is_enable")
//    private boolean enable = true;
//
//    @Comment("是否第三方服务")
//    @Column(name = "is_third_party")
//    private boolean thirdParty = true;
//
//    @OneToMany(mappedBy = "serve")
//    private List<BizServeMethod> methodList;
//
//}
