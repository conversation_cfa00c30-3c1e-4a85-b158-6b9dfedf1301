package com.workplat.serviceZone.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/15
 * @Description: 服务专区配置VO
 */
@Data
public class ConfServiceZoneVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 专区名称
     */
    private String name;

    /**
     * 专区描述
     */
    private String description;

    /**
     * 专区图标文件id
     */
    private String iconFileId;

    /**
     * 背景图文件id
     */
    private String backgroundFileId;

    /**
     * PC背景图
     */
    private String pcBackgroundFileId;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 排序号（来自BaseEntity）
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 服务清单列表
     */
    private List<ConfServiceItemVO> serviceItems;
}
