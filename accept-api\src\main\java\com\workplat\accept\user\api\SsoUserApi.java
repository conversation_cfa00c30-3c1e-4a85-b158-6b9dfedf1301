package com.workplat.accept.user.api;

import cn.hutool.http.server.HttpServerRequest;
import com.workplat.accept.user.vo.SsoUserVO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/23 10:43
 * @Description:
 */

@Tag(name = "用户")
@RestController
@RequestMapping(value = "/api/user")
public interface SsoUserApi {

    /**
     * 根据用户ID返回信息
     * @param userId
     * @return
     */
    @GetMapping("/rmcLoginByUserId")
    @Operation(summary = "获取用户信息")
    public ResponseData<SsoUserVO> rmcLoginByUserId(String userId);

    /**
     * 退出登陆
     * @return
     */
    @GetMapping("/logout")
    @Operation(summary = "退出登陆")
    public ResponseData logout();

    /**
     * 根据姓名身份证获取一网通办token
     * @param name
     * @param idCard
     * @return
     */
    @GetMapping("/getANetToken")
    @Operation(summary = "获取一网通办Token")
    public ResponseData getANetToken(@RequestParam String name, String idCard);
}
