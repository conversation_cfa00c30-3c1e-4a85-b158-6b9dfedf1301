package com.workplat.accept.business.third.dto;

import lombok.Data;

/**
 * @Author: Odin
 * @Date: 2024/10/23 13:44
 * @Description:
 */

@Data
public class ThirdDataQueryDto {

    private Integer page;
    private Integer pageSize;
    // 业务编号 / 合同号 / 坐落
    private String keyword;
//    // 业务编号
//    private String objectId;
//    // 合同号
//    private String contractNo;
//    // 坐落
//    private String address;
    // 审核状态
    private String status;

    // 是否工作人员
    private boolean worker;
    private String cardId;
    //一手房 二手房
    private String interfaceCode;
}
