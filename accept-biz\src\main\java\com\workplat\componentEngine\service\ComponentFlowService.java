package com.workplat.componentEngine.service;

import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.flow.ConfFlowApi;
import com.workplat.flow.dto.ConfFlowDTO;
import com.workplat.utils.ChatCacheUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ComponentFlowService {

    private final ConfFlowApi confFlowApi;
    private final ConfComponentService confComponentService;
    private final ChatCacheUtil chatCacheUtil;

    public ComponentFlowService(ConfFlowApi confFlowApi,
                                ConfComponentService confComponentService,
                                ChatCacheUtil chatCacheUtil) {
        this.confFlowApi = confFlowApi;
        this.confComponentService = confComponentService;
        this.chatCacheUtil = chatCacheUtil;
    }

    /**
     * 获取下一个组件
     */
    public ConfComponent getNextComponent(ComponentDataContext currentComponent) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(currentComponent.getRecordId());
        String flowCode = chatProcessDTO.getFlowCode();
        List<ConfFlowDTO.ConfComponentDTO> components = confFlowApi.queryByCode(flowCode)
                .getData()
                .getComponents();

        for (ConfFlowDTO.ConfComponentDTO component : components) {
            if (component.getComponentId().equals(currentComponent.getConfComponent().getId())) {
                int nextIndex = components.indexOf(component) + 1;
                if (nextIndex < components.size()) {
                    ConfFlowDTO.ConfComponentDTO confComponentDTO = components.get(nextIndex);
                    return confComponentService.getByCode(confComponentDTO.getComponentCode());
                }
            }
        }
        return null;
    }

    public ConfComponent getPrevComponent(ComponentDataContext currentComponent) {
        ChatProcessDTO chatProcessDTO = chatCacheUtil.get(currentComponent.getRecordId());
        String flowCode = chatProcessDTO.getFlowCode();
        List<ConfFlowDTO.ConfComponentDTO> components = confFlowApi.queryByCode(flowCode)
                .getData()
                .getComponents();
        for (ConfFlowDTO.ConfComponentDTO component : components) {
            if (component.getComponentId().equals(currentComponent.getConfComponent().getId())) {
                int prevIndex = components.indexOf(component) - 1;
                if (prevIndex >= 0) {
                    ConfFlowDTO.ConfComponentDTO confComponentDTO = components.get(prevIndex);
                    return confComponentService.getByCode(confComponentDTO.getComponentCode());
                }
            }
        }
        return null;
    }
} 