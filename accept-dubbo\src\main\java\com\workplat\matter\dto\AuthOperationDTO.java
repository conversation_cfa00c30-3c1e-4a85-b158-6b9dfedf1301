package com.workplat.matter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>eng
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/6/6 13:40
 */
@Data
@Schema(description = "授权操作DTO")
public class AuthOperationDTO {

    @Schema(description = "授权人姓名")
    private String name;

    @Schema(description = "授权人手机号")
    private String phone;

    @NotBlank(message = "授权人身份证号不能为空")
    @Schema(description = "授权人身份证号")
    private String idCard;

    @NotBlank(message = "实例id不能为空")
    @Schema(description = "实例id")
    private String instanceId;

    @NotBlank(message = "授权类型不能为空")
    @Schema(description = "授权类型")
    private String type;

    @Schema(description = "是否人脸核验")
    private boolean face;

    @Schema(description = "签名文件地址")
    private String signFileUrl;

}
