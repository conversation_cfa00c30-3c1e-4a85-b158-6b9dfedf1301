package com.workplat.accept.business.accept.api.Impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.accept.api.InstanceAcceptApi;
import com.workplat.accept.business.chat.dto.InstancePushLogDTO;
import com.workplat.accept.business.chat.service.DifyServiceHelper;
import com.workplat.accept.business.chat.vo.BlockWorkFlowVO;
import com.workplat.gss.application.dubbo.constant.InstanceApplicationStatusEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.push.entity.InstancePushLog;
import com.workplat.push.service.InstancePushLogService;
import com.workplat.push.vo.InstancePushLogVO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Odin
 * @Date: 2024/9/24 10:47
 * @Description:
 */

@RestController
public class InstanceAcceptApiImpl implements InstanceAcceptApi {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final InstancePushLogService binstancePushLogService;
    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final DifyServiceHelper difyServiceHelper;


    public InstanceAcceptApiImpl(BizInstanceInfoService bizInstanceInfoService,
                                 InstancePushLogService instancePushLogService,
                                 BizInstanceFieldsService bizInstanceFieldsService,
                                 DifyServiceHelper difyServiceHelper) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.binstancePushLogService = instancePushLogService;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.difyServiceHelper = difyServiceHelper;
    }


    @Override
    @ApiLogging(module = "业务审批", operation = "更新草稿", type = OperationType.UPDATE)
    public ResponseData<String> updateDraft(String instanceId) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        if (bizInstanceInfo != null) {
            bizInstanceInfo.setApplicationStatusEnum(InstanceApplicationStatusEnum.TEMP);
            bizInstanceInfoService.update(bizInstanceInfo);
        }
        return ResponseData.success("更新成功");
    }

    @Override
    @ApiLogging(module = "业务审批", operation = "更新推送状态为待处理", type = OperationType.UPDATE)
    public ResponseData<String> updatePushStatus(String instanceId) {
        // 更新推送表状态 0
        InstancePushLog pushLog = binstancePushLogService.queryForSingle(ImmutableMap.of("=(instance.id)", instanceId));
        if (pushLog != null) {
            pushLog.setPushStatus("0");
            binstancePushLogService.update(pushLog);
        }
        return ResponseData.success("更新成功");
    }

    @Override
    @ApiLogging(module = "业务审批", operation = "获取推送记录分页", type = OperationType.QUERY)
    public ResponseData<Page<InstancePushLogVO>> pushLogPage(InstancePushLogDTO dto, PageableDTO pageable) {
        return ResponseData.success(binstancePushLogService.queryForPage(dto, pageable));
    }

    @Override
    public ResponseData<Object> socialJudge(String instanceId) {
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(instanceId);

        String s1 = """
                {
                "家庭汽车数量": "clsl",
                "家庭大型农机具、经营性船数量": "dxnjcbsl",
                "家庭房产数量": "zfsl",
                "家庭金融资产（万元）": "srzc",
                "是否本地户籍": "localHj",
                "家庭成员人均月收入": "jtcyrjsr",
                "是否丧失劳动能力": "sfssldnl",
                "申请人月收入": "ysr",
                "困难情况说明": "knsm",
                "是否生活必须支出突然增加（自负教育、医疗等）": "sfshbxzctrzj"
                }""";
        Map<String, String> paramsMap = JSONObject.parseObject(s1, Map.class);

        //获取填写数据 {"clsl":"12"}
        String formObj = bizInstanceFieldsVO.getFormObj();
        Map<String, String> formObjMap = JSONObject.parseObject(formObj, Map.class);

        // 根据字段数据formObjMap转换paramsMap成参数,转换新的map
        Map<String, String> newParamsMap = new HashMap<>();
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (formObjMap.containsKey(value)) {
                newParamsMap.put(key, formObjMap.get(value));
            }
            if ("localHj".equals( value)){
                newParamsMap.put(key, "1".equals(formObjMap.get(value))? "是": "否");
            }
            if ("sfssldnl".equals( value)){
                newParamsMap.put(key, "1".equals(formObjMap.get(value))? "是": "否");
            }
            if ("sfshbxzctrzj".equals( value)){
                newParamsMap.put(key, "1".equals(formObjMap.get(value))? "是": "否");
            }
        }

        String appKey =  "app-iF1Mi8JvawIbIVQiXWBS3uwJ";

        BlockWorkFlowVO blockWorkFlowResponse = difyServiceHelper.blockingWorkflow(instanceId,
                appKey, ImmutableMap.of("input", JSONObject.toJSONString(newParamsMap)));
        Map<String, Object> data = blockWorkFlowResponse.getData();
        if (data != null && data.containsKey("outputs")) {
            Object outputsObj = data.get("outputs");
            if (outputsObj instanceof Map<?, ?> outputs) {
                Object resultObj = outputs.get("result");
                if (resultObj != null) {
                  return ResponseData.success(resultObj);
                }
            }
        }
        return ResponseData.success().build();
    }
}
