package com.workplat.gss.application.biz.service;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.application.dubbo.constant.BizInstanceExtendJsonEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 情形选择之后网点信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/13 19:21
 */
@Service
@Slf4j
public class BizInstanceNetWorkWindowService {

    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;


    /**
     * 情形选择之后保存网点信息
     *
     * @param instanceId
     * @param addIds
     * @param delIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void initInstanceNetWorkWindow(String instanceId, String addIds, String delIds) {
        log.info("情形选择之后保存网点信息，instanceId:{},addIds:{},delIds:{}", instanceId, addIds, delIds);
        String netWorkWindowCode = null;
        if (StringUtils.isNotEmpty(addIds)) {
            Set<String> addIdsList = Arrays.stream(addIds.split(",")).collect(Collectors.toSet());
            netWorkWindowCode = addIdsList.stream().findFirst().orElse(null);
        }

        if (netWorkWindowCode == null) {
            return;
        }

        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(BizInstanceExtendJsonEnum.NET_WORK_WINDOW.getCode(), netWorkWindowCode);
        bizInstanceInfo.extendJsonAdd(jsonObject);
        bizInstanceInfoService.update(bizInstanceInfo);
    }

    /**
     * 拿到情形选择之后保存的网点信息
     *
     * @param bizInstanceInfo
     * @return
     */
    public String getInstanceNetWorkWindow(BizInstanceInfo bizInstanceInfo) {
        Object object = BizInstanceExtendJsonEnum.NET_WORK_WINDOW.convertObject(bizInstanceInfo);
        return object == null ? "" : String.valueOf(object);
    }
}
