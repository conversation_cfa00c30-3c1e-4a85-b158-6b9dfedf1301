package com.workplat.electronic.certificate;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.core.util.Sm4Util;
import com.workplat.gss.common.script.model.instance.LicenseFetchInput;
import com.workplat.gss.common.script.model.instance.LicenseFetchOutput;
import com.workplat.gss.common.script.service.instance.LicenseFetchLoader;
import com.workplat.gss.file.constant.SysFileSource;
import com.workplat.gss.file.dto.SysFileUploadModel;
import com.workplat.gss.file.entity.SysFileEntity;
import com.workplat.gss.file.service.SysFileEntityService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.http.MediaType;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 身份证
 * 这玩意复制到脚本中心的测试里直接就可以执行
 */
@Slf4j
public class SfzLicenseFetchLoaderImpl implements LicenseFetchLoader {

    public static final String URL = "http://************/certificate-lb";
    public static final String AK = "Mjrh2FSQsyH84E6v";
    //结婚证
//    public static final String ZYLX = "MZ_00010";
    //身份证
    public static final String ZYLX = "GA_0121";
    //户口本
//    public static final String ZYLX = "GA_0119";


    public static final boolean DEBUG_MODE = false;


    public static void main(String[] args) {
        LicenseFetchLoader licenseFetchLoader = new SfzLicenseFetchLoaderImpl();
        LicenseFetchOutput output = licenseFetchLoader.licenseFetch(new LicenseFetchInput());
        System.out.println(JSONObject.toJSONString(output, JSONWriter.Feature.PrettyFormat));
    }


    @Override
    public LicenseFetchOutput licenseFetch(LicenseFetchInput input) {
        LicenseFetchOutput output = new LicenseFetchOutput();
        JSONObject formData = new JSONObject();

        if (input == null || input.getInstanceId() == null) {
            if (DEBUG_MODE) {
                formData.put("cusMsg", "input为空");
            }
            return output;
        }
        // 表单数据
        BizInstanceInfoService infoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceInfo info = infoService.queryById(input.getInstanceId());
        if (info == null) {
            if (DEBUG_MODE) {
                formData.put("cusMsg", "BizInstanceInfo为空");
            }
            return output;
        }
        if (DEBUG_MODE) {
            formData.put("cusMsg2", "电子证照数据查询" + info.getLinkCertificateCode() + "_" + info.getLinkName() + "_" + info.getLinkCertificateCode());
        }
        String body = findCertificateByHolderCodeAndTypeCodes(info.getLinkCertificateCode(), info.getLinkName(), info.getLinkCertificateCode());
        if (StringUtil.isEmpty(body)) {
            if (DEBUG_MODE) {
                formData.put("cusMsg", "电子证照数据查询为空");
            }
            return output;
        }
        if (DEBUG_MODE) {
            formData.put("findCertificateByHolderCodeAndTypeCodes_res", body);
        }


        output.setFormData(formData);

        // 上传文件
        try {
            // 获得原文件
            String certFileId = JSONArray.parseArray(body).getJSONObject(0).getString("certFileId");
            byte[] imgBytes = handlePreviewBytes(downloadCertificate(body, certFileId));
            if (DEBUG_MODE) {
                formData.put("findCertificateByHolderCodeAndTypeCodes_res", body);
            }
            SysFileEntity sysFileEntity = getSysFileEntity(imgBytes, "身份证-原件.jpg");

            if (DEBUG_MODE) {
                formData.put("BytesBase64", toBase64Image(imgBytes, "jpg"));
            }
            List<JSONObject> fileList = new ArrayList<>();
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(sysFileEntity));
            fileList.add(jsonObject);
            // 文件列表
            output.setFiles(fileList);

        } catch (Exception e) {
            if (DEBUG_MODE) {
                formData.put("exception", e.getMessage());
            }
        }

        return output;
    }

    // byte[] 转 base64 图片字符串
    public static String toBase64Image(byte[] imageBytes, String imageType) {
        String base64 = Base64.getEncoder().encodeToString(imageBytes);
        return base64;
    }

    /**
     * 获得文件信息
     *
     * @param bytes    文件流
     * @param fileName 文件名
     * @return 文件信息
     */
    private static SysFileEntity getSysFileEntity(byte[] bytes, String fileName) throws Exception {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        SysFileUploadModel mongoFileModel = new SysFileUploadModel();
        mongoFileModel.setContent(inputStream);
        mongoFileModel.setFileName(fileName);
        //文件类型
        mongoFileModel.setContentType(MediaType.IMAGE_JPEG_VALUE);
        mongoFileModel.setLength((long) bytes.length);
        mongoFileModel.setFileSource(SysFileSource.SYSTEM_USER_PRIVATE_FILE.name());
        mongoFileModel.setMd5(DigestUtils.md5Hex(inputStream.readAllBytes()));
        SysFileEntityService entityService = ApplicationContextUtil.getBean(SysFileEntityService.class);
        return entityService.upload(mongoFileModel);
    }

    /**
     * 获得电子证照数据
     *
     * @return
     */
    public static String findCertificateByHolderCodeAndTypeCodes(String cardNo, String queryName, String queryIdCard) {

        JSONObject request = new JSONObject()
                .fluentPut("platformCode", "estate")
                .fluentPut("serviceItemCode", "query")
                .fluentPut("serviceItemName", "一手房过户")
                .fluentPut("usageIp", "*************")
                .fluentPut("certificateHolderCode", Sm4Util.encodeStr(cardNo, AK))
                .fluentPut("certificateTypeCodes", ListUtil.of(ZYLX))
                .fluentPut("queryPersonName", Sm4Util.encodeStr(queryName, AK))
                .fluentPut("queryPersonCardId", Sm4Util.encodeStr(queryIdCard, AK))
                .fluentPut("holderType", "PERSON");

        String body = HttpRequest.post(URL + "/api/safety/certificate/custom/findCertificateByHolderCodeAndTypeCodes")
                .body(request.toJSONString()

                )
                .execute()
                .body();
        if (body.contains("\"status\":400") || body.contains("\"status\":500")) {
            return "";
        }

        return body;
    }

    /**
     * 获得电子证照文件
     *
     * @return
     */
    public static byte[] downloadCertificate(String body, String certFileId) {

        byte[] bytes = HttpRequest.post(URL + "/api/safety/certificate/custom/downloadCertificate")
                .body(new JSONObject()
                        .fluentPut("platformCode", "estate")
                        .fluentPut("serviceItemCode", "query")
                        .fluentPut("serviceItemName", "一手房过户")
                        .fluentPut("usageIp", "*************")
                        .fluentPut("certFileId", certFileId)
                        .toJSONString()
                )
                .execute()
                .bodyBytes();

        return bytes;
    }


    /**
     * 判断是否为 PDF 格式
     */
    public static boolean isPdf(byte[] bytes) {
        if (bytes == null || bytes.length < 4) return false;
        return bytes[0] == 0x25 && bytes[1] == 0x50 && bytes[2] == 0x44 && bytes[3] == 0x46;
        // 即 "%PDF"
    }

    /**
     * 将 PDF 的第1页转换为 JPG，并返回 JPG 的 byte[]
     */
    public static byte[] convertPdfToJpg(byte[] pdfBytes) throws IOException {
        try (PDDocument document = PDDocument.load(pdfBytes)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            BufferedImage image = pdfRenderer.renderImageWithDPI(0, 150); // 第0页，150DPI

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", baos);
            return baos.toByteArray();
        }
    }

    /**
     * 主方法：判断是否PDF，是就转为JPG并返回；否则原样返回
     */
    public static byte[] handlePreviewBytes(byte[] previewBytes) throws IOException {
        if (isPdf(previewBytes)) {
            return convertPdfToJpg(previewBytes);
        }
        return previewBytes; // 不是 PDF，直接返回
    }

}
