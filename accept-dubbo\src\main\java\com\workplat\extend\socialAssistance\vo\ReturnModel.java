package com.workplat.extend.socialAssistance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName: ReturnModel
 * @Description:
 * @Author: shenjh
 * @Date: 2020/8/7
 **/
@Schema(description = "响应")
@Data
@Builder
public class ReturnModel<T> {
    /**
     * 错误状态，返回错误的提示信息
     */
    @Schema(description = "错误状态，返回错误的提示信息")
    private String message;
    /**
     * 错误消息
     */
    @Schema(description = "错误消息")
    private String error;
    /**
     * 路径
     */
    @Schema(description = "路径")
    private String path;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private int status;
    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    private long timestamp;
    /**
     * 数据
     */
    @Schema(description = "数据")
    private T data;

    /**
     * 数据
     */
    @Schema(description = "是否成功")
    @Builder.Default
    private Boolean isSuccess = true;
}
