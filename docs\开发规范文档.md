# AI政务智能中枢平台开发规范文档

## 1. 项目架构概述

### 1.1 模块划分
项目采用三层模块架构：
- **xxx-biz**：业务实现层，包含启动类、Service实现、Controller实现
- **xxx-api**：对外交互API层，包含API接口定义、DTO类
- **xxx-dubbo**：对内Dubbo服务层，包含Dubbo接口定义、VO类、Entity类

### 1.2 依赖关系
```
xxx-biz (业务实现层)
  ↓ 依赖
xxx-api (API接口层)
  ↓ 依赖  
xxx-dubbo (Dubbo服务层)
```

## 2. 参数传输规范

### 2.1 Controller层参数接收规范

#### 2.1.1 请求体参数
```java
// 使用@RequestBody接收复杂对象
@PostMapping("/queryListByName")
ResponseData<Page<BizServeInfoVo>> queryListByName(@RequestBody(required = false) KeyDto keyDto, PageableDTO pageDTO);

// 使用@RequestBody接收DTO对象
@PostMapping("/add")
ResponseData addInfo(@RequestBody BizServeInfoDto bizServeInfoDto);
```

#### 2.1.2 查询参数
```java
// 使用@RequestParam或直接参数接收简单类型
@GetMapping("/getById")
ResponseData<BizServeInfoVo> getById(String id);

@GetMapping("/getByIdToChannel")
ResponseData<BizServeInfoVo> getByIdToChannel(String id, String channel);
```

#### 2.1.3 路径参数
```java
// API路径规范：/api/{中心名称}/{模块名称}/{功能}/{...}
@RequestMapping(value = "/api/serve")
@RequestMapping("/api/matter/extend/")
```

### 2.2 Service层参数传递规范

#### 2.2.1 方法参数数量限制
- 单个方法参数不超过5个
- 超过3个参数时，建议封装为DTO对象
- 布尔类型参数使用包装类型Boolean

#### 2.2.2 参数类型选择
```java
// 推荐：使用包装类型，避免空指针异常
public BizServeInfoVo getByIdToChannel(String id, String channel) {
    // 实现逻辑
}

// 推荐：复杂查询使用Map参数
public List<BizInstanceChat> getList(Map<String, Object> queryParams) {
    return queryForList(queryParams);
}
```

### 2.3 Repository层查询参数规范
```java
// 使用MapUtil构建查询条件
Map<String, Object> queryMap = MapUtil.<String, Object>builder()
    .put("=(instanceId)", instanceId)
    .put("=(userId)", userId)
    .build();
List<Entity> results = repository.queryForList(queryMap);
```

## 3. 数据传输对象（DTO）规范

### 3.1 命名规范
- **类名**：业务名称 + DTO后缀，如`BizServeInfoDto`、`AskDTO`
- **包路径**：位于xxx-api模块的dto包下
- **文件命名**：使用驼峰命名法，首字母大写

### 3.2 DTO类结构规范
```java
package com.workplat.serve.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 服务信息DTO
 * <AUTHOR>
 * @date 创建日期
 */
@Data
@Schema(description = "服务信息DTO")
public class BizServeInfoDto {
    
    @Schema(description = "服务ID")
    private String id;
    
    @NotBlank(message = "服务名称不能为空")
    @Schema(description = "服务名称")
    private String name;
    
    @Schema(description = "服务类型")
    private String type;
}
```

### 3.3 字段定义标准

#### 3.3.1 必需注解
- `@Schema`：API文档描述，必须包含description属性
- `@Data`：Lombok注解，生成getter/setter方法

#### 3.3.2 验证注解
```java
@NotBlank(message = "字段名称不能为空")    // 字符串非空验证
@NotNull(message = "字段不能为null")      // 对象非null验证
@Valid                                   // 嵌套对象验证
```

#### 3.3.3 JSON序列化注解
```java
@JSONField(name = "response_mode")       // 自定义JSON字段名
@JSONField(format = DateFormat.DATE_MINI_PATTERN)  // 日期格式化
```

### 3.4 DTO与Entity转换规范
```java
// 使用BeanUtil进行对象拷贝
BizServeInfo bizServeInfo = new BizServeInfo();
BeanUtil.copyProperties(bizServeInfoDto, bizServeInfo);

// 集合转换
List<BizServeMethod> methodList = Lists.newArrayList();
for (BizServeMethodDto methodDto : bizServeInfoDto.getMethodList()) {
    BizServeMethod method = new BizServeMethod();
    BeanUtil.copyProperties(methodDto, method);
    methodList.add(method);
}
```

## 4. 视图对象（VO）规范

### 4.1 命名规范
- **类名**：业务名称 + VO后缀，如`BizServeInfoVo`、`ComponentRunVO`
- **包路径**：位于xxx-dubbo模块的vo包下

### 4.2 VO类结构规范
```java
package com.workplat.accept.business.serve.vo;

import com.workplat.gss.common.core.annotation.dict.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "服务信息VO")
public class BizServeInfoVo implements Serializable {
    
    @Schema(description = "服务ID")
    private String id;
    
    @Schema(description = "服务名称")
    private String name;
    
    @Dict(dictCode = "serve_type")
    @Schema(description = "服务类型")
    private String type;
    
    @Schema(description = "办理方式列表")
    private List<BizServeMethodVo> methodList;
}
```

### 4.3 VO与DTO的区别和使用场景

#### 4.3.1 使用场景区别
- **DTO**：用于接收前端请求参数，进行数据传输
- **VO**：用于向前端返回数据，进行数据展示

#### 4.3.2 设计差异
```java
// DTO：关注数据传输，包含验证注解
public class BizServeInfoDto {
    @NotBlank(message = "服务名称不能为空")
    private String name;
}

// VO：关注数据展示，包含字典转换、格式化等
public class BizServeInfoVo {
    @Dict(dictCode = "serve_type")
    private String type;
    
    @JSONField(format = DateFormat.DATE_MINI_PATTERN)
    private Date createTime;
}
```

### 4.4 字典转换规范
```java
// 使用@Dict注解进行字典值转换
@Dict(dictCode = "serve_type")
@Schema(description = "服务类型")
private String type;

@Dict(dictCode = "Region")
@Schema(description = "所属区域")
private String region;
```

## 5. 模块职责划分

### 5.1 accept-biz模块职责
- **启动类**：AcceptApp.java，包含Spring Boot启动配置
- **Service实现类**：业务逻辑实现，继承BaseServiceImpl
- **Controller实现类**：API接口实现，实现xxx-api模块定义的接口
- **Repository接口**：数据访问层，继承BaseRepository
- **工具类**：utils包下的通用工具类
- **配置类**：Spring配置类、Bean定义

### 5.2 accept-api模块职责
- **API接口定义**：使用@RestController和@RequestMapping定义接口
- **DTO类**：数据传输对象，用于接收请求参数
- **请求响应模型**：API的输入输出模型定义

### 5.3 accept-dubbo模块职责
- **Dubbo服务接口**：微服务间调用的接口定义
- **VO类**：视图对象，用于数据展示
- **Entity类**：数据库实体类，使用JPA注解
- **枚举类**：业务枚举定义
- **常量类**：业务常量定义

### 5.4 层级间依赖关系
```java
// biz层调用api层接口
@RestController
public class BizServeApiImpl implements BizServeApi {
    @Autowired
    private BizServeInfoService bizServeInfoService;
}

// biz层实现dubbo层服务接口
@Service
public class BizServeInfoServiceImpl extends BaseServiceImpl<BizServeInfo>
    implements BizServeInfoService {
}
```

## 6. 代码组织结构

### 6.1 包结构标准化

#### 6.1.1 accept-biz模块包结构
```
com.workplat
├── AcceptApp.java                    // 启动类
├── accept.business                   // 业务模块
│   ├── chat                         // 聊天相关业务
│   │   ├── service                  // Service接口和实现
│   │   │   ├── Impl                 // Service实现类
│   │   │   └── ChatComponentService.java
│   │   ├── controller               // Controller实现
│   │   └── constant                 // 业务常量
│   ├── home                         // 首页相关业务
│   └── serve                        // 服务相关业务
├── conf                             // 配置相关
│   ├── component                    // 组件配置
│   └── flow                         // 流程配置
├── utils                            // 工具类
│   ├── ApiSend.java                 // API请求工具
│   ├── ImagesUtil.java              // 图片处理工具
│   └── FormJsonProcessor.java       // 表单处理工具
└── componentEngine                  // 组件引擎
    ├── constant                     // 组件常量
    └── service                      // 组件服务
```

#### 6.1.2 accept-api模块包结构
```
com.workplat
├── serve.api                        // 服务API接口
│   └── BizServeApi.java
├── serve.dto                        // 服务DTO
│   ├── BizServeInfoDto.java
│   └── BizServeTypeDto.java
├── matter.dto                       // 事项DTO
│   ├── NetworkWindowDTO.java
│   └── AuthOperationDTO.java
└── componentEngine.vo               // 组件VO
    └── BusinessTypeVO.java
```

#### 6.1.3 accept-dubbo模块包结构
```
com.workplat
├── accept.business                  // 业务实体和VO
│   ├── chat                        // 聊天相关
│   │   ├── entity                  // 实体类
│   │   ├── vo                      // VO类
│   │   └── dto                     // DTO类
│   ├── serve                       // 服务相关
│   │   ├── entity                  // 实体类
│   │   └── vo                      // VO类
│   └── home                        // 首页相关
│       ├── entity                  // 实体类
│       └── vo                      // VO类
├── accept.user                     // 用户相关
│   └── entity                      // 用户实体
└── gss.service.item.dubbo          // GSS服务
    └── matter                      // 事项相关
        ├── entity                  // 事项实体
        ├── constant                // 事项常量
        └── service                 // 事项服务接口
```

### 6.2 类文件命名规则

#### 6.2.1 Entity类命名
```java
// 命名规范：业务前缀 + 实体名称
// 数据库表名映射规范
@Entity
@Table(name = "biz_serve_info")        // 业务相关：biz_前缀
public class BizServeInfo extends BaseEntity {}

@Entity
@Table(name = "cus_home_conf")         // 定制相关：cus_前缀
public class HomeConf extends BaseEntity {}

@Entity
@Table(name = "sys_dictionary")        // 系统配置：sys_前缀
public class SysDictionary extends BaseEntity {}
```

#### 6.2.2 Service类命名
```java
// Service接口：业务名称 + Service
public interface BizServeInfoService extends BaseService<BizServeInfo> {}

// Service实现类：业务名称 + ServiceImpl
@Service
public class BizServeInfoServiceImpl extends BaseServiceImpl<BizServeInfo>
    implements BizServeInfoService {}
```

#### 6.2.3 Repository类命名
```java
// Repository接口：实体名称 + Repository
@Repository
public interface BizServeInfoRepository extends BaseRepository<BizServeInfo> {}
```

#### 6.2.4 Controller类命名
```java
// API接口：业务名称 + Api
public interface BizServeApi {}

// Controller实现：业务名称 + ApiImpl
@RestController
public class BizServeApiImpl implements BizServeApi {}
```

### 6.3 配置文件组织方式

#### 6.3.1 配置文件结构
```
accept-biz/src/main/resources/
├── bootstrap.yml                    // 启动配置
├── application.yml                  // 应用配置（如果需要）
├── application-dev.yml              // 开发环境配置
├── application-test.yml             // 测试环境配置
├── application-prod.yml             // 生产环境配置
└── static/                          // 静态资源
```

#### 6.3.2 Nacos配置中心集成
```yaml
# bootstrap.yml
spring:
  application:
    name: ai-central-platform
  profiles:
    active: dev
  cloud:
    nacos:
      config:
        server-addr: 192.168.124.215:8848
        namespace: ai-central-platform-${spring.profiles.active}
        group: ${spring.profiles.active}
        prefix: ${spring.application.name}
        file-extension: yaml
        shared-configs:
          - data-id: shared-config-mysql.yaml
            refresh: true
            group: SHARED_CONFIG
```

## 7. Service层实现规范

### 7.1 Service接口定义
```java
// 继承BaseService，获得基础CRUD方法
public interface BizServeInfoService extends BaseService<BizServeInfo> {
    // 只定义业务特有的方法
    // 基础的save、update、delete、queryById等方法由BaseService提供
}
```

### 7.2 Service实现类规范
```java
@Service
public class BizServeInfoServiceImpl extends BaseServiceImpl<BizServeInfo>
    implements BizServeInfoService {

    // 依赖注入使用@Autowired
    @Autowired
    private BizServeMethodService bizServeMethodService;

    // 业务方法实现
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveWithMethods(BizServeInfo serveInfo, List<BizServeMethod> methods) {
        // 保存主实体
        save(serveInfo);

        // 保存关联实体
        if (!methods.isEmpty()) {
            methods.forEach(method -> method.setServe(serveInfo));
            bizServeMethodService.saveList(methods);
        }
    }
}
```

### 7.3 Repository层实现规范
```java
// Repository接口继承BaseRepository
@Repository
public interface BizServeInfoRepository extends BaseRepository<BizServeInfo> {
    // 可以添加自定义查询方法
    // 基础的CRUD方法由BaseRepository提供
}
```

### 7.4 查询条件构建规范

#### 7.4.1 使用MapUtil构建查询条件（传统方式）
```java
// 使用MapUtil构建查询条件
Map<String, Object> queryMap = MapUtil.<String, Object>builder()
    .put("=(id)", id)                    // 等于条件
    .put("like(name)", name)             // 模糊查询
    .put(">(createTime)", startTime)     // 大于条件
    .put("<(createTime)", endTime)       // 小于条件
    .put("in(status)", statusList)       // IN条件
    .build();

// 执行查询
List<BizServeInfo> results = queryForList(queryMap);
Page<BizServeInfo> pageResults = queryForPage(queryMap, pageable);
```

#### 7.4.2 使用BaseQuery构建查询条件（推荐方式）
```java
// 1. 定义Query类，继承BaseQuery
@Setter
@Getter
public class InstancePushLogQuery extends BaseQuery {

    @QueryMapBuild(operator = QueryOperator.IN)
    private List<String> pushStatus;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.matterName")
    private String matterName;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.applicationName")
    private String applicationName;

    @QueryMapBuild(operator = QueryOperator.LIKE, fieldName = "instance.applicationCertificateCode")
    private String applicationCertificateCode;
}

// 2. Service层使用BaseQuery
@Override
public Page<InstancePushLogVO> queryForPage(InstancePushLogDTO dto, PageableDTO pageable) {
    InstancePushLogQuery query = new InstancePushLogQuery();

    // 设置查询条件
    if (StringUtils.isNotBlank(dto.getPushStatus())) {
        query.setPushStatus(List.of(dto.getPushStatus().split(",")));
    }
    if (StringUtils.isNotBlank(dto.getMatterName())) {
        query.setMatterName(dto.getMatterName());
    }
    if (StringUtils.isNotBlank(dto.getName())) {
        query.setApplicationName(dto.getName());
    }

    // 执行查询（BaseQuery会自动转换为Map查询条件）
    Page<InstancePushLog> entityPage = queryForPage(query.toQueryMap(), pageable.convertPageable());

    // 转换为VO
    return entityPage.map(instancePushLogConverter::convert);
}
```

#### 7.4.3 BaseQuery注解说明
```java
// @QueryMapBuild注解参数说明
@QueryMapBuild(
    operator = QueryOperator.LIKE,      // 查询操作符：LIKE、IN、EQ、GT、LT等
    fieldName = "instance.matterName"   // 字段名称，支持关联查询（可选，默认使用属性名）
)
private String matterName;

// 支持的查询操作符
QueryOperator.EQ        // 等于 =
QueryOperator.LIKE      // 模糊查询 like
QueryOperator.IN        // 包含 in
QueryOperator.GT        // 大于 >
QueryOperator.LT        // 小于 <
QueryOperator.GTE       // 大于等于 >=
QueryOperator.LTE       // 小于等于 <=
QueryOperator.NOT_NULL  // 非空 is not null
```

#### 7.4.4 BaseQuery vs MapUtil对比

| 特性 | BaseQuery | MapUtil |
|------|-----------|---------|
| 类型安全 | ✅ 编译时检查 | ❌ 运行时错误 |
| 代码可读性 | ✅ 注解清晰表达意图 | ❌ 字符串拼接易错 |
| 重用性 | ✅ Query类可复用 | ❌ 每次手动构建 |
| 维护性 | ✅ 集中管理查询条件 | ❌ 分散在各处 |
| 关联查询 | ✅ 支持fieldName指定 | ❌ 需要手动拼接 |

**推荐使用BaseQuery方式进行复杂查询条件构建，简单查询可继续使用MapUtil方式。**

## 8. Controller层实现规范

### 8.1 API接口定义规范
```java
@Tag(name = "服务相关接口")
@RestController
@RequestMapping(value = "/api/serve")
public interface BizServeApi {

    @Operation(summary = "服务列表")
    @PostMapping("/queryListByName")
    ResponseData<Page<BizServeInfoVo>> queryListByName(
        @RequestBody(required = false) KeyDto keyDto,
        PageableDTO pageDTO);

    @Operation(summary = "根据id查询服务")
    @GetMapping("/getById")
    ResponseData<BizServeInfoVo> getById(String id);
}
```

### 8.2 Controller实现类规范
```java
@Slf4j
@RestController
public class BizServeApiImpl implements BizServeApi {

    @Autowired
    private BizServeInfoService bizServeInfoService;

    @Autowired
    private BizServeInfoConverter bizServeInfoConverter;

    @Override
    public ResponseData<BizServeInfoVo> getById(String id) {
        try {
            BizServeInfo bizServeInfo = bizServeInfoService.queryById(id);
            BizServeInfoVo vo = bizServeInfoConverter.convert(bizServeInfo);
            return ResponseData.success("获取成功", vo);
        } catch (Exception e) {
            log.error("查询服务信息失败，id: {}", id, e);
            return ResponseData.error("查询失败");
        }
    }
}
```

### 8.3 响应数据规范
```java
// 统一使用ResponseData包装响应
public class ResponseData<T> {
    private int code;           // 状态码
    private boolean success;    // 是否成功
    private String message;     // 响应信息
    private String errorMessage; // 错误信息
    private T data;            // 响应数据

    // 成功响应
    public static <T> ResponseData<T> success(String message, T data) {
        return new ResponseData<>(200, message, data);
    }

    // 失败响应
    public static <T> ResponseData<T> error(String message) {
        return new ResponseData<>(500, message, null);
    }
}
```

## 9. 异常处理和事务管理

### 9.1 异常处理规范
```java
// 业务异常使用BusinessException
if (StringUtils.isBlank(id)) {
    throw new BusinessException("服务ID不能为空");
}

// Controller层异常处理
@Override
public ResponseData<BizServeInfoVo> getById(String id) {
    try {
        // 业务逻辑
        return ResponseData.success("获取成功", vo);
    } catch (BusinessException e) {
        log.warn("业务异常：{}", e.getMessage());
        return ResponseData.error(e.getMessage());
    } catch (Exception e) {
        log.error("系统异常", e);
        return ResponseData.error("系统异常，请联系管理员");
    }
}
```

### 9.2 事务管理规范
```java
// Service层方法添加事务注解
@Transactional(rollbackFor = Exception.class)
@Override
public void saveWithRelations(BizServeInfo serveInfo) {
    // 事务方法实现
    save(serveInfo);
    // 其他相关操作
}

// 幂等性控制
@Transactional(rollbackFor = Exception.class)
@Idempotent(expireTime = 5)
@Override
public ResponseData<List<BizInstanceQuotaVO>> save(List<BizInstanceQuotaDto> dto) {
    // 幂等性保护的方法实现
}
```

## 10. 常量和枚举定义规范

### 10.1 常量类定义
```java
// 业务常量接口
public interface HomeConfConstant {
    String TAB = "0";
    String MENU = "1";
    String NOT_DELETED = "0";
    String DELETED = "1";
}

// 组件引擎常量类
public final class ComponentEngineCode {
    private ComponentEngineCode() {
        // 私有构造函数，防止实例化
    }

    // 材料相关组件
    public static final String MATERIAL_UPLOAD = "MaterialUpload";
    public static final String MATERIAL_GROUP = "MaterialGroup";

    // 表单相关组件
    public static final String FIELD_SHOW = "FieldExtraction";
    public static final String FORM_GROUP = "FormGroup";
}
```

### 10.2 枚举类定义规范
```java
@AllArgsConstructor
@Getter
@DictGroup(group = DictGroupConstant.BUSINESS, name = "绑定类型", code = "INSTANCE_BINDING_TYPE")
public enum BindingTypeEnum {

    @DictCode
    FIELD("字段"),
    @DictCode
    MATTER("事项"),
    @DictCode
    MATERIAL("材料");

    @DictName
    private String name;
}
```

## 11. 数据转换和映射规范

### 11.1 Converter类实现
```java
@Component
public class BizServeInfoConverter implements BaseConverter<BizServeInfo, BizServeInfoVo> {

    @Autowired
    private BizServeMethodConverter bizServeMethodConverter;

    @Override
    public BizServeInfoVo convert(BizServeInfo source) {
        BizServeInfoVo vo = new BizServeInfoVo();
        BeanUtil.copyProperties(source, vo);

        // 转换关联对象
        if (!source.getMethodList().isEmpty()) {
            List<BizServeMethodVo> methodVos = bizServeMethodConverter.convert(source.getMethodList());
            vo.setMethodList(methodVos);
        }

        return vo;
    }
}
```

### 11.2 数据转换最佳实践
```java
// 单对象转换
BizServeInfoVo vo = bizServeInfoConverter.convert(entity);

// 集合转换
List<BizServeInfoVo> voList = entities.stream()
    .map(bizServeInfoConverter::convert)
    .collect(Collectors.toList());

// 分页对象转换
Page<BizServeInfo> entityPage = bizServeInfoService.queryForPage(queryMap, pageable);
Page<BizServeInfoVo> voPage = entityPage.map(bizServeInfoConverter::convert);
```

## 12. 数据库表命名和实体映射规范

### 12.1 数据库表命名规范
| 功能模块 | 数据库表名前缀 | 实体类名前缀 | 路径前缀 |
|:----:|:-------:|:------:|:------------------:|
| 系统配置 | sys_ | Sys | /system/* |
| 业务配置 | conf_ | Conf | /conf/* |
| 业务相关 | biz_ | Biz | /business/* |
| 日志相关 | log_ | Log | /system/log/* |
| 定制相关 | cus_ | Cus | /business/custom/* |

### 12.2 实体类注解规范
```java
@Entity
@Table(name = "biz_serve_info", indexes = {
    @Index(name = "idx_code", columnList = "code"),
    @Index(name = "idx_deleted", columnList = "deleted")
})
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class BizServeInfo extends BaseEntity {

    @Comment("服务名称")
    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @Comment("服务编码")
    @Column(name = "code", length = 50, unique = true)
    private String code;

    @OneToMany(mappedBy = "serve", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Where(clause = "deleted = 0")
    private List<BizServeMethod> methodList = new ArrayList<>();
}
```

---

## 附录：代码示例和最佳实践

### A.1 完整的CRUD实现示例
参考项目中的BizServeInfo相关类的实现，包括Entity、DTO、VO、Service、Repository、Controller的完整实现。

### A.2 组件引擎实现示例
参考项目中的ComponentEngine相关实现，了解组件化架构的设计模式。

### A.3 配置管理示例
参考项目中的Nacos配置中心集成和多环境配置管理实现。

### A.4 工具类使用示例
```java
// 使用MapUtil构建查询条件
Map<String, Object> queryMap = MapUtil.<String, Object>builder()
    .put("=(instanceId)", instanceId)
    .put("like(name)", "%" + name + "%")
    .build();

// 使用BeanUtil进行对象拷贝
BeanUtil.copyProperties(source, target);

// 使用RedisUtil进行缓存操作
redisUtil.set("key", value, 3600);
Object cachedValue = redisUtil.get("key");
```

### A.5 日志记录规范
```java
@Slf4j
public class BizServeApiImpl {

    @Override
    public ResponseData<BizServeInfoVo> getById(String id) {
        log.info("查询服务信息，id: {}", id);

        try {
            // 业务逻辑
            log.debug("查询结果：{}", result);
            return ResponseData.success("获取成功", vo);
        } catch (BusinessException e) {
            log.warn("业务异常：{}", e.getMessage());
            return ResponseData.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询服务信息失败，id: {}", id, e);
            return ResponseData.error("系统异常");
        }
    }
}
```

---

**文档版本**：v1.0
**最后更新**：2025-08-12
**适用项目**：AI政务智能中枢平台 (ai-central-platform)
