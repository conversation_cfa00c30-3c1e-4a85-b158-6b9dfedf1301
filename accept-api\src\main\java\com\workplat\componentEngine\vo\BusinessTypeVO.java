package com.workplat.componentEngine.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务类型数据模型
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "业务类型数据模型")
public class BusinessTypeVO {

    @Schema(description = "业务类型编码")
    private String code;
    
    @Schema(description = "业务类型名称")
    private String name;
    
    @Schema(description = "业务类型描述")
    private String description;
} 