package com.workplat.accept.business.chat.service;

import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.vo.BizChatConversationVO;
import com.workplat.gss.common.core.service.BaseService;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 会话服务接口
 */
public interface BizChatConversationService extends BaseService<BizChatConversation> {


    /**
     * 创建新会话
     *
     * @param conversation 会话对象
     * @return 创建后的会话对象
     */
    BizChatConversation createConversation(BizChatConversation conversation);

    void updateConversation(String conversationId);
} 