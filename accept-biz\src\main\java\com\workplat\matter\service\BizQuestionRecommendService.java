package com.workplat.matter.service;

import com.workplat.gss.common.core.service.BaseService;
import com.workplat.matter.entity.BizQuestionRecommend;

import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.service
 * @description 问题推荐service
 * @date 2025/5/26 16:42
 */
public interface BizQuestionRecommendService extends BaseService<BizQuestionRecommend> {

    /**
     * 问题推荐清单
     * @return
     */
    Map<String, String[]> questionList();
}
