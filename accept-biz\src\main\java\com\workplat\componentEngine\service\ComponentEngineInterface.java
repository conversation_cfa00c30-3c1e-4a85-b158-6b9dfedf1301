package com.workplat.componentEngine.service;


import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;

public interface ComponentEngineInterface {


    /**
     * 加载组件
     *
     * @return 加载结果
     */
    ComponentRunVO execute(ComponentDataContext componentDataContext);

    /**
     * 填充数据
     * @param componentDataContext 上下文
     */
    void fillData(ComponentDataContext componentDataContext);

    /**
     * 获取下一个指令
     * @param componentDataContext 上下文
     * @return 指令
     */
    String getNextInstruction(ComponentDataContext componentDataContext);
}
