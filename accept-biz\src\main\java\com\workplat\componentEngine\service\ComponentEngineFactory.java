package com.workplat.componentEngine.service;

import com.workplat.accept.business.chat.dto.ChatProcessDTO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.componentEngine.engine.AbstractComponentEngine;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.conf.component.entity.ConfComponent;
import com.workplat.conf.component.service.ConfComponentService;
import com.workplat.utils.ChatCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class ComponentEngineFactory {

    private final List<AbstractComponentEngine> allEngines;
    private final ConfComponentService confComponentService;
    private final ChatCacheUtil chatCacheUtil;

    public ComponentEngineFactory(List<AbstractComponentEngine> componentEngines,
                                  ConfComponentService confComponentService,
                                  ChatCacheUtil chatCacheUtil) {
        this.allEngines = componentEngines;
        this.confComponentService = confComponentService;
        this.chatCacheUtil = chatCacheUtil;
    }

    /**
     * 根据上下文获取对应的组件引擎
     */
    public Optional<ComponentEngineInterface> getEngine(ComponentDataContext context) {
        // 只要遍历所有引擎，依赖canHandle方法的高效实现
        for (AbstractComponentEngine engine : allEngines) {
            if (engine.canHandle(context)) {
                return Optional.of(engine);
            }
        }

        return Optional.empty();
    }

    public Object run(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        return getEngine(componentDataContext)
                .map(engine -> engine.execute(componentDataContext))
                .orElse(null);
    }

    @NotNull
    private ComponentDataContext getComponentDataContext(ComponentRunDTO componentRunDto) {
        ConfComponent component = confComponentService.getByCode(componentRunDto.getComponentCode());
        ComponentDataContext componentDataContext = new ComponentDataContext();
        componentDataContext.setConfComponent(component);
        componentDataContext.setRecordId(componentRunDto.getRecordId());
        componentDataContext.setInputs(componentRunDto.getInputs());
        // 获取缓存的流程信息, 用于获取实例ID
        if (StringUtils.isBlank(componentRunDto.getInstanceId())) {
            ChatProcessDTO chatProcessDTO = chatCacheUtil.get(componentRunDto.getRecordId());
            componentRunDto.setInstanceId(chatProcessDTO.getInstanceId());
        }
        componentDataContext.setInstanceId(componentRunDto.getInstanceId());
        componentDataContext.setSubmitData(componentRunDto.getSubmitData());
        return componentDataContext;
    }

    public void backFillData(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        getEngine(componentDataContext)
                .ifPresent(engine -> engine.fillData(componentDataContext));
    }

    public Object getNextInstruction(ComponentRunDTO componentRunDto) {
        ComponentDataContext componentDataContext = getComponentDataContext(componentRunDto);
        return getEngine(componentDataContext)
                .map(engine -> engine.getNextInstruction(componentDataContext))
                .orElse(null);
    }
}
