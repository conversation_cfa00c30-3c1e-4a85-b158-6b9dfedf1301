package com.workplat.serviceZone.service;

import com.workplat.accept.business.serviceZone.entity.ConfServiceItemZoneRelation;
import com.workplat.gss.common.core.service.BaseService;

import java.util.List;

/**
 * @Author: yangfan
 * @Date: 2025/8/26
 * @Description: 服务清单专区关联Service接口
 */
public interface ConfServiceItemZoneRelationService extends BaseService<ConfServiceItemZoneRelation> {

    /**
     * 根据专区ID查询关联的服务清单
     * @param serviceZoneId 专区ID
     * @return 关联关系列表
     */
    List<ConfServiceItemZoneRelation> getByServiceZoneId(String serviceZoneId);

    /**
     * 根据服务清单ID查询关联的专区
     * @param serviceItemId 服务清单ID
     * @return 关联关系列表
     */
    List<ConfServiceItemZoneRelation> getByServiceItemId(String serviceItemId);

    /**
     * 根据专区ID查询关联关系（包含完整详情，用于VO转换）
     * @param serviceZoneId 专区ID
     * @return 关联关系列表
     */
    List<ConfServiceItemZoneRelation> getByServiceZoneIdWithDetails(String serviceZoneId);

    /**
     * 根据服务清单ID查询关联关系（包含完整详情，用于VO转换）
     * @param serviceItemId 服务清单ID
     * @return 关联关系列表
     */
    List<ConfServiceItemZoneRelation> getByServiceItemIdWithDetails(String serviceItemId);

    /**
     * 批量保存服务清单与专区的关联关系
     * @param serviceItemId 服务清单ID
     * @param serviceZoneIds 专区ID列表
     */
    void saveRelations(String serviceItemId, List<String> serviceZoneIds);

    /**
     * 批量保存专区与服务清单的关联关系
     * @param serviceZoneId 专区ID
     * @param serviceItemIds 服务清单ID列表
     */
    void saveRelationsByZone(String serviceZoneId, List<String> serviceItemIds);

    /**
     * 删除专区下的所有关联关系
     * @param serviceZoneId 专区ID
     */
    void deleteByServiceZoneId(String serviceZoneId);

    /**
     * 删除服务清单的所有关联关系
     * @param serviceItemId 服务清单ID
     */
    void deleteByServiceItemId(String serviceItemId);

    /**
     * 更新关联关系排序
     * @param id 关联关系ID
     * @param sort 排序值
     */
    void updateSort(String id, Integer sort);

    /**
     * 批量更新关联关系排序
     * @param relations 关联关系列表
     */
    void batchUpdateSort(List<ConfServiceItemZoneRelation> relations);
}