package com.workplat.accept.business.accept.api.Impl;

import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.accept.api.BizInstanceFieldsCustomizeApi;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.utils.FormFieldFilterUtil;
import com.workplat.utils.FormStepProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Controller
@Transactional(rollbackFor = Exception.class)
public class BizInstanceFieldsCustomizeApiImpl implements BizInstanceFieldsCustomizeApi {

    private final BizInstanceFieldsService bizInstanceFieldsService;

    public BizInstanceFieldsCustomizeApiImpl(BizInstanceFieldsService bizInstanceFieldsService) {
        this.bizInstanceFieldsService = bizInstanceFieldsService;
    }

    @ApiLogging(operation = "获取表单域字段映射", module = "申报表单模块-自定义", type = OperationType.QUERY)
    @Override
    public ResponseData<Map<String, String>> getFormAreaMapping(String instanceId, String title) {
        Map<String, String> formAreaFields = new HashMap<>();
        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(instanceId);
        if (bizInstanceFieldsVO != null) {
            try {
                JSONObject formJson = FormStepProcessor.getComponentByTitle(bizInstanceFieldsVO.getFormJson(), title);
                formAreaFields = FormStepProcessor.extractFormFieldsMap(JSONObject.toJSONString(formJson));
            } catch (Exception e) {
                log.error("getFormAreaMapping error: {}", e.getMessage());
            }
        }
        return ResponseData.success(formAreaFields);
    }
}
