package com.workplat.accept.user.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.workplat.accept.business.home.constant.HomeConfConstant;
import com.workplat.accept.user.entity.SsoUser;
import com.workplat.accept.user.service.SsoUserService;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.admin.modules.authencation.service.AuthUserService;
import com.workplat.gss.admin.modules.authencation.vo.UserVO;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Odin
 * @Date: 2024/9/23 11:17
 * @Description:
 */

@Slf4j
@Service
public class SsoUserServiceImpl extends BaseServiceImpl<SsoUser> implements SsoUserService {

    @Resource
    private AuthUserService authUserService;

    @Override
    public SsoUser getUserInfoByUserId(String userId) {
        UserVO user = authUserService.getUser(userId);
        SsoUser ssoUser = SsoUser.builder().userId(user.getId())
                .username(user.getUsername())
                .isWorker("1")
                .realName(user.getRealName())
                .gender(user.getGender())
                .certificateType(user.getCertificateType())
                .certificateNumber(user.getCertificateNumber())
                .email(user.getEmail())
                .officePhone(user.getOfficePhone())
                .officePhoneShort(user.getOfficePhoneShort())
                .officeFax(user.getOfficeFax())
                .officeAddress(user.getOfficeAddress())
                .hireDate(user.getHireDate())
                .staffNo(user.getStaffNo())
                .introduction(user.getIntroduction())
                .position(user.getPosition())
                .birthday(user.getBirthday()).build();
        SsoUser tmpSsoUser = queryForSingle(MapUtil.<String, Object>builder().put("=(userId)", userId).build());
        if (ObjectUtil.isNull(tmpSsoUser)) {
            save(ssoUser);
        } else {
            ssoUser.setId(tmpSsoUser.getId());
            update(ssoUser);
        }
        return ssoUser;
    }

    @Override
    public SsoUser loadByUserId(String userId) {
        return queryForSingle(MapUtil.<String, Object>builder().put("=(userId)", userId).build());
    }

    @Override
    public void saveUser(SsoUser ssoUser) {
        save(ssoUser);
    }

    @Override
    public String getANetToken(String name, String idCard) {
        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", idCard).put("name", name).build();
        return JSONUtil.parseObj(GatewayUtils.getAllinoneApiResult("/api/zzj/selfServiceLogin", formMap)).getStr("token");
    }
}
