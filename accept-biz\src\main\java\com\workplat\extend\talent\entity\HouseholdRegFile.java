package com.workplat.extend.talent.entity;

import com.workplat.gss.common.core.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;

/**
 * <AUTHOR> cheng
 * @package com.workplat.extend.entity
 * @description 人才落户登记表材料
 * @date 2025/7/2 14:45
 */
@Setter
@Getter
@Entity
@Table(name = "household_reg_file")
public class HouseholdRegFile extends BaseEntity {


    @Comment("用户姓名")
    @Column(name = "name", length = 32)
    private String name;

    @Comment("用户身份证号")
    @Column(name = "card_id", length = 32)
    private String cardId;

    @Comment("材料id")
    @Column(name = "file_id", length = 32)
    private String fileId;

    @Lob
    @Comment("字段信息")
    @Column(name = "data_json", columnDefinition = "LONGTEXT")
    private String dataJson;
}
