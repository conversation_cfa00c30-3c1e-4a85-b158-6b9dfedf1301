//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import com.alibaba.fastjson2.JSONObject;
import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 档案申请办件推送
 */
public class ArchivesDistributionDriverLoaderImpl implements DistributionDriverLoader {

    Logger log = LoggerFactory.getLogger(ArchivesDistributionDriverLoaderImpl.class);


    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {
        DistributionDriverOutput output = new DistributionDriverOutput();
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceFieldsService bizInstanceFieldsService = ApplicationContextUtil.getBean(BizInstanceFieldsService.class);
        RestTemplate restTemplate = ApplicationContextUtil.getBean(RestTemplate.class);

        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(input.getInstanceId());

        BizInstanceFieldsVO bizInstanceFieldsVO = bizInstanceFieldsService.queryByInstanceId(input.getInstanceId());
        try {
            JSONObject formObj = JSONObject.parseObject(bizInstanceFieldsVO.getFormObj());
            formObj.remove("applicationDate");

            Map<Object, Object> bodyMap = Map.of("typeId", getTypeId(bizInstanceInfo.getMatterCode()),
                    "archiveParams", formObj,
                    "cardId", bizInstanceInfo.getApplicationCertificateCode());
            GatewayUtils.executePostToBodyRequest("allinone-api/api/archives/applyArchiveByCardId", null, JSONObject.toJSONString(bodyMap), false, false);
//            restTemplate.postForObject("http://192.168.124.241/api/archives/applyArchiveByCardId", bodyMap, String.class);
        } catch (Exception e) {
            output.setSuccess(false);
            output.setMsg(StringUtils.substring(ExceptionUtils.getStackTrace(e), 0, 6000));
        }
        return output;
    }


    String getTypeId(String matterCode) {
        return switch (matterCode) {
            case "twsbdacx" -> "12a7ebf2c5ea474fa1a0d068fb0a54ce";
            case "sydacx" -> "42dc436b21334a4eb4b058068b6f8db3";
            case "sqzsydacx" -> "710b9e1c4a46489c90288560b7ab4be2";
            case "dszvdacx" -> "a5c0a72792fa44fd8d3575f6a0a98460";
            case "hydjdacx" -> "b893ea029762428fb6c9db2cb2ed8b0d";
            case "csyxzmcx" -> "cc9a13e300fa4c0599201ee6fede994f";
            default -> throw new BusinessException("事项编码不支持");
        };
    }

}