package com.workplat.accept.business.third.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.workplat.gss.common.core.constant.DateFormat;
import jakarta.persistence.Column;
import lombok.Builder;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: Odin
 * @Date: 2024/9/26 10:53
 * @Description:
 */
@Data
@Builder
public class ThirdDataVO {

    private List<Content> content;
    private ThirdPageable pageable;
    private ThirdSort sort;

    private boolean last;
    private int totalPages;
    private long totalElements;
    private boolean first;
    private int numberOfElements;
    private int size;
    private int number;
    private boolean empty;

    @Data
    public static class Content{
        // 申请事项类型
        private String applyName;

        // 合同编号
        private String contractNo;

        // 业务编号
        private String businessNo;

        // 申请时间
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
        private Date applyTime;

        // 申请人
        private String applyPerson;

        // 审核意见
        private String auditRemark;

        // 业务id => 不动产：businessId，一件事：onethingId
        private String objectId;

        // 受理状态
        private String acceptStatus;

        // 坐落
        private String address;

        // 业务状态 审核中 已通过 已退回
        private String applyStatus;

        // 跳转链接
        private String skipUrl;

        // 预留字段01
        private String remark01;

        // 预留字段02
        private String remark02;

        private String btnName;
        private String btnSkipUrl;

    }

    @Data
    public static class ThirdPageable{
        private ThirdSort sort;
        private int pageNumber;
        private int pageSize;
        private long offset;
        private boolean paged;
        private boolean unpaged;
    }

    @Data
    public static class ThirdSort {
        private boolean unsorted;
        private boolean sorted;
        private boolean empty;
    }

}
