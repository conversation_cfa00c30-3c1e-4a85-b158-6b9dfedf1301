package com.workplat.accept.business.chat.constant;

import com.workplat.gss.common.core.annotation.dict.DictCode;
import com.workplat.gss.common.core.annotation.dict.DictGroup;
import com.workplat.gss.common.core.annotation.dict.DictName;
import com.workplat.gss.common.core.constant.DictGroupConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@Getter
@Slf4j
@DictGroup(group = DictGroupConstant.BUSINESS, name = "节点状态", code = "NODE_STATUS")
public enum NodeStatusEnum {

    @DictCode
    DRAFT_ACCEPT("草稿"),
    @DictCode
    NO_ACCEPT("未受理"),
    @DictCode
    FINISH_ACCEPT("已完成"),
    @DictCode
    REJECT_BUSINESS("业务退回"),
    @DictCode
    BUSINESS_ACCEPTING("办理中");

    @DictName
    private String value;

    public static NodeStatusEnum getByValue(String value) {
        for (NodeStatusEnum nodeStatusEnum : NodeStatusEnum.values()) {
            if (nodeStatusEnum.value.equals(value)) {
                return nodeStatusEnum;
            }
        }
        return null;
    }
}
