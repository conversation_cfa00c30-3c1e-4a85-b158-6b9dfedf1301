package com.workplat.gss.application.biz.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Multimaps;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.biz.service.BizInstanceNetWorkWindowService;
import com.workplat.gss.application.dubbo.dto.BizInstanceQuotaDto;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceOption;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.*;
import com.workplat.gss.application.dubbo.vo.*;
import com.workplat.gss.common.core.annotation.Idempotent;
import com.workplat.gss.common.core.autowired.GssAutowired;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.script.model.instance.QuotaBindInput;
import com.workplat.gss.common.script.model.instance.QuotaBindOutput;
import com.workplat.gss.common.script.model.instance.QuotaVerifyInput;
import com.workplat.gss.common.script.model.instance.QuotaVerifyOutput;
import com.workplat.gss.script.dubbo.constants.ScriptConstants;
import com.workplat.gss.script.dubbo.loader.ScriptRunnerService;
import com.workplat.gss.script.dubbo.service.ConfScriptManageBindService;
import com.workplat.gss.service.item.dubbo.matter.constant.BindingStatusEnum;
import com.workplat.gss.service.item.dubbo.matter.constant.BindingTypeEnum;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatterPublic;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


@Slf4j
@Controller
public class BizInstanceQuotaApiImpl implements BizInstanceSituationApi {

    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;
    @GssAutowired
    private ConfMatterPublicService confMatterPublicService;
    @Autowired
    private BizInstanceQuotaService bizInstanceQuotaService;
    @Autowired
    private BizInstanceOptionService bizInstanceOptionService;
    @Autowired
    private BizInstanceMaterialService bizInstanceMaterialService;
    @Autowired
    private BizInstanceDocumentService bizInstanceDocumentService;
    @Autowired
    private BizInstanceResultService bizInstanceResultService;
    @Autowired
    private BizInstanceNetWorkWindowService bizInstanceNetWorkWindowService;
    @Autowired
    private BizInstanceFieldsService bizInstanceFieldsService;
    @Autowired
    private ScriptRunnerService scriptRunnerService;
    @Autowired
    private ConfScriptManageBindService confScriptManageBindService;

    // 创建线程池
    static ExecutorService executor = new ThreadPoolExecutor(4, 20,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>());

    @PreDestroy
    public void destroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Idempotent(expireTime = 5)
    @Override
    public ResponseData<List<BizInstanceQuotaVO>> save(List<BizInstanceQuotaDto> dto) {
        String instanceId = dto.get(0).getInstanceId();
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        String matterPublicId = bizInstanceInfo.getMatterPublicId();
        ConfMatterPublic confMatterPublic = confMatterPublicService.queryById(matterPublicId);
        // 事项配置的情形列表
        List<ConfMatterAcceptQuotaVO> matterAcceptQuotaVOList = JSON.parseArray(confMatterPublic.getSituationJson(), ConfMatterAcceptQuotaVO.class);

        //<editor-fold desc="脚本-指标校验">
        try {
            // 拿到脚本指标绑定脚本id
            ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = matterAcceptQuotaVOList.get(0);
            if (confMatterAcceptQuotaVO.getIsBindAfterScript() != null && confMatterAcceptQuotaVO.getIsBindAfterScript()) {
                String quotaVerifyScriptId = confMatterAcceptQuotaVO.getAfterScripts() == null ? "" : confMatterAcceptQuotaVO.getAfterScripts();
                String[] quotaVerifyScriptIds = quotaVerifyScriptId.split(",");
                QuotaVerifyInput quotaVerifyInput = new QuotaVerifyInput();
                quotaVerifyInput.setInstanceId(instanceId);
                quotaVerifyInput.setQuotaDtoJson(JSON.toJSONString(dto));
                for (String scriptId : quotaVerifyScriptIds) {
                    QuotaVerifyOutput quotaVerifyOutput = scriptRunnerService.run(scriptId
                            , ScriptConstants.ScriptType.QUOTA_VERIFY.getMethod()
                            , QuotaVerifyInput.class
                            , quotaVerifyInput);
                    if (quotaVerifyOutput != null && !quotaVerifyOutput.isSuccess()) {
                        return ResponseData.error().message(quotaVerifyOutput.getMsg()).build();
                    }
                }
            }

        } catch (Exception e) {
        }
        //</editor-fold>

        // 删除instance情形
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("=(instanceId)", instanceId);
        bizInstanceQuotaService.deleteByParams(hashMap);

        // 重新保存instance情形
        List<BizInstanceQuota> bizInstanceQuotaList = BeanUtil.copyToList(dto, BizInstanceQuota.class);
        recursiveSave(bizInstanceQuotaList, null, instanceId);
        List<BizInstanceQuota> newEntity = bizInstanceQuotaService.queryForList(hashMap);

        // 已选中情形选项平铺列表
        List<BizInstanceOption> optionFlatList = new ArrayList<>();
        recursiveList(newEntity, optionFlatList);

        // 事项配置情形指标平铺列表
        List<ConfMatterAcceptQuotaVO> matterAcceptQuotaVOFlatList = new ArrayList<>();
        recursive(matterAcceptQuotaVOList, matterAcceptQuotaVOFlatList);

        // 生成所有情形选项optionId对应选项绑定对象列表
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> materialBindsMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> fieldBindsMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> documentBindsMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> matterBindsMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> netWorkWindowBindsMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        for (ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO : matterAcceptQuotaVOFlatList) {
            List<ConfMatterAcceptOptionVO> options = confMatterAcceptQuotaVO.getOptions();
            if (CollectionUtil.isNotEmpty(options)) {
                for (ConfMatterAcceptOptionVO option : options) {
                    if (CollectionUtil.isNotEmpty(option.getBinding())) {
                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : option.getBinding()) {
                            if (BindingTypeEnum.MATERIAL.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                materialBindsMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
                            }
                            if (BindingTypeEnum.FIELD.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                fieldBindsMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
                            }
                            if (BindingTypeEnum.DOCUMENT.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                documentBindsMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
                            }
                            if (BindingTypeEnum.MATTER.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                matterBindsMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
                            }
                            if (BindingTypeEnum.NetWorkWindow.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                netWorkWindowBindsMap.put(confMatterAcceptOptionBindingVO.getOptionId(), confMatterAcceptOptionBindingVO);
                            }
                        }
                    }
                }
            }
        }

        // 生成所有选择选项的绑定内容id对应绑定类型列表
        ListMultimap<String, String> allMaterialMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> allFieldMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> allDocumentMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> allMatterMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> allNetWorkWindowMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        for (ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO : matterAcceptQuotaVOFlatList) {
            List<ConfMatterAcceptOptionVO> options = confMatterAcceptQuotaVO.getOptions();
            if (CollectionUtil.isNotEmpty(options)) {
                for (ConfMatterAcceptOptionVO option : options) {
                    if (CollectionUtil.isNotEmpty(option.getBinding())) {
                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : option.getBinding()) {
                            if (BindingTypeEnum.MATERIAL.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                allMaterialMap.put(confMatterAcceptOptionBindingVO.getContentId(), confMatterAcceptOptionBindingVO.getBindingType());
                            }
                            if (BindingTypeEnum.FIELD.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                allFieldMap.put(confMatterAcceptOptionBindingVO.getContentId(), confMatterAcceptOptionBindingVO.getBindingType());
                            }
                            if (BindingTypeEnum.DOCUMENT.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                allDocumentMap.put(confMatterAcceptOptionBindingVO.getContentId(), confMatterAcceptOptionBindingVO.getBindingType());
                            }
                            if (BindingTypeEnum.MATTER.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                allMatterMap.put(confMatterAcceptOptionBindingVO.getContentId(), confMatterAcceptOptionBindingVO.getBindingType());
                            }
                            if (BindingTypeEnum.NetWorkWindow.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                allNetWorkWindowMap.put(confMatterAcceptOptionBindingVO.getContentId(), confMatterAcceptOptionBindingVO.getBindingType());
                            }
                        }
                    }
                }
            }
        }

        // 生成已选择选项的绑定内容id对应绑定类型列表
        ListMultimap<String, String> selectedMaterialMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> selectedFieldMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> selectedDocumentMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> selectedMatterMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        ListMultimap<String, String> selectedNetWorkWindowMap = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        for (BizInstanceOption bizInstanceOption : optionFlatList) {
            List<ConfMatterAcceptOptionBindingVO> materialList = materialBindsMap.get(bizInstanceOption.getOptId());
            for (ConfMatterAcceptOptionBindingVO bindingVO : materialList) {
                selectedMaterialMap.put(bindingVO.getContentId(), bindingVO.getBindingType());
            }
            List<ConfMatterAcceptOptionBindingVO> fieldList = fieldBindsMap.get(bizInstanceOption.getOptId());
            for (ConfMatterAcceptOptionBindingVO bindingVO : fieldList) {
                selectedFieldMap.put(bindingVO.getContentId(), bindingVO.getBindingType());
            }
            List<ConfMatterAcceptOptionBindingVO> documentList = documentBindsMap.get(bizInstanceOption.getOptId());
            for (ConfMatterAcceptOptionBindingVO bindingVO : documentList) {
                selectedDocumentMap.put(bindingVO.getContentId(), bindingVO.getBindingType());
            }
            List<ConfMatterAcceptOptionBindingVO> matterList = matterBindsMap.get(bizInstanceOption.getOptId());
            for (ConfMatterAcceptOptionBindingVO bindingVO : matterList) {
                selectedMatterMap.put(bindingVO.getContentId(), bindingVO.getBindingType());
            }
            List<ConfMatterAcceptOptionBindingVO> netWorkWindowList = netWorkWindowBindsMap.get(bizInstanceOption.getOptId());
            for (ConfMatterAcceptOptionBindingVO bindingVO : netWorkWindowList) {
                selectedNetWorkWindowMap.put(bindingVO.getContentId(), bindingVO.getBindingType());
            }
        }


        // 筛选出绑定对象最终增加与删除结果
        List<String> materialAddIds = new ArrayList<>();
        List<String> materialDelIds = new ArrayList<>();
        filterFinalAdditionDelAndAddResults(instanceId, materialAddIds, materialDelIds
                , allMaterialMap, selectedMaterialMap, "材料");

        List<String> fieldAddIds = new ArrayList<>();
        List<String> fieldDelIds = new ArrayList<>();
        filterFinalAdditionDelAndAddResults(instanceId, fieldAddIds, fieldDelIds
                , allFieldMap, selectedFieldMap, "字段");

        List<String> documentAddIds = new ArrayList<>();
        List<String> documentDelIds = new ArrayList<>();
        filterFinalAdditionDelAndAddResults(instanceId, documentAddIds, documentDelIds
                , allDocumentMap, selectedDocumentMap, "文书");

        List<String> matterAddIds = new ArrayList<>();
        List<String> matterDelIds = new ArrayList<>();
        filterFinalAdditionDelAndAddResults(instanceId, matterAddIds, matterDelIds
                , allMatterMap, selectedMatterMap, "事项");

        List<String> netWorkWindowAddIds = new ArrayList<>();
        List<String> netWorkWindowDelIds = new ArrayList<>();
        filterFinalAdditionDelAndAddResults(instanceId, netWorkWindowAddIds, netWorkWindowDelIds
                , allNetWorkWindowMap, selectedNetWorkWindowMap, "网点");

        // 注册事务提交同步回调
        registerSynchronization(instanceId, materialAddIds, materialDelIds, fieldAddIds, fieldDelIds
                , documentAddIds, documentDelIds, matterAddIds, matterDelIds, netWorkWindowAddIds, netWorkWindowDelIds);

        return ResponseData.success("保存成功", BeanUtil.copyToList(newEntity, BizInstanceQuotaVO.class));
    }

    @Override
    public ResponseData<BizInstanceSituationVO> query(String instanceId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("=(instanceId)", instanceId);
        List<BizInstanceQuota> newEntity = bizInstanceQuotaService.queryForList(hashMap);
        List<BizInstanceQuotaVO> bizInstanceQuotaVOS = BeanUtil.copyToList(newEntity, BizInstanceQuotaVO.class);
        BizInstanceSituationVO bizInstanceSituationVO = new BizInstanceSituationVO();
        Collections.reverse(bizInstanceQuotaVOS);
        bizInstanceSituationVO.setQuotaVOList(bizInstanceQuotaVOS);
        List<BizInstanceResultVO> instanceResult = bizInstanceResultService.getInstanceResult(instanceId);
        String matterName = instanceResult.get(0).getMatterName();
        HashMap<String, List<BizInstanceResultVO>> map = new HashMap<>();
        map.put(matterName, instanceResult);
        bizInstanceSituationVO.setResult(map);
        List<BizInstanceMaterialVO> instanceMaterial = bizInstanceMaterialService.getInstanceMaterial(instanceId);
        bizInstanceSituationVO.setMaterialVOList(instanceMaterial);
        return ResponseData.success("保存成功", bizInstanceSituationVO);
    }

    /**
     * 递归保存多层树结构。
     *
     * @param confMatterAcceptQuotas 需要保存的多层树结构
     */
    @Transactional()
    public void recursiveSave(List<BizInstanceQuota> confMatterAcceptQuotas, String id, String instance) {

        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
            for (BizInstanceQuota quota : confMatterAcceptQuotas) {
                // 保存当前节点
                BizInstanceQuota confMatterAcceptQuota = bizInstanceQuotaService.create(quota);
                confMatterAcceptQuota.setOptionId(id);
                confMatterAcceptQuota.setInstanceId(instance);
                List<BizInstanceOption> options = confMatterAcceptQuota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    for (BizInstanceOption option : options) {
                        option.setQuotaId(confMatterAcceptQuota.getId());
                        BizInstanceOption confMatterAcceptOption = bizInstanceOptionService.create(option);
                        // 递归保存子节点
                        recursiveSave(confMatterAcceptOption.getQuotas(), confMatterAcceptOption.getId(), null);
                    }
                }

            }
        }
    }

    @Override
    public ResponseData<List<ConfMatterAcceptQuotaVO>> get(String instanceId) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        String matterPublicId = bizInstanceInfo.getMatterPublicId();
        List<ConfMatterAcceptQuotaVO> voList = getMatterPublicId(instanceId, matterPublicId);
        return ResponseData.success(voList);
    }

    @Override
    public ResponseData<List<ConfMatterAcceptQuotaVO>> getByMatterId(String matterId) {
        ConfMatterPublic confMatterPublic = confMatterPublicService.getLatestConfMatterPublic(matterId);
        List<ConfMatterAcceptQuotaVO> voList = getMatterPublicId(null, confMatterPublic.getId());
        return ResponseData.success(voList);
    }

    /**
     * 获取情形列表
     *
     * @param instanceId
     * @param matterPublicId
     * @return
     */
    private List<ConfMatterAcceptQuotaVO> getMatterPublicId(String instanceId, String matterPublicId) {
        ConfMatterPublic confMatterPublic = confMatterPublicService.queryById(matterPublicId);
        List<ConfMatterAcceptQuotaVO> voList = JSON.parseArray(confMatterPublic.getSituationJson(), ConfMatterAcceptQuotaVO.class);
        List<ConfMatterAcceptQuotaVO> list = new ArrayList<>();
        recursive(voList, list);
        Map<String, ConfMatterAcceptQuotaVO> map = list.stream()
                .collect(Collectors.toMap(
                        ConfMatterAcceptQuotaVO::getCode, // key
                        vo -> vo, // value, 如果希望值保持原样，则可以简化为第二个参数vo -> vo
                        (existing, replacement) -> existing)); // 合并函数，当有重复key时决定保留哪个值，这里设置为保留第一次出现的值


        //绑定指标map
        ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash = Multimaps.newListMultimap(
                new HashMap<>(), // 使用 HashMap 作为底层存储
                ArrayList::new // 使用 ArrayList 作为值的容器
        );
        recursiveQuery(list, hash);
        recur(voList, hash, map);
        sort(voList);

        if (CollUtil.isEmpty(voList)) {
            return voList;
        }

        //<editor-fold desc="脚本-指标绑定">
        try {
            // 拿到脚本指标绑定脚本id
            ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = voList.get(0);
            if (confMatterAcceptQuotaVO.getIsBindScript() == null || !confMatterAcceptQuotaVO.getIsBindScript()
                    || StringUtils.isEmpty(instanceId)) {
                return voList;
            }
            String quotaBindScriptId = confMatterAcceptQuotaVO.getScripts() == null ? "" : confMatterAcceptQuotaVO.getScripts();
            String[] quotaBindScriptIds = quotaBindScriptId.split(",");
            // 执行脚本
            QuotaBindInput quotaBindInput = new QuotaBindInput();
            quotaBindInput.setInstanceId(instanceId);
            quotaBindInput.setQuotaVoList(JSON.toJSONString(voList));
            QuotaBindOutput quotaBindOutput = null;
            for (String scriptId : quotaBindScriptIds) {
                quotaBindOutput = scriptRunnerService.run(scriptId
                        , ScriptConstants.ScriptType.QUOTA_BIND.getMethod()
                        , QuotaBindInput.class
                        , quotaBindInput);
                if (quotaBindOutput == null) {
                    continue;
                }
                quotaBindInput.setQuotaVoList(quotaBindOutput.getQuotaVoList());
            }
            // 处理默认勾选部分情形
            if (quotaBindOutput != null && StringUtils.isNotEmpty(quotaBindOutput.getQuotaVoList())) {
                voList = JSON.parseArray(quotaBindOutput.getQuotaVoList(), ConfMatterAcceptQuotaVO.class);
            }
        } catch (Exception e) {
        }
        //</editor-fold>

        return BeanUtil.copyToList(voList, ConfMatterAcceptQuotaVO.class);
    }

    public void sort(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas) {

        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
            List<ConfMatterAcceptQuotaVO> quotaVOS = confMatterAcceptQuotas.stream().sorted(Comparator.comparing(ConfMatterAcceptQuotaVO::getSort))
                    .toList();
            for (ConfMatterAcceptQuotaVO quota : quotaVOS) {
                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    List<ConfMatterAcceptOptionVO> collect = options.stream().sorted(Comparator.comparing(ConfMatterAcceptOptionVO::getSort))
                            .toList();
                    for (ConfMatterAcceptOptionVO option : collect) {
                        // 递归
                        sort(option.getQuotas());
                    }
                }

            }
        }
    }

    public void recur(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash, Map<String, ConfMatterAcceptQuotaVO> map) {

        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
                String code = quota.getCode();
                ListMultimap<String, String> multimap = Multimaps.newListMultimap(
                        new HashMap<>(), // 使用 HashMap 作为底层存储
                        ArrayList::new // 使用 ArrayList 作为值的容器
                );
                List<ConfMatterAcceptOptionBindingVO> confMatterAcceptOptionBindingVOS = hash.get(code);
                if (CollectionUtil.isNotEmpty(confMatterAcceptOptionBindingVOS)) {
                    for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : confMatterAcceptOptionBindingVOS) {
                        multimap.put(confMatterAcceptOptionBindingVO.getBindingType(), confMatterAcceptOptionBindingVO.getOptionId());
                    }
                }
                List<String> addOnly = multimap.get(BindingStatusEnum.ADDONLY.getName());
                List<String> addAll = multimap.get(BindingStatusEnum.ADDALL.getName());
                List<String> removeOnly = multimap.get(BindingStatusEnum.REMOVEONLY.getName());
                List<String> removeAll = multimap.get(BindingStatusEnum.REMOVEALL.getName());

                quota.setBindOptionId(addOnly);
                quota.setBindOptionIds(addAll);
                quota.setBindExcludeOptionId(removeOnly);
                quota.setBindExcludeOptionIds(removeAll);
                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    for (ConfMatterAcceptOptionVO option : options) {
                        // 递归
                        List<ConfMatterAcceptQuotaVO> quotas = option.getQuotas();
                        List<ConfMatterAcceptQuotaVO> quo = new ArrayList<>();
                        recur(quotas, hash, map);
                        List<ConfMatterAcceptOptionBindingVO> binding = option.getBinding();
                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : binding) {
                            if ("指标".equals(confMatterAcceptOptionBindingVO.getType())) {
                                ConfMatterAcceptQuotaVO confMatterAcceptQuotaVO = map.get(confMatterAcceptOptionBindingVO.getQuotaCode());
                                if (CollectionUtil.isNotEmpty(quotas)) {
                                    quotas.add(confMatterAcceptQuotaVO);
                                    option.setQuotas(quotas);
                                } else {
                                    quo.add(confMatterAcceptQuotaVO);
                                    option.setQuotas(quo);
                                }
                            }
                        }

                    }
                }

            }
        }
    }

    public void recursiveQuery(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, ListMultimap<String, ConfMatterAcceptOptionBindingVO> hash) {

        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    for (ConfMatterAcceptOptionVO option : options) {
                        List<ConfMatterAcceptOptionBindingVO> binding = option.getBinding();
                        for (ConfMatterAcceptOptionBindingVO confMatterAcceptOptionBindingVO : binding) {
                            if (BindingTypeEnum.QUOTA.getName().equals(confMatterAcceptOptionBindingVO.getType())) {
                                hash.put(confMatterAcceptOptionBindingVO.getQuotaCode(), confMatterAcceptOptionBindingVO);
                            }
                        }

                        // 递归
                        recursiveQuery(option.getQuotas(), hash);
                    }
                }

            }
        }
    }

    public void recursive(List<ConfMatterAcceptQuotaVO> confMatterAcceptQuotas, List<ConfMatterAcceptQuotaVO> confMatterAcceptQuota) {

        if (CollectionUtil.isNotEmpty(confMatterAcceptQuotas)) {
            for (ConfMatterAcceptQuotaVO quota : confMatterAcceptQuotas) {
                confMatterAcceptQuota.add(quota);
                List<ConfMatterAcceptOptionVO> options = quota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    for (ConfMatterAcceptOptionVO option : options) {
                        // 递归
                        recursive(option.getQuotas(), confMatterAcceptQuota);
                    }
                }

            }
        }
    }

    public void recursiveList(List<BizInstanceQuota> bizInstanceQuotaList, List<BizInstanceOption> optionList) {
        if (CollectionUtil.isNotEmpty(bizInstanceQuotaList)) {
            for (BizInstanceQuota quota : bizInstanceQuotaList) {
                List<BizInstanceOption> options = quota.getOptions();
                if (CollectionUtil.isNotEmpty(options)) {
                    optionList.addAll(options);
                    for (BizInstanceOption option : options) {
                        // 递归
                        recursiveList(option.getQuotas(), optionList);
                    }
                }

            }
        }
    }

    /**
     * 过滤最终的增加和删除结果
     *
     * @param instanceId
     * @param addIds
     * @param delIds
     * @param allMap
     * @param selectedMap
     * @param name
     */
    private void filterFinalAdditionDelAndAddResults(String instanceId
            , List<String> addIds, List<String> delIds
            , ListMultimap<String, String> allMap, ListMultimap<String, String> selectedMap
            , String name) {
        selectedMap.asMap().forEach((key, value) -> {
            List<String> allTypeList = new ArrayList<>(allMap.get(key));
            List<String> selectedTypeList = new ArrayList<>(value);
            // 增加（全部满足）
            long allCount = allTypeList.stream().filter(v -> BindingStatusEnum.ADDALL.getName().equals(v)).count();
            long selectedCount = selectedTypeList.stream().filter(v -> BindingStatusEnum.ADDALL.getName().equals(v)).count();
            if (allCount != 0 && selectedCount >= allCount) {
                addIds.add(key);
            }
            // 移除（全部满足）
            allCount = allTypeList.stream().filter(v -> BindingStatusEnum.REMOVEALL.getName().equals(v)).count();
            selectedCount = selectedTypeList.stream().filter(v -> BindingStatusEnum.REMOVEALL.getName().equals(v)).count();
            if (allCount != 0 && selectedCount >= allCount) {
                delIds.add(key);
            }
            // 增加（任一满足）
            if (selectedTypeList.stream().anyMatch(element -> element.equals(BindingStatusEnum.ADDONLY.getName()))) {
                addIds.add(key);
            }
            // 移除（任一满足）
            if (selectedTypeList.stream().anyMatch(element -> element.equals(BindingStatusEnum.REMOVEONLY.getName()))) {
                delIds.add(key);
            }
        });
        // 打印日志
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("过滤对象", name);
        jsonObject.put("instanceId", instanceId);
        jsonObject.put("allMap", allMap.asMap());
        jsonObject.put("selectedMap", selectedMap.asMap());
        jsonObject.put("addIds", addIds);
        jsonObject.put("delIds", delIds);
        log.info("生成一次告知-过滤最终的增加和删除结果：{}", jsonObject.toJSONString(JSONWriter.Feature.PrettyFormat));
    }

    /**
     * 注册事务提交同步回调
     *
     * @param instanceId
     * @param materialAddIds
     * @param materialDelIds
     * @param fieldAddIds
     * @param fieldDelIds
     * @param documentAddIds
     * @param documentDelIds
     * @param matterAddIds
     * @param matterDelIds
     * @param netWorkWindowAddIds
     * @param netWorkWindowDelIds
     */
    private void registerSynchronization(String instanceId, List<String> materialAddIds, List<String> materialDelIds
            , List<String> fieldAddIds, List<String> fieldDelIds, List<String> documentAddIds, List<String> documentDelIds
            , List<String> matterAddIds, List<String> matterDelIds, List<String> netWorkWindowAddIds, List<String> netWorkWindowDelIds) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            /**
             * 事务提交后执行
             */
            @Override
            public void afterCommit() {
                /**
                 * 初始化各模块数据
                 * 阻塞多个线程，释放条件是有多个线程完成了任务
                 * 用countDownLatch.await();阻塞某些线程，直到有多个线程调用countDownLatch.countDown();
                 */
                CountDownLatch countDownLatch = new CountDownLatch(4);
                long startTime = System.currentTimeMillis();

                // 初始化instance对象
                initBizInstanceObject(countDownLatch, instanceId, materialAddIds, materialDelIds
                        , fieldAddIds, fieldDelIds, documentAddIds, documentDelIds, matterAddIds, matterDelIds);

                try {
                    // 阻塞
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    // 重新设置中断标志
                    Thread.currentThread().interrupt();
                    log.error("情形初始化线程被中断", e);
                    throw new BusinessException("情形初始化线程被中断", e);
                }

                // 因为afterCommit之后主线程事务已经释放，所以接下来任务需要异步执行才能开启事务，否则对数据库的修改无效
                Future<?> future = executor.submit(() -> {
                    // 初始化网点
                    bizInstanceNetWorkWindowService.initInstanceNetWorkWindow(instanceId, String.join(",", netWorkWindowAddIds), String.join(",", netWorkWindowDelIds));
                    // 将材料电子证照提取的字段合并到BizInstanceFields
                    BizInstanceFields instanceFields = bizInstanceFieldsService.queryForSingle(MapUtil.<String, Object>builder().put("=(instance.id)", instanceId).build());
                    bizInstanceFieldsService.mergeMaterialsFields(instanceId, instanceFields);
                });
                try {
                    future.get();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }

                long endTime = System.currentTimeMillis();
                double durationSeconds = (endTime - startTime) / 1000.0;
                log.info("{} 执行耗时: {} s", "情形选择初始化instance对象", durationSeconds);
            }
        });
    }


    /**
     * 初始化instance对象
     * 异步执行的初始化对象方法中不要出现锁表操作
     *
     * @param countDownLatch
     * @param instanceId
     * @param materialAddIds
     * @param materialDelIds
     * @param fieldAddIds
     * @param fieldDelIds
     * @param documentAddIds
     * @param documentDelIds
     * @param matterAddIds
     * @param matterDelIds
     */
    private void initBizInstanceObject(CountDownLatch countDownLatch, String instanceId
            , List<String> materialAddIds, List<String> materialDelIds
            , List<String> fieldAddIds, List<String> fieldDelIds
            , List<String> documentAddIds, List<String> documentDelIds
            , List<String> matterAddIds, List<String> matterDelIds) {
        executor.execute(() -> {
            try {
                bizInstanceMaterialService.initInstanceMaterial(instanceId, String.join(",", materialAddIds), String.join(",", materialDelIds));
            } catch (Exception e) {
                log.error("初始化instance对象失败", e);
                throw new BusinessException(e);
            } finally {
                countDownLatch.countDown();
            }
        });
        executor.execute(() -> {
            try {
                bizInstanceFieldsService.initBizInstanceFields(instanceId, String.join(",", fieldAddIds), String.join(",", fieldDelIds));
            } catch (Exception e) {
                log.error("初始化instance对象失败", e);
                throw new BusinessException(e);
            } finally {
                countDownLatch.countDown();
            }
        });
        executor.execute(() -> {
            try {
                bizInstanceDocumentService.initInstanceDocument(instanceId, String.join(",", documentAddIds), String.join(",", documentDelIds));
            } catch (Exception e) {
                log.error("初始化instance对象失败", e);
                throw new BusinessException(e);
            } finally {
                countDownLatch.countDown();
            }
        });
        executor.execute(() -> {
            try {
                bizInstanceResultService.initInstanceResult(instanceId, String.join(",", matterAddIds), String.join(",", matterDelIds));
            } catch (Exception e) {
                log.error("初始化instance对象失败", e);
                throw new BusinessException(e);
            } finally {
                countDownLatch.countDown();
            }
        });
    }
}
