package com.workplat.accept.business.house.dto;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;

@Data
@Builder
public class ThirdPartyHouseDTO {
    private String fwqy;      // 房屋区域
    private String htbh;      // 合同编号
    private String cmf;       // 出卖方
    private String xmmc;      // 项目名称
    private String fwyt;      // 房屋用途
    private String fwzmj;     // 房屋面积
    private LocalDateTime basj; // 备案时间
    private String msrxm;     // 买受人姓名
    private String msrzjh;    // 买受人证件号
    private String houseNo;   // 房号
    private String roomNo;    // 房间号
    private String address;   // 地址
} 